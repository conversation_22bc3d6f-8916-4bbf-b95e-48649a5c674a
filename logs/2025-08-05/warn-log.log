2025-08-05 15:27:58.672 [main] WARN  io.netty.resolver.dns.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS.
2025-08-05 15:28:03.996 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 15:28:04.217 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-08-05 15:28:06.140 [main] WARN  com.zaxxer.hikari.HikariConfig - DatebookHikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05 15:28:06.152 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
2025-08-05 15:28:14.583 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytPermissionAccount",So @TableField annotation will not work!
2025-08-05 15:28:14.627 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytPermissionResource",So @TableField annotation will not work!
2025-08-05 15:28:14.644 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytPermissionResourceRole",So @TableField annotation will not work!
2025-08-05 15:28:14.818 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.portal.model.SytSysOrganizationUser".
2025-08-05 15:28:14.818 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.portal.model.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-05 15:28:14.837 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmCategory",So @TableField annotation will not work!
2025-08-05 15:28:14.856 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmForm",So @TableField annotation will not work!
2025-08-05 15:28:14.875 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmProcessDefinitionExt",So @TableField annotation will not work!
2025-08-05 15:28:14.896 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmProcessInstanceExt",So @TableField annotation will not work!
2025-08-05 15:28:14.911 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmTaskAssignRule",So @TableField annotation will not work!
2025-08-05 15:28:14.931 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.publicres.model.SytResourApply",So @TableField annotation will not work!
2025-08-05 15:28:15.728 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytAddressBook",So @TableField annotation will not work!
2025-08-05 15:28:15.823 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytApplet",So @TableField annotation will not work!
2025-08-05 15:28:16.897 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.sanyth.portal.model.SytElectronicSeal",So @TableField will not work!
2025-08-05 15:28:16.979 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytFileResource",So @TableField annotation will not work!
2025-08-05 15:28:17.064 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytHallStatistics",So @TableField annotation will not work!
2025-08-05 15:28:17.151 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytLuckyDrawAwards",So @TableField annotation will not work!
2025-08-05 15:28:17.228 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytLuckyDrawParticipator",So @TableField annotation will not work!
2025-08-05 15:28:17.523 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytLuckyDraw",So @TableField annotation will not work!
2025-08-05 15:28:17.611 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytMessage",So @TableField annotation will not work!
2025-08-05 15:28:17.815 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytMessageSend",So @TableField annotation will not work!
2025-08-05 15:28:17.959 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytMobileNavigation",So @TableField annotation will not work!
2025-08-05 15:28:18.649 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytStatisticsBlock",So @TableField annotation will not work!
2025-08-05 15:28:18.750 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytStatisticsTab",So @TableField annotation will not work!
2025-08-05 15:28:21.391 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytZxzxRelpy",So @TableField annotation will not work!
2025-08-05 15:28:21.415 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytZxzxQuestion",So @TableField annotation will not work!
2025-08-05 15:28:21.506 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytZxzxZxflb",So @TableField annotation will not work!
2025-08-05 15:28:22.751 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmTaskExt",So @TableField annotation will not work!
2025-08-05 15:28:22.783 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmCommentTemplate",So @TableField annotation will not work!
2025-08-05 15:28:33.576 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmFormField",So @TableField annotation will not work!
2025-08-05 15:28:33.880 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmModelExt",So @TableField annotation will not work!
2025-08-05 15:28:34.856 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.sanyth.portal.bpm.model.BpmFormVariable",So @TableField will not work!
2025-08-05 15:28:34.973 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmCustomColumn",So @TableField annotation will not work!
