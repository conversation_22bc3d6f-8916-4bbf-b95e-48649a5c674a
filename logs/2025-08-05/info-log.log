2025-08-05 15:27:53.131 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-05 15:27:53.478 [main] INFO  com.sanyth.portal.Main - Starting Main using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 22385 (/Users/<USER>/WorkSpace/sanyth-portal/git-sanyth-portal/portal_web/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-portal)
2025-08-05 15:27:53.479 [main] INFO  com.sanyth.portal.Main - The following 1 profile is active: "loc"
2025-08-05 15:27:55.957 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 15:27:55.960 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-05 15:27:56.250 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository, org.springframework.data.elasticsearch.repository.ElasticsearchRepository.
2025-08-05 15:27:56.298 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 323 ms. Found 2 Elasticsearch repository interfaces.
2025-08-05 15:27:56.307 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 15:27:56.310 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-05 15:27:56.349 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository.
2025-08-05 15:27:56.350 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-05 15:27:56.360 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 15:27:56.361 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05 15:27:56.392 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-08-05 15:27:56.394 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-08-05 15:27:56.395 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-08-05 15:27:56.395 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 JPA repository interfaces.
2025-08-05 15:27:56.401 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 15:27:56.402 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-05 15:27:56.434 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-08-05 15:27:56.434 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-08-05 15:27:56.456 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 53 ms. Found 1 LDAP repository interfaces.
2025-08-05 15:27:56.465 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 15:27:56.465 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-05 15:27:56.495 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-05 15:27:56.496 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-05 15:27:56.496 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-05 15:27:56.496 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 MongoDB repository interfaces.
2025-08-05 15:27:56.520 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 15:27:56.522 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 15:27:56.562 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-05 15:27:56.562 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-05 15:27:56.563 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-05 15:27:56.563 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-08-05 15:27:57.288 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:27:57.306 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:27:57.340 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 15:27:57.943 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-08-05 15:27:57.976 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-08-05 15:27:57.976 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-05 15:27:57.976 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-05 15:27:58.171 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-05 15:27:58.172 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4599 ms
2025-08-05 15:27:58.604 [main] INFO  org.redisson.Version - Redisson 3.17.3
2025-08-05 15:28:01.358 [redisson-netty-2-31] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:6380
2025-08-05 15:28:03.050 [redisson-netty-2-19] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for *************/*************:6380
2025-08-05 15:28:03.740 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-05 15:28:04.191 [cluster-rtt-ClusterId{value='6891b283f03e4a527ab91152', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:21183}] to *************:13337
2025-08-05 15:28:04.191 [cluster-ClusterId{value='6891b283f03e4a527ab91152', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:21182}] to *************:13337
2025-08-05 15:28:04.192 [cluster-ClusterId{value='6891b283f03e4a527ab91152', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=152756291}
2025-08-05 15:28:04.436 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 15:28:04.436 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 15:28:04.437 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 15:28:05.135 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-05 15:28:05.410 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.33
2025-08-05 15:28:05.824 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-05 15:28:06.141 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-08-05 15:28:08.057 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-08-05 15:28:08.142 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.Oracle10gDialect
2025-08-05 15:28:12.749 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-05 15:28:12.816 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-05 15:28:20.960 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Spring Data Elasticsearch: 4.2.10
2025-08-05 15:28:20.961 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Elasticsearch Client in build: 7.12.1
2025-08-05 15:28:20.961 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Elasticsearch Client used: 7.12.1
2025-08-05 15:28:20.961 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Elasticsearch cluster: 7.12.1
2025-08-05 15:28:22.045 [main] INFO  org.flowable.spring.boot.eventregistry.EventRegistryAutoConfiguration - No deployment resources were found for autodeployment
2025-08-05 15:28:23.041 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Found 2 Engine Configurators in total:
2025-08-05 15:28:23.041 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
2025-08-05 15:28:23.041 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
2025-08-05 15:28:23.041 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
2025-08-05 15:28:23.048 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
2025-08-05 15:28:26.147 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing configure() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
2025-08-05 15:28:27.392 [main] INFO  liquibase.database - Could not set remarks reporting on OracleDatabase: com.sun.proxy.$Proxy231.setRemarksReporting(boolean)
2025-08-05 15:28:30.796 [main] INFO  liquibase.changelog - Reading from SYT_NEW_PORTAL.FLW_EV_DATABASECHANGELOG
2025-08-05 15:28:31.282 [main] INFO  org.flowable.eventregistry.impl.EventRegistryEngineImpl - EventRegistryEngine default created
2025-08-05 15:28:31.287 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing configure() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
2025-08-05 15:28:31.856 [main] INFO  org.flowable.idm.engine.impl.IdmEngineImpl - IdmEngine default created
2025-08-05 15:28:32.518 [main] INFO  org.flowable.engine.impl.ProcessEngineImpl - ProcessEngine default created
2025-08-05 15:28:32.984 [main] INFO  org.flowable.engine.impl.cmd.ValidateV5EntitiesCmd - Total of v5 deployments found: 0
2025-08-05 15:28:35.308 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
