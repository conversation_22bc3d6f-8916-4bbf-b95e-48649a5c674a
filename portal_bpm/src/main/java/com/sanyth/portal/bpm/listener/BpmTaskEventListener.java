package com.sanyth.portal.bpm.listener;

import com.google.common.collect.ImmutableSet;
import com.sanyth.portal.bpm.model.BpmTaskExt;
import com.sanyth.portal.bpm.service.BpmTaskService;
import com.sanyth.portal.bpm.service.IBpmCommentTemplateService;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEntityEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEventType;
import org.flowable.engine.delegate.event.AbstractFlowableEngineEventListener;
import org.flowable.engine.delegate.event.FlowableMultiInstanceActivityCompletedEvent;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 监听 {@link Task} 的开始与完成，创建与更新对应的 {@link BpmTaskExt} 记录
 *
 */
@Component
public class BpmTaskEventListener extends AbstractFlowableEngineEventListener {

    @Resource
    @Lazy // 解决循环依赖
    private BpmTaskService taskService;
    @Autowired
    private IBpmCommentTemplateService iBpmCommentTemplateService;

    public static final Set<FlowableEngineEventType> TASK_EVENTS = ImmutableSet.<FlowableEngineEventType>builder()
            .add(FlowableEngineEventType.TASK_CREATED)
            .add(FlowableEngineEventType.TASK_ASSIGNED)
            .add(FlowableEngineEventType.TASK_COMPLETED)
            .add(FlowableEngineEventType.MULTI_INSTANCE_ACTIVITY_COMPLETED)
            .add(FlowableEngineEventType.MULTI_INSTANCE_ACTIVITY_COMPLETED_WITH_CONDITION)
            .build();

    public BpmTaskEventListener(){
        super(TASK_EVENTS);
    }

    @Override
    protected void taskCreated(FlowableEngineEntityEvent event) {
        taskService.createTaskExt((Task) event.getEntity());
    }

    @Override
    protected void taskCompleted(FlowableEngineEntityEvent event) {
        taskService.updateTaskExtComplete((Task)event.getEntity());
        // 保存审批意见到个人模板
        iBpmCommentTemplateService.addMyComment(((Task)event.getEntity()).getId());
    }

    @Override
    protected void taskAssigned(FlowableEngineEntityEvent event) {
        taskService.updateTaskExtAssign((Task)event.getEntity());
        // 保存审批意见到个人模板
        iBpmCommentTemplateService.addMyComment(((Task)event.getEntity()).getId());
    }

    @Override
    protected void multiInstanceActivityCompleted(FlowableMultiInstanceActivityCompletedEvent event) {
        taskService.updateTaskExt(event);
    }
    @Override
    protected void multiInstanceActivityCompletedWithCondition(FlowableMultiInstanceActivityCompletedEvent event) {
        taskService.updateTaskExt(event);
    }

}
