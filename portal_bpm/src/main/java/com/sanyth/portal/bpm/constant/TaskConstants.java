package com.sanyth.portal.bpm.constant;

public class TaskConstants {

    /**
     * 流程发起人
     */
    public static final String PROCESS_INITIATOR = "initiator";

    /**
     * 会签
     */
    public static final String MULTI_INSTANCE = "multiInstance";

    /**
     * 会签任务总数
     */
    public static final String NUMBER_OF_INSTANCES = "nrOfInstances";

    /**
     * 正在执行的会签总数
     */
    public static final String NUMBER_OF_ACTIVE_INSTANCES = "nrOfActiveInstances";

    /**
     * 已完成的会签任务总数
     */
    public static final String NUMBER_OF_COMPLETED_INSTANCES = "nrOfCompletedInstances";

    /**
     * 循环的索引值，可以使用elementIndexVariable属性修改loopCounter的变量名
     */
    public static final String LOOP_COUNTER = "loopCounter";

    /**
     * 角色候选组前缀
     */
    public static final String ROLE_GROUP_PREFIX = "ROLE";

    /**
     * 部门候选组前缀
     */
    public static final String DEPT_GROUP_PREFIX = "DEPT";
    /**
    * 部门领导
    */
    public static final String DATA_TYPE_DEPTHEAD = "DEPTHEAD";
    /**
    * 发起人部门领导
    */
    public static final String DATA_TYPE_HEAD = "HEAD";
    /**
     * 表单中所选部门的领导
     *
     */
    public static final String DATA_TYPE_SELECTDEPTHEAD = "SELECTDEPTHEAD";

    /**
     * 发起人组织关系
     */
    public static final String ATTRS_GROUP_PREFIX = "ATTRS";

    /**
     * 自定义组织关系
     */
    public static final String INITATTR_GROUP_PREFIX = "ATTR";
    /**
     * 流程流转变量，驳回
     */
    public static final String PROCESS_OUTCOME = "outcome";
    /**
     * 流程流转变量，通过数量
     */
    public static final String PROCESS_PASSCOUNT = "passCount";
    /**
     * 流程流转变量，不通过数量
     */
    public static final String PROCESS_FAILCOUNT = "failCount";

    /**
     * 手工选择下个环节审批人开关
     */
    public static final String TASK_CHANGE_NEXT_USER = "changeuser";
    /**
     * 下个环节参与手工设置审批开关
     */
    public static final String TASK_IS_CHANGE_NEXT_USER = "ischangeuser";
}
