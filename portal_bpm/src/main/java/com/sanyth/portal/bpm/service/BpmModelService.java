package com.sanyth.portal.bpm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.bpm.vo.BpmModelPageItemRespVO;
import com.sanyth.portal.bpm.vo.BpmModelPageReqVO;
import com.sanyth.portal.bpm.vo.BpmModelRespVO;
import com.sanyth.portal.bpm.vo.BpmModelUpdateReqVO;
import org.flowable.bpmn.model.*;

import javax.validation.Valid;
import java.util.List;

/**
 * Flowable流程模型接口
 *
 */
public interface BpmModelService {
    /**
     * 获得流程模型分页
     *
     * @param pageVO 分页查询
     * @return 流程模型分页
     */
    Page<BpmModelPageItemRespVO> getModelPage(BpmModelPageReqVO pageVO);

    /**
     * 创建流程模型
     *
     * @param modelVO 创建信息
     * @param bpmnXml BPMN XML
     * @return 创建的流程模型的编号
     */
    String createModel(@Valid BpmModelUpdateReqVO modelVO);

    /**
     * 获得流程模块
     *
     * @param id 编号
     * @return 流程模型
     */
    BpmModelRespVO getModel(String id);

    /**
     * 修改流程模型
     *
     * @param updateReqVO 更新信息
     */
    void updateModel(@Valid BpmModelUpdateReqVO updateReqVO);

    /**
     * 将流程模型，部署成一个流程定义
     *
     * @param id 编号
     */
    void deployModel(String id);

    /**
     * 删除模型
     *
     * @param id 编号
     */
    void deleteModel(List<String> id);

    /**
     * 修改模型的状态，实际更新的部署的流程定义的状态
     *
     * @param id 编号
     * @param state 状态
     */
    void updateModelState(String id, Integer state);

    /**
     * 获得流程模型编号对应的 BPMN Model
     *
     * @param id 流程模型编号
     * @return BPMN Model
     */
    BpmnModel getBpmnModel(String id);

    /**
     * 通过流程定义id获取BpmnModel
     *
     * @param processDefId 流程定义id
     * @return
     */
    public BpmnModel getBpmnModelByProcessDefId(String processDefId);

    /**
     * 通过流程定义id获取所有的节点
     *
     * @param processDefId 流程定义id
     * @return
     */
    public List<FlowNode> findFlowNodes(String processDefId);

    /**
     * 获取end节点
     *
     * @param processDefId 流程定义id
     * @return FlowElement
     */
    public List<EndEvent> findEndFlowElement(String processDefId);

    /**
     * 判断节点是不是子流程的节点
     * @param processDefId 流程定义id
     * @param activityId 节点id
     * @return
     */
    public boolean checkActivitySubprocessByActivityId(String processDefId, String activityId);
    /**
     * 通过流程id获取节点
     *
     * @param processDefId 流程定义id
     * @param activityId   节点id
     * @return
     */
    public List<Activity> findActivityByActivityId(String processDefId, String activityId);

    /**
     * 通过流程id获取主流程中的节点
     *
     * @param processDefId 流程定义id
     * @param activityId   节点id
     * @param processDefId
     * @param activityId
     * @return
     */
    public FlowNode findMainProcessActivityByActivityId(String processDefId, String activityId);

    /**
     * 查找节点
     * @param processDefId 流程定义id
     * @param activityId 节点id
     * @return
     */
    public FlowNode findFlowNodeByActivityId(String processDefId, String activityId) ;

    /**
     * 通过名称获取节点
     *
     * @param processDefId 流程定义id
     * @param name         节点名称
     * @return
     */
    public Activity findActivityByName(String processDefId, String name);

    /**
     * 获取start节点
     *
     * @param processDefId 流程定义id
     * @return FlowElement
     */
    public List<StartEvent> findStartFlowElement(String processDefId);

    /**
     * 校验流程标识key
     * @param createRetVO
     * @return java.lang.String
     */
    void checkKey(BpmModelUpdateReqVO createRetVO);

}
