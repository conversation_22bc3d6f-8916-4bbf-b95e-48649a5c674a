package com.sanyth.portal.bpm.listener;

import com.sanyth.portal.bpm.service.BpmTaskService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 自动审批 事件监听(执行监听)
 *
 * <AUTHOR>
 * @date 2022/07/05
 */
@Component
public class AutoApproveExecutionListener implements ExecutionListener {

    @Resource
    @Lazy
    private BpmTaskService bpmTaskService;

    @Override
    public void notify(DelegateExecution delegateExecution) {
//        System.out.println("payne----进入事件(执行)监听器自动审批");
        //TODO 自动审批暂时实现通过功能，后期扩展按配置审批，拒绝等操作
//        BpmTaskService bpmTaskService = SpringUtils.getObject(BpmTaskService.class);
        bpmTaskService.approveTaskByListener(delegateExecution);
    }

}
