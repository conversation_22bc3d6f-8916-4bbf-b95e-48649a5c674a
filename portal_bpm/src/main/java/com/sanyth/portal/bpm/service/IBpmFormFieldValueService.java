package com.sanyth.portal.bpm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmFormFieldValue;
import com.sanyth.portal.core.base.BaseQuery;

import java.util.List;
import java.util.Map;

/**
 * 用户填写的表单字段值 服务接口
 * Created by WDL on 2022-12-30.
 */
public interface IBpmFormFieldValueService extends IService<BpmFormFieldValue> {
        Page<BpmFormFieldValue> queryPage(BaseQuery query);

        List<BpmFormFieldValue> querList(BpmFormFieldValue entity);

        /**
         * 保存数据
         * @param humanCode
         * @param formvariables
         * @param recordId
         * @param projectId
         */
        void saveFormFieldValueData(String humanCode, Map<String, Object> formvariables, String recordId, String projectId);

        /**
         * 根据填写记录ID删除数据
         * @param recordIds
         */
        void deleteByRecordIds(String recordIds);
}
