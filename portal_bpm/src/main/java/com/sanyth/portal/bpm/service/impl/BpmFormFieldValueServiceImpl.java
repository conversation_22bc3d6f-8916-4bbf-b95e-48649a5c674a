package com.sanyth.portal.bpm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.bpm.mapper.BpmFormFieldValueMapper;
import com.sanyth.portal.bpm.model.BpmFormFieldValue;
import com.sanyth.portal.bpm.service.IBpmFormFieldValueService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytPermissionAccount;
import com.sanyth.portal.service.ISytPermissionAccountService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户填写的表单字段值 服务实现
 * Created by WDL on 2022-12-30.
 */
@Service
public class BpmFormFieldValueServiceImpl extends ServiceImpl<BpmFormFieldValueMapper, BpmFormFieldValue> implements IBpmFormFieldValueService {

    @Resource
    private BpmFormFieldValueMapper bpmFormFieldValueMapper;
    @Resource
    private ISytPermissionAccountService iSytPermissionAccountService;

    @Override
    public Page<BpmFormFieldValue> queryPage(BaseQuery query) {
        Page<BpmFormFieldValue> page = new Page<>(query.getPage(), query.getPageSize());
        BpmFormFieldValue obj = JSON.toJavaObject(query.getQueryParam(), BpmFormFieldValue.class);
        Wrapper<BpmFormFieldValue> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public List<BpmFormFieldValue> querList(BpmFormFieldValue entity) {
        Wrapper<BpmFormFieldValue> wrapper = buildWrapper(entity);
        return list(wrapper);
    }

    @Override
    @Transactional(rollbackFor=Exception.class)
    public void saveFormFieldValueData(String humanCode, Map<String, Object> formvariables, String recordId, String projectId) {

        // 删除已有记录
        // this.deleteByRecordIds(recordId);
        // 不能直接删除已有记录，再添加，因为可能会有审批数据，或者有修改的数据
        // 保存数据
        if(formvariables != null && formvariables.size() > 0 && formvariables.get("mobilefields") != null){
            // 查询对应的用户信息
            SytPermissionAccount account = iSytPermissionAccountService.getByHumancode(humanCode);
            JSONObject form = (JSONObject) JSONObject.toJSON(formvariables);
            // JSONArray widgetList = (JSONArray) form.get("widgetList");   // 简单表单取此值可以；复杂的表单，需要使用mobilefields移动端的这个值
            JSONArray widgetList = (JSONArray) form.get("mobilefields");
            JSONObject fieldsValues = (JSONObject) JSONObject.parse(String.valueOf(form.get("fieldsValues")));
            // 查询已有数据
            List<BpmFormFieldValue> fieldValueList = this.querList(new BpmFormFieldValue().setProjectId(projectId).setRecordId(recordId));
            if(CollectionUtils.isNotEmpty(fieldValueList)){
                // 修改数据
                Map<String, BpmFormFieldValue> map = fieldValueList.stream().collect(Collectors.toMap(BpmFormFieldValue::getFieldEn, obj -> obj));
                // 新增的数据
                List<BpmFormFieldValue> addList = new ArrayList<>();
                // 修改的数据
                List<BpmFormFieldValue> updateList = new ArrayList<>();
                for (Object object : widgetList){
                    JSONObject widget = JSONObject.parseObject(String.valueOf(object));
                    JSONObject options = (JSONObject) widget.get("options");
                    String fieldEn = options.get("name") == null ? "" : options.get("name").toString();
                    String fieldZh = options.get("label") == null ? "" : options.get("label").toString();
                    Object valueObject = fieldsValues.get(fieldEn);
                    String value = valueObject == null ? "" : String.valueOf(valueObject);

                    if(map.get(fieldEn) != null){
                        BpmFormFieldValue formFieldValue = map.get(fieldEn);
                        formFieldValue.setValue(value);
                        updateList.add(formFieldValue);
                    } else {
                        BpmFormFieldValue bpmFormFieldValue = new BpmFormFieldValue().setFieldEn(fieldEn).setFieldZh(fieldZh).setValue(value)
                                .setHumanCode(account.getHumancode()).setHumanName(account.getHumanname())
                                .setOrganizationnames(account.getOrganizationnames())
                                .setRecordId(recordId).setProjectId(projectId);
                        addList.add(bpmFormFieldValue);
                    }
                }
                this.saveBatch(addList);
                this.updateBatchById(updateList);
            } else {
                // 新增数据
                List<BpmFormFieldValue> list = new ArrayList<>();
                for (Object object : widgetList){
                    JSONObject widget = JSONObject.parseObject(String.valueOf(object));
                    JSONObject options = (JSONObject) widget.get("options");
                    String fieldEn = options.get("name") == null ? "" : options.get("name").toString();
                    String fieldZh = options.get("label") == null ? "" : options.get("label").toString();
                    Object valueObject = fieldsValues.get(fieldEn);
                    String value = valueObject == null ? "" : String.valueOf(valueObject);

                    list.add(new BpmFormFieldValue().setFieldEn(fieldEn).setFieldZh(fieldZh).setValue(value)
                            .setHumanCode(account.getHumancode()).setHumanName(account.getHumanname())
                            .setOrganizationnames(account.getOrganizationnames())
                            .setRecordId(recordId).setProjectId(projectId));
                }
                this.saveBatch(list);
            }
        }
    }

    @Override
    public void deleteByRecordIds(String recordIds) {
        bpmFormFieldValueMapper.delete(new LambdaQueryWrapper<BpmFormFieldValue>().in(BpmFormFieldValue::getRecordId, recordIds.split(",")));
    }

    private Wrapper<BpmFormFieldValue> buildWrapper(BpmFormFieldValue entity){
        QueryWrapper<BpmFormFieldValue> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(entity.getFieldEn())) {
            wrapper.eq("FIELD_EN", entity.getFieldEn());
        }
        if (StringUtils.isNotBlank(entity.getRecordId())) {
            wrapper.eq("RECORD_ID", entity.getRecordId());
        }
        if (StringUtils.isNotBlank(entity.getProjectId())) {
            wrapper.eq("PROJECT_ID", entity.getProjectId());
        }
        if (StringUtils.isNotBlank(entity.getHumanCode())) {
            wrapper.eq("HUMAN_CODE", entity.getHumanCode());
        }
        if (StringUtils.isNotBlank(entity.getHumanName())) {
            wrapper.eq("HUMAN_NAME", entity.getHumanName());
        }

        return wrapper;
    }
}
