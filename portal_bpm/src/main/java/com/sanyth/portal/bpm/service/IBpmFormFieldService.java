package com.sanyth.portal.bpm.service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmFormField;
import com.sanyth.portal.core.base.BaseQuery;

import java.util.List;

/**
 * 表单对应的字段 服务接口
 * Created by WDL on 2022-12-23.
 */
public interface IBpmFormFieldService extends IService<BpmFormField> {

        Page<BpmFormField> queryPage(BaseQuery query);

        List<BpmFormField> queryList(BpmFormField entity);

        /**
         * 根据流程定义ID删除数据;
         * 多个值以英文逗号分隔
         * @param processDefinitionId
         */
        void deleteByDefId(String processDefinitionId);

        /**
         * 根据表单ID删除数据，且流程定义ID值为空的数据;
         * 多个值以英文逗号分隔
         * @param formId
         */
        void deleteByFormId(String formId);

        /**
         * 保存表单数据；
         * 参数definitionId、formId只能一个有值
         * @param formFields
         * @param definitionId
         * @param formId
         */
        void saveFormFieldData(List<String> formFields, String definitionId, String formId);
}
