package com.sanyth.portal.bpm.listener;

import cn.hutool.core.bean.BeanUtil;
import com.sanyth.portal.biz.dict.model.DictGroup;
import com.sanyth.portal.biz.dict.service.IDictGroupService;
import com.sanyth.portal.biz.user.model.SytAccountListInfo;
import com.sanyth.portal.biz.user.service.ISytAccountListInfoService;
import com.sanyth.portal.bpm.service.IBpmProcessInstanceExtService;
import com.sanyth.portal.bpm.vo.BpmProcessInstanceRespVO;
import com.sanyth.portal.model.SytPermissionAccount;
import com.sanyth.portal.service.ISytPermissionAccountService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.sanyth.portal.core.common.Constants.UPDATE_ACCONT_INFO_AGREE;


/**
 * 更新帐户信息任务侦听器
 * 代理表达式 实现TaskListener接口
 * <AUTHOR>
 * @date 2024/11/20
 */
@Component(value = "updateAccountInfoTaskListener")
public class UpdateAccountInfoTaskListener implements TaskListener {

    @Resource
    private ISytPermissionAccountService sytPermissionAccountService;
    @Resource
    private ISytAccountListInfoService sytAccountListInfoService;
    @Resource
    private IBpmProcessInstanceExtService bpmProcessInstanceExtService;
    @Resource
    private IDictGroupService dictGroupService;

    @Override
    public void notify(DelegateTask delegateTask) {
        System.out.println("进入更新帐户信息任务侦听器");
        BpmProcessInstanceRespVO processInstanceVO = bpmProcessInstanceExtService.getProcessInstanceVO(delegateTask.getProcessInstanceId());
        if (processInstanceVO != null) {
            BpmProcessInstanceRespVO.User startUser = processInstanceVO.getStartUser();
            SytPermissionAccount account = sytPermissionAccountService.getByHumancode(startUser.getHumanCode());

            Map<String, Object> formVariables = processInstanceVO.getFormVariables();
            Map<String, Object> formCustomViewData = (Map<String, Object>) formVariables.get("formCustomViewData");
            if (formCustomViewData != null) {
                //基本信息，多条信息修改
                Map<String, List<Map<String, Object>>> base = (Map<String, List<Map<String, Object>>>) formCustomViewData.get("baseList");
                Map<String, List<Map<String, Object>>> subset = (Map<String, List<Map<String, Object>>>) formCustomViewData.get("dtxxList");
                for (String s : base.keySet()) {
                    List<Map<String, Object>> objectList = base.get(s);
                    for (Map<String, Object> objectMap : objectList) {
                        String spzt = (String) objectMap.getOrDefault("spzt", "");
                        if (UPDATE_ACCONT_INFO_AGREE.equals(spzt)) {
                            String field = (String) objectMap.get("field");
                            String newValue = (String) objectMap.get("newValue");
                            BeanUtil.setFieldValue(account, field, newValue);
                            sytPermissionAccountService.updateById(account);
                        }
                    }
                }
                for (String s : subset.keySet()) {
                    List<Map<String, Object>> objectList = subset.get(s);
                    for (Map<String, Object> objectMap : objectList) {
                        String spzt = (String) objectMap.getOrDefault("spzt", "");
                        String id = (String) objectMap.getOrDefault("id", "");
                        //spzt = 同意
                        if (UPDATE_ACCONT_INFO_AGREE.equals(spzt)) {
                            String xgfs = (String) objectMap.getOrDefault("xgfs","");
                            switch (xgfs) {
                                case "add":
                                case "edit":
                                    SytAccountListInfo accountListInfo = BeanUtil.mapToBean(objectMap, SytAccountListInfo.class, true);
                                    DictGroup group = dictGroupService.getById(s);
                                    accountListInfo.setGroupId(s);
                                    accountListInfo.setGroupName(group.getName());
                                    accountListInfo.setHumancode(account.getHumancode());
                                    sytAccountListInfoService.saveOrUpdate(accountListInfo);
                                    break;
                                case "delete":
                                    sytAccountListInfoService.removeById(id);
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }

            }

        }
    }
}
