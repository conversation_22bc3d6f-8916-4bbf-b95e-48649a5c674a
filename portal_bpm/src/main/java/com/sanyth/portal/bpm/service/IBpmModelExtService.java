package com.sanyth.portal.bpm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmModelExt;
import com.sanyth.portal.core.base.BaseQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 流程模型扩展 服务接口
 * Created by ZWB on 2022-05-24.
 */
public interface IBpmModelExtService extends IService<BpmModelExt> {
    Page<BpmModelExt> queryPage(BaseQuery query);


    BpmModelExt getByModeId(String modelId);

    Map<String, BpmModelExt> getBpmModelExtMap(Collection<String> ids);

    List<BpmModelExt> getModelExtList(Collection<String> ids);
}
