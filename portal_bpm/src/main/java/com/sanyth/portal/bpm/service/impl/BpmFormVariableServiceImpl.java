package com.sanyth.portal.bpm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.bpm.mapper.BpmFormVariableMapper;
import com.sanyth.portal.bpm.model.BpmFormVariable;
import com.sanyth.portal.bpm.service.IBpmFormVariableService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytPermissionAccount;
import com.sanyth.portal.service.ISytPermissionAccountService;
import com.sanyth.portal.util.property.PropertyPlaceholderHelperUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 表单变量 服务实现
 * Created by ZWB on 2022-08-05.
 */
@Service
public class BpmFormVariableServiceImpl extends ServiceImpl<BpmFormVariableMapper, BpmFormVariable> implements IBpmFormVariableService {

    @Resource
    private ISytPermissionAccountService accountService;

    @Override
    public Page<BpmFormVariable> queryPage(BaseQuery query) {
        Page<BpmFormVariable> page = new Page<>(query.getPage(), query.getPageSize());
        BpmFormVariable obj = JSON.toJavaObject(query.getQueryParam(), BpmFormVariable.class);
        Wrapper<BpmFormVariable> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public List<BpmFormVariable> queryList(BpmFormVariable variable) {
        return list(buildWrapper(variable));
    }

    @Override
    public String formatVariable(String formFields,Map<String,Object> varMap) {
        Map<String, Object> map = new HashMap<>();
        List<String> stringList = PropertyPlaceholderHelperUtil.findParamKeys(formFields);
        stringList.forEach(str->{
            String[] split = str.split("\\.");
            Object value = "";
            switch (split[0]) {
                case "currentUser":
                    SytPermissionAccount account = accountService.getByHumancode(String.valueOf(varMap.get("humancode")));
                    if(account==null) break;
                    value = BeanUtil.getFieldValue(account, split[1]);
                    break;
                case "flow":
//                    value = Objects.isNull(varMap.get("reason")) ? "#{" + str + "}" : varMap.get("reason");//预处理转为#{},审批时再格式化为具体的意见
                    value = Objects.isNull(varMap.get(split[1])) ? "" : varMap.get(split[1]);
                    break;
                case "date":
                    String splitVal = split[1];
                    String[] strings = splitVal.split(",");
                    if (strings.length > 1) {
                        String type = strings[0];
                        String format = strings[1];
                        format = StringUtils.isEmpty(format) ? "yyyy-MM-dd" : format;
                        DateTime date = DateUtil.date();
                        switch (type) {
                            case "now":
                                value = DateUtil.format(date, format);
                                break;
                            case "yesterday":
                                value = DateUtil.format(DateUtil.yesterday(), format);
                                break;
                            case "tomorrow":
                                value = DateUtil.format(DateUtil.tomorrow(), format);
                                break;
                            case "lastWeek":
                                value = DateUtil.format(DateUtil.lastWeek(), format);
                                break;
                            case "nextWeek":
                                value = DateUtil.format(DateUtil.nextWeek(), format);
                                break;
                            case "lastMonth":
                                value = DateUtil.format(DateUtil.lastMonth(), format);
                                break;
                            case "nextMonth":
                                value = DateUtil.format(DateUtil.nextMonth(), format);
                                break;
                            default:
                                break;
                        }
                        if (type.contains("M")) {//月
                            String val = type.replace("M", "");
                            value = DateUtil.format(DateUtil.offsetMonth(date, Integer.parseInt(val)), format);
                        } else if (type.contains("W")) {//周
                            String val = type.replace("W", "");
                            value = DateUtil.format(DateUtil.offsetMonth(date, Integer.parseInt(val)), format);
                        } else if (type.contains("D")) {//天
                            String val = type.replace("D", "");
                            value = DateUtil.format(DateUtil.offsetMonth(date, Integer.parseInt(val)), format);
                        } else if (type.contains("H")) {//小时
                            String val = type.replace("H", "");
                            value = DateUtil.format(DateUtil.offsetMonth(date, Integer.parseInt(val)), format);
                        }
                    }
                case "publicRes":
                    // String valTmp = split[1];
                    // if(Objects.equals(valTmp, "applyDate")){
                    //     value = Objects.isNull(varMap.get("applyDate")) ? "#{" + str + "}" : varMap.get("applyDate");//预处理转为#{},发起资源预约时间格式化
                    // } else if(Objects.equals(valTmp, "applyReason")){
                    //     value = Objects.isNull(varMap.get("applyReason")) ? "#{" + str + "}" : varMap.get("applyReason");//预处理转为#{},发起资源预约申请理由格式化
                    // } else if(Objects.equals(valTmp, "resName")){
                    //     value = Objects.isNull(varMap.get("resName")) ? "#{" + str + "}" : varMap.get("resName");   //预处理转为#{},发起资源预约资源名称格式化
                    // }
                    break;
                default:
                    break;
            }
//            if (!Objects.isNull(value) && !Objects.equals(value, "")) {
                map.put(str, value);
//            }
        });
        return PropertyPlaceholderHelperUtil.placeholderHelper("${", "}", map, formFields);
    }

    private Wrapper<BpmFormVariable> buildWrapper(BpmFormVariable bpmFormVariable){
        QueryWrapper<BpmFormVariable> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(bpmFormVariable.getName())) {
            wrapper.like("NAME", bpmFormVariable.getName());
        }
        if (StringUtils.isNotBlank(bpmFormVariable.getStatus())) {
            wrapper.eq("STATUS", bpmFormVariable.getStatus());
        }
        wrapper.orderByAsc("VALUE");
        return wrapper;
    }
}
