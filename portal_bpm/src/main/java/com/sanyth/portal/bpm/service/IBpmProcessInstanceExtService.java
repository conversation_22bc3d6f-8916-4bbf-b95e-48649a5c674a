package com.sanyth.portal.bpm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmProcessDefinitionExt;
import com.sanyth.portal.bpm.model.BpmProcessInstanceExt;
import com.sanyth.portal.bpm.vo.*;
import com.sanyth.portal.util.collection.CollectionUtils;
import org.flowable.engine.delegate.event.FlowableCancelledEvent;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Bpm 流程实例的拓展表
 *
 * <AUTHOR>
 * @since 2022-05-16
 */
public interface IBpmProcessInstanceExtService extends IService<BpmProcessInstanceExt> {

    /**
     * 获得流程实例
     *
     * @param id 流程实例的编号
     * @return 流程实例
     */
    ProcessInstance getProcessInstance(String id);

    /**
     * 获得流程实例列表
     *
     * @param ids 流程实例的编号集合
     * @return 流程实例列表
     */
    List<ProcessInstance> getProcessInstances(Set<String> ids);
    List<BpmProcessInstanceExt> getBpmProcessInstanceExts(Set<String> ids);

    /**
     * 获得流程实例 Map
     *
     * @param ids 流程实例的编号集合
     * @return 流程实例列表 Map
     */
    default Map<String, ProcessInstance> getProcessInstanceMap(Set<String> ids) {
        return CollectionUtils.convertMap(getProcessInstances(ids), ProcessInstance::getProcessInstanceId);
    }

    /**
     * 获得流程实例扩展 Map
     *
     * @param ids id
     * @return {@link Map}<{@link String}, {@link BpmProcessInstanceExt}>
     */
    default Map<String, BpmProcessInstanceExt> getBpmProcessInstanceExtMap(Set<String> ids) {
        return CollectionUtils.convertMap(getBpmProcessInstanceExts(ids), BpmProcessInstanceExt::getProcessInstanceId);
    }

    /**
     * 获得流程实例的分页
     *
     * @param userId 用户编号
     * @param pageReqVO 分页请求
     * @return 流程实例的分页
     */
    Page<BpmProcessInstancePageItemRespVO> getMyProcessInstancePage(String userId,
                                                                    @Valid BpmProcessInstanceMyPageReqVO pageReqVO);
    Long getMyProcessInstanceCount(String userId, @Valid BpmProcessInstanceMyPageReqVO pageReqVO);
    /**
     * 创建流程实例（提供给前端）
     *
     * @param userId 用户编号
     * @param createReqVO 创建信息
     * @return 实例的编号
     */
    Map<String,Object> createProcessInstance(String userId, @Valid BpmProcessInstanceCreateReqVO createReqVO);

    /**
     * 创建流程实例（提供给内部）
     *
     * @param userId 用户编号
     * @param createReqDTO 创建信息
     * @return 实例的编号
     */
//    Map<String,Object> createProcessInstance(String userId, @Valid BpmProcessInstanceCreateReqDTO createReqDTO);

    /**
     * 获得流程实例 VO 信息
     *
     * @param id 流程实例的编号
     * @return 流程实例
     */
    BpmProcessInstanceRespVO getProcessInstanceVO(String id);

    BpmProcessInstanceExt getProcessInstanceVOByPxjl(String id);

//    BpmProcessInstanceRespVO getProcessInstanceVO(String id);

    /**
     * 撤销流程实例
     *
     * @param userId 用户编号
     * @param cancelReqVO 撤销信息
     */
    void cancelProcessInstance(String userId, @Valid BpmProcessInstanceCancelReqVO cancelReqVO);

    /**
     * 获得历史的流程实例
     *
     * @param id 流程实例的编号
     * @return 历史的流程实例
     */
    HistoricProcessInstance getHistoricProcessInstance(String id);

    /**
     * 获得历史的流程实例列表
     *
     * @param ids 流程实例的编号集合
     * @return 历史的流程实例列表
     */
    List<HistoricProcessInstance> getHistoricProcessInstances(Set<String> ids);

    /**
     * 获得历史的流程实例 Map
     *
     * @param ids 流程实例的编号集合
     * @return 历史的流程实例列表 Map
     */
    default Map<String, HistoricProcessInstance> getHistoricProcessInstanceMap(Set<String> ids) {
        return CollectionUtils.convertMap(getHistoricProcessInstances(ids), HistoricProcessInstance::getId);
    }

    /**
     * 创建 ProcessInstance 拓展记录
     *
     * @param instance 流程任务
     */
    void createProcessInstanceExt(ProcessInstance instance);

    /**
     * 更新 ProcessInstance 拓展记录为撤销
     *
     * @param event 流程取消事件
     */
    void updateProcessInstanceExtCancel(FlowableCancelledEvent event);

    /**
     * 更新 ProcessInstance 拓展记录为完成
     *
     * @param instance 流程任务
     */
    void updateProcessInstanceExtComplete(ProcessInstance instance);

    /**
     * 更新 ProcessInstance 拓展记录为不通过
     *
     * @param id 流程编号
     * @param reason 理由。例如说，审批不通过时，需要传递该值
     */
    void updateProcessInstanceExtReject(String id, String reason);

    /**
     * 删除流程
     * @param humanCode
     * @param cancelReqVO
     */
    void deleteProcessInstance(String humanCode, BpmProcessInstanceCancelReqVO cancelReqVO);

    /**
     * 删除流程示例扩展
     */
    void deleteProcessInstanceExt(BpmProcessInstanceExt bpmProcessInstanceExt);

    /**
     * 修改流程
     * @param humanCode
     * @param updateReqVO
     */
    Map<String, Object> updateProcessInstance(String humanCode, BpmProcessInstanceUpdateReqVO updateReqVO);

    /**
     * 判断流程实例是否已审批
     * @param processInstance
     */
    boolean checkApproved(ProcessInstance processInstance);

    /**
     * 判断当前流程标识，是否有已发起的流程实例
     */
    boolean hasProcessInstanceBykey(String key);
}
