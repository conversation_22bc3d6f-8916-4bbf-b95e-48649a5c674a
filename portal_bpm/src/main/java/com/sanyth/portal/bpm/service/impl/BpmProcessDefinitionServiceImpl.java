package com.sanyth.portal.bpm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.bpm.convert.BpmProcessDefinitionConvert;
import com.sanyth.portal.bpm.dto.BpmProcessDefinitionCreateReqDTO;
import com.sanyth.portal.bpm.mapper.BpmProcessDefinitionExtMapper;
import com.sanyth.portal.bpm.model.BpmForm;
import com.sanyth.portal.bpm.model.BpmProcessDefinitionExt;
import com.sanyth.portal.bpm.service.BpmModelService;
import com.sanyth.portal.bpm.service.BpmProcessDefinitionService;
import com.sanyth.portal.bpm.service.IBpmFormService;
import com.sanyth.portal.bpm.util.FlowableUtils;
import com.sanyth.portal.bpm.util.PageUtils;
import com.sanyth.portal.bpm.util.ServiceExceptionUtil;
import com.sanyth.portal.bpm.vo.BpmProcessDefinitionListReqVO;
import com.sanyth.portal.bpm.vo.BpmProcessDefinitionPageItemRespVO;
import com.sanyth.portal.bpm.vo.BpmProcessDefinitionPageReqVO;
import com.sanyth.portal.bpm.vo.BpmProcessDefinitionRespVO;
import com.sanyth.portal.core.common.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.Process;
import org.flowable.common.engine.impl.db.SuspensionState;
import org.flowable.common.engine.impl.util.io.BytesStreamSource;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

import static com.sanyth.portal.bpm.enums.ErrorCodeConstants.PROCESS_DEFINITION_KEY_NOT_MATCH;
import static com.sanyth.portal.bpm.enums.ErrorCodeConstants.PROCESS_DEFINITION_NAME_NOT_MATCH;
import static com.sanyth.portal.util.collection.CollectionUtils.*;
import static java.util.Collections.emptyList;

/**
 * 流程定义实现
 * 主要进行 Flowable {@link ProcessDefinition} 和 {@link Deployment} 的维护
 *
 */
@Service
@Slf4j
public class BpmProcessDefinitionServiceImpl implements BpmProcessDefinitionService {

    private static final String BPMN_FILE_SUFFIX = ".bpmn";

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private BpmProcessDefinitionExtMapper processDefinitionMapper;

    @Resource
    private IBpmFormService formService;
    @Resource
    private BpmModelService modelService;

    @Override
    public ProcessDefinition getProcessDefinition(String id) {
        return repositoryService.getProcessDefinition(id);
    }

    @Override
    public ProcessDefinition getProcessDefinition2(String id) {
        return repositoryService.createProcessDefinitionQuery().processDefinitionId(id).singleResult();
    }

    @Override
    public ProcessDefinition getProcessDefinitionByDeploymentId(String deploymentId) {
        if (StrUtil.isEmpty(deploymentId)) {
            return null;
        }
        return repositoryService.createProcessDefinitionQuery().deploymentId(deploymentId).singleResult();
    }

    @Override
    public List<ProcessDefinition> getProcessDefinitionListByDeploymentIds(Set<String> deploymentIds) {
        if (CollUtil.isEmpty(deploymentIds)) {
            return emptyList();
        }
        return repositoryService.createProcessDefinitionQuery().deploymentIds(deploymentIds).list();
    }

    @Override
    public ProcessDefinition getActiveProcessDefinition(String key) {
        return repositoryService.createProcessDefinitionQuery().processDefinitionKey(key).active().singleResult();
    }

    @Override
    public List<Deployment> getDeployments(Set<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return emptyList();
        }
        List<Deployment> list = new ArrayList<>(ids.size());
        for (String id : ids) {
            addIfNotNull(list, getDeployment(id));
        }
        return list;
    }

    @Override
    public Deployment getDeployment(String id) {
        if (StrUtil.isEmpty(id)) {
            return null;
        }
        return repositoryService.createDeploymentQuery().deploymentId(id).singleResult();
    }

    @Override
    public BpmnModel getBpmnModel(String processDefinitionId) {
        return repositoryService.getBpmnModel(processDefinitionId);
    }

    @Override
    public String createProcessDefinition(@Valid BpmProcessDefinitionCreateReqDTO createReqDTO) {
        // 创建 Deployment 部署
        Deployment deploy = repositoryService.createDeployment()
                .key(createReqDTO.getKey()).name(createReqDTO.getName()).category(createReqDTO.getCategory())
                .addBytes(createReqDTO.getKey() + BPMN_FILE_SUFFIX, createReqDTO.getBpmnBytes())
                .deploy();

        // 设置 ProcessDefinition 的 category 分类
        ProcessDefinition definition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
        repositoryService.setProcessDefinitionCategory(definition.getId(), createReqDTO.getCategory());
        // 注意 1，ProcessDefinition 的 key 和 name 是通过 BPMN 中的 <bpmn2:process /> 的 id 和 name 决定
        // 注意 2，目前该项目的设计上，需要保证 Model、Deployment、ProcessDefinition 使用相同的 key，保证关联性。
        //          否则，会导致 ProcessDefinition 的分页无法查询到。
        if (!Objects.equals(definition.getKey(), createReqDTO.getKey())) {
            throw ServiceExceptionUtil.exception(PROCESS_DEFINITION_KEY_NOT_MATCH, createReqDTO.getKey(), definition.getKey());
        }
        if (!Objects.equals(definition.getName(), createReqDTO.getName())) {
            throw ServiceExceptionUtil.exception(PROCESS_DEFINITION_NAME_NOT_MATCH, createReqDTO.getName(), definition.getName());
        }

        // 流转条件表达式相关的字段
        BpmnModel bpmnModel = modelService.getBpmnModelByProcessDefId(definition.getId());
        Process process = bpmnModel.getMainProcess();
        String conditionIds = process.getAttributeValue(BpmnXMLConstants.FLOWABLE_EXTENSIONS_NAMESPACE, "conditionIds");

        // 插入拓展表
        BpmProcessDefinitionExt definitionDO = BpmProcessDefinitionConvert.INSTANCE.convert2(createReqDTO);
        definitionDO.setProcessDefinitionId(definition.getId());
        definitionDO.setConditionIds(conditionIds);
        processDefinitionMapper.insert(definitionDO);
        return definition.getId();
    }

    @Override
    public void updateProcessDefinitionState(String id, Integer state) {
        // 激活
        if (Objects.equals(SuspensionState.ACTIVE.getStateCode(), state)) {
            repositoryService.activateProcessDefinitionById(id, false, null);
            return;
        }
        // 挂起
        if (Objects.equals(SuspensionState.SUSPENDED.getStateCode(), state)) {
            // suspendProcessInstances = false，进行中的任务，不进行挂起。
            // 原因：只要新的流程不允许发起即可，老流程继续可以执行。
            repositoryService.suspendProcessDefinitionById(id, false, null);
            return;
        }
        log.error("[updateProcessDefinitionState][流程定义({}) 修改未知状态({})]", id, state);
    }

    @Override
    public String getProcessDefinitionBpmnXML(String id) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(id);
        if (bpmnModel == null) {
            return null;
        }
        BpmnXMLConverter converter = new BpmnXMLConverter();
        return StrUtil.utf8Str(converter.convertToXML(bpmnModel));
    }

    @Override
    public boolean isProcessDefinitionEquals(@Valid BpmProcessDefinitionCreateReqDTO createReqDTO) {
        // 校验 name、description 是否更新
        ProcessDefinition oldProcessDefinition = getActiveProcessDefinition(createReqDTO.getKey());
        if (oldProcessDefinition == null) {
            return false;
        }
        BpmProcessDefinitionExt oldProcessDefinitionExt = getProcessDefinitionExt(oldProcessDefinition.getId());
        if (!StrUtil.equals(createReqDTO.getName(), oldProcessDefinition.getName())
                || !StrUtil.equals(createReqDTO.getDescription(), oldProcessDefinitionExt.getDescription())
                || !StrUtil.equals(createReqDTO.getCategory(), oldProcessDefinition.getCategory())) {
            return false;
        }
        // 校验 form 信息是否更新
        if (!ObjectUtil.equal(createReqDTO.getFormType(), oldProcessDefinitionExt.getFormType())
                || !ObjectUtil.equal(createReqDTO.getFormId(), oldProcessDefinitionExt.getFormId())
                || !ObjectUtil.equal(createReqDTO.getFormConf(), oldProcessDefinitionExt.getFormConf())
                || !ObjectUtil.equal(createReqDTO.getFormFields(), oldProcessDefinitionExt.getFormFields())
                || !ObjectUtil.equal(createReqDTO.getFormCustomCreatePath(), oldProcessDefinitionExt.getFormCustomCreatePath())
                || !ObjectUtil.equal(createReqDTO.getFormCustomViewPath(), oldProcessDefinitionExt.getFormCustomViewPath())) {
            return false;
        }
        // 校验 BPMN XML 信息
        BpmnModel newModel = buildBpmnModel(createReqDTO.getBpmnBytes());
        BpmnModel oldModel = getBpmnModel(oldProcessDefinition.getId());
        //TODO  貌似 flowable 不修改这个也不同。需要看看。 sourceSystemId 不同
        if (FlowableUtils.equals(oldModel, newModel)) {
            return false;
        }
        // 最终发现都一致，则返回 true
        return true;
    }

    /**
     * 构建对应的 BPMN Model
     *
     * @param bpmnBytes 原始的 BPMN XML 字节数组
     * @return BPMN Model
     */
    private  BpmnModel buildBpmnModel(byte[] bpmnBytes) {
        // 转换成 BpmnModel 对象
        BpmnXMLConverter converter = new BpmnXMLConverter();
        return converter.convertToBpmnModel(new BytesStreamSource(bpmnBytes), true, true);
    }



    @Override
    public BpmProcessDefinitionExt getProcessDefinitionExt(String id) {
        return processDefinitionMapper.selectByProcessDefinitionId(id);
    }

    @Override
    public List<BpmProcessDefinitionRespVO> getProcessDefinitionList(BpmProcessDefinitionListReqVO listReqVO) {
        List<ProcessDefinition> processDefinitions = getProcessDefinitions(listReqVO);
        if (CollUtil.isEmpty(processDefinitions)) {
            return Collections.emptyList();
        }

        // 获得 BpmProcessDefinitionDO Map
        List<BpmProcessDefinitionExt> processDefinitionDOs = processDefinitionMapper.selectListByProcessDefinitionIds(
                convertList(processDefinitions, ProcessDefinition::getId));
        Map<String, BpmProcessDefinitionExt> processDefinitionDOMap = convertMap(processDefinitionDOs,
                BpmProcessDefinitionExt::getProcessDefinitionId);
        // 执行查询，并返回
        return BpmProcessDefinitionConvert.INSTANCE.convertList3(processDefinitions, processDefinitionDOMap);
    }

    @Override
    public List<ProcessDefinition> getProcessDefinitions(BpmProcessDefinitionListReqVO listReqVO) {
        // 拼接查询条件
        ProcessDefinitionQuery definitionQuery = repositoryService.createProcessDefinitionQuery();
        if (Objects.equals(SuspensionState.SUSPENDED.getStateCode(), listReqVO.getSuspensionState())) {
            definitionQuery.suspended();
        } else if (Objects.equals(SuspensionState.ACTIVE.getStateCode(), listReqVO.getSuspensionState())) {
            definitionQuery.active();
        }
        if(StringUtils.isNotBlank(listReqVO.getName())){
            definitionQuery.processDefinitionNameLike("%" + listReqVO.getName() + "%");
//            definitionQuery.processDefinitionNameLikeIgnoreCase("%" + listReqVO.getName() + "%");
        }
        if(StringUtils.isNotBlank(listReqVO.getCategory())){
            definitionQuery.processDefinitionCategoryLike(listReqVO.getCategory());
        }
        // 执行查询
        List<ProcessDefinition> processDefinitions = definitionQuery.list();
        return processDefinitions;
    }

    @Override
    public Page<BpmProcessDefinitionRespVO> getProcessDefinitionListPage(CurrentUser user, BpmProcessDefinitionListReqVO listReqVO) {
        // 拼接查询条件
        ProcessDefinitionQuery definitionQuery = repositoryService.createProcessDefinitionQuery();
        if (Objects.equals(SuspensionState.SUSPENDED.getStateCode(), listReqVO.getSuspensionState())) {
            definitionQuery.suspended();
        } else if (Objects.equals(SuspensionState.ACTIVE.getStateCode(), listReqVO.getSuspensionState())) {
            definitionQuery.active();
        }
        if(StringUtils.isNotBlank(listReqVO.getName())){
            definitionQuery.processDefinitionNameLike("%" + listReqVO.getName() + "%");
//            definitionQuery.processDefinitionNameLikeIgnoreCase("%" + listReqVO.getName() + "%");
        }
        if(StringUtils.isNotBlank(listReqVO.getCategory())){
            definitionQuery.processDefinitionCategoryLike(listReqVO.getCategory());
        }
        /*
        * flowable:candidateStarterGroups="group1" flowable:candidateStarterUsers="all"
        * 暂时没找到definitionQuery支持匹配账户或组 并同时支持查询为null的方法，故写死all判断组为null时(也就是模型上没选择角色或组织机构时)
        * 匹配candidateStarterUsers为all的数据(前端判断roleid或orgid为null时写死为all)
        * */
        definitionQuery.startableByUserOrGroups("all", FlowableUtils.getCandidateGroupNoPrefix(user));
        // 执行查询
        List<ProcessDefinition> processDefinitions = definitionQuery.orderByProcessDefinitionVersion().desc().orderByProcessDefinitionKey().asc()
                .listPage(PageUtils.getStart(listReqVO), listReqVO.getPageSize());
        Page<BpmProcessDefinitionRespVO> page = new Page<>(listReqVO.getPage(), listReqVO.getPageSize());
        if (CollUtil.isEmpty(processDefinitions)) {
            page.setRecords(emptyList());
            page.setTotal(definitionQuery.count());
            return page;
        }

        // 获得 BpmProcessDefinitionDO Map
        List<BpmProcessDefinitionExt> processDefinitionDOs = processDefinitionMapper.selectListByProcessDefinitionIds(
                convertList(processDefinitions, ProcessDefinition::getId));
        Map<String, BpmProcessDefinitionExt> processDefinitionDOMap = convertMap(processDefinitionDOs,
                BpmProcessDefinitionExt::getProcessDefinitionId);
        // 拼接结果
        long definitionCount = definitionQuery.count();
        List<BpmProcessDefinitionRespVO> list = BpmProcessDefinitionConvert.INSTANCE.convertList3(processDefinitions, processDefinitionDOMap);;
        page.setRecords(list);
        page.setTotal(definitionCount);
        return page;
    }

    @Override
    public Page<BpmProcessDefinitionPageItemRespVO> getProcessDefinitionPage(BpmProcessDefinitionPageReqVO pageVO) {
        ProcessDefinitionQuery definitionQuery = repositoryService.createProcessDefinitionQuery();
        if (StrUtil.isNotBlank(pageVO.getKey())) {
            definitionQuery.processDefinitionKey(pageVO.getKey());
        }
        if(StringUtils.isNotBlank(pageVO.getName())){
            definitionQuery.processDefinitionNameLike("%" + pageVO.getName() + "%");
//            definitionQuery.processDefinitionNameLikeIgnoreCase("%" + pageVO.getName() + "%");
        }
        if(StringUtils.isNotBlank(pageVO.getCategory())){
            definitionQuery.processDefinitionCategoryLike(pageVO.getCategory());
        }
        if (Objects.equals(SuspensionState.SUSPENDED.getStateCode(), pageVO.getSuspensionState())) {
            definitionQuery.suspended();
        } else if (Objects.equals(SuspensionState.ACTIVE.getStateCode(), pageVO.getSuspensionState())) {
            definitionQuery.active();
        }

        // 执行查询
        List<ProcessDefinition> processDefinitions = definitionQuery.orderByProcessDefinitionVersion().desc()
                .listPage(PageUtils.getStart(pageVO), pageVO.getPageSize());

        Page<BpmProcessDefinitionPageItemRespVO> page = new Page<>(pageVO.getPage(), pageVO.getPageSize());
        if (CollUtil.isEmpty(processDefinitions)) {
            page.setRecords(emptyList());
            page.setTotal(definitionQuery.count());
            return page;
        }
        // 获得 Deployment Map
        Set<String> deploymentIds = new HashSet<>();
        processDefinitions.forEach(definition -> addIfNotNull(deploymentIds, definition.getDeploymentId()));
        Map<String, Deployment> deploymentMap = getDeploymentMap(deploymentIds);

        // 获得 BpmProcessDefinitionDO Map
        List<BpmProcessDefinitionExt> processDefinitionDOs = processDefinitionMapper.selectListByProcessDefinitionIds(
                convertList(processDefinitions, ProcessDefinition::getId));
        Map<String, BpmProcessDefinitionExt> processDefinitionDOMap = convertMap(processDefinitionDOs,
                BpmProcessDefinitionExt::getProcessDefinitionId);

        // 获得 Form Map
        Set<String> formIds = convertSet(processDefinitionDOs, BpmProcessDefinitionExt::getFormId);
        Map<String, BpmForm> formMap = formService.getFormMap(formIds);

        // 拼接结果
        long definitionCount = definitionQuery.count();
        List<BpmProcessDefinitionPageItemRespVO> list = BpmProcessDefinitionConvert.INSTANCE.convertList(processDefinitions, deploymentMap, processDefinitionDOMap, formMap);
        page.setRecords(list);
        page.setTotal(definitionCount);
        return page;
    }

    /**
     * 激活或挂起流程定义
     *
     * @param suspended 是否暂停状态
     * @param definitionId 流程定义ID
     */
    @Override
    public void updateState(Boolean suspended, String definitionId) {
        if (!suspended) {
            // 激活
            repositoryService.activateProcessDefinitionById(definitionId, true, null);
        } else {
            // 挂起
            repositoryService.suspendProcessDefinitionById(definitionId, true, null);
        }
    }

    /**
     * 删除流程定义
     *
     * @param deployId 流程部署ID act_ge_bytearray 表中 deployment_id值
     */
    @Override
    public void delete(String deployId) {
        // true 允许级联删除 ,不设置会导致数据库外键关联异常
        repositoryService.deleteDeployment(deployId, true);
    }

    @Override
    public BpmProcessDefinitionRespVO getDefinitionRespVO(String definitionId) {
        BpmProcessDefinitionExt processDefinitionExt = this.getProcessDefinitionExt(definitionId);
//        ProcessDefinition processDefinition = this.getProcessDefinition(definitionId);    // 此方法查询的字段category值为http://flowable.org/bpmn
        ProcessDefinition processDefinition = this.getProcessDefinition2(definitionId);
        BpmProcessDefinitionRespVO definitionRespVO = BpmProcessDefinitionConvert.INSTANCE.convert3(processDefinition);
        BpmProcessDefinitionConvert.INSTANCE.copyTo(processDefinitionExt, definitionRespVO);
        return definitionRespVO;
    }

    @Override
    public void deleteProcessDefinitionByModelId(String modelId) {
        processDefinitionMapper.deleteProcessDefinitionByModelId(modelId);
    }

    @Override
    public void deleteProcessDefinitionByDefId(List<String> processDefinitionlIds) {
        processDefinitionMapper.deleteProcessDefinitionByDefId(processDefinitionlIds);
    }

}
