package com.sanyth.portal.bpm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmFormVariable;
import com.sanyth.portal.core.base.BaseQuery;

import java.util.List;
import java.util.Map;

/**
 * 表单变量 服务接口
 * Created by ZWB on 2022-08-05.
 */
public interface IBpmFormVariableService extends IService<BpmFormVariable> {
    Page<BpmFormVariable> queryPage(BaseQuery query);
    List<BpmFormVariable> queryList(BpmFormVariable variable);

    /**
     * 格式化表单变量
     */
    String formatVariable(String formFields, Map<String,Object> varMap);

}
