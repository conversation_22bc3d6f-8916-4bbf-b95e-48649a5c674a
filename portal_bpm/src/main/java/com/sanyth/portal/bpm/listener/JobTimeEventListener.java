package com.sanyth.portal.bpm.listener;

import com.google.common.collect.ImmutableSet;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEntityEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEventType;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.event.AbstractFlowableEngineEventListener;
import org.flowable.task.service.TaskService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 全局监听定时事件
 *
 * <AUTHOR>
 * @date 2022/08/10
 */
@Component
public class JobTimeEventListener extends AbstractFlowableEngineEventListener {

    @Resource
    @Lazy
    private RuntimeService runtimeService;

    @Resource
    @Lazy
    private TaskService taskService;

    public static final Set<FlowableEngineEventType> JOBTIME_EVENTS = ImmutableSet.<FlowableEngineEventType>builder()
            .add(FlowableEngineEventType.TIMER_FIRED)
            .add(FlowableEngineEventType.TIMER_SCHEDULED)
            .build();

    public JobTimeEventListener(){
        super(JOBTIME_EVENTS);
    }

    @Override
    protected void timerFired(FlowableEngineEntityEvent event) {
        super.timerFired(event);
    }

    @Override
    protected void timerScheduled(FlowableEngineEntityEvent event) {
        super.timerScheduled(event);
    }
}
