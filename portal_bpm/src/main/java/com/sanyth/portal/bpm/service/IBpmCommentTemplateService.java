package com.sanyth.portal.bpm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmCommentTemplate;
import com.sanyth.portal.core.base.BaseQuery;

import java.util.List;

/**
 * 审批意见模板 服务接口
 * Created by WDL on 2022-11-11.
 */
public interface IBpmCommentTemplateService extends IService<BpmCommentTemplate> {
    Page<BpmCommentTemplate> queryPage(BaseQuery query);

    List<BpmCommentTemplate> queryList(BpmCommentTemplate bpmCommentTemplate);

    Long getCount(BpmCommentTemplate bpmCommentTemplate);

    /**
     * 保存审批意见到个人模板
     */
    void addMyComment(String reason);
}
