package com.sanyth.portal.bpm.service;

import com.sanyth.portal.bpm.dto.AdminUserRespDTO;
import com.sanyth.portal.model.SytPermissionAccount;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Admin 用户 API 接口
 */
public interface AdminUserApi {

    /**
     * 通过用户 ID 查询用户
     *
     * @param id 用户ID
     * @return 用户对象信息
     */
    AdminUserRespDTO getUser(String id);

        /**
        * 通过用户 humancode 查询用户
        *
        * @param humancode 用户账户
        * @return 用户对象信息
        */
    SytPermissionAccount getAccount(String humancode);

    /**
     * 获得指定角色的用户数组
     *
     * @param roleIds 部门数组
     * @return 用户数组
     */
    List<AdminUserRespDTO> getUsersByRoleIds(Collection<String> roleIds);
    /**
     * 获得指定部门的用户数组
     *
     * @param deptIds 部门数组
     * @return 用户数组
     */
    List<AdminUserRespDTO> getUsersByDeptIds(Collection<String> deptIds);
    /**
     * 获得指定部门的主管
     *
     * @param deptIds 部门数组
     * @return 用户数组
     */
    List<AdminUserRespDTO> getHeadsByDeptIds(Collection<String> deptIds);

    /**
     * 获得指定岗位的用户数组
     *
     * @param postIds 岗位数组
     * @return 用户数组
     */
    List<AdminUserRespDTO> getUsersByPostIds(Collection<String> postIds);

    /**
     * 获得用户 Map
     *
     * @param ids 用户编号数组
     * @return 用户 Map
     */
    Map<String, AdminUserRespDTO> getUserMap(Collection<String> ids);

    /**
     * 校验用户们是否有效。如下情况，视为无效：
     * 1. 用户编号不存在
     * 2. 用户被禁用
     *
     * @param ids 用户编号数组
     */
//    void validUsers(Set<String> ids);

    /**
     * 获得用户 Map
     *
     * @param ids 用户编号数组
     * @return 用户 Map
     */
    List<AdminUserRespDTO> getUsersByHumancodes(Collection<String> ids);

}
