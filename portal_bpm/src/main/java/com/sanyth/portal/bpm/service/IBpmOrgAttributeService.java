package com.sanyth.portal.bpm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmOrgAttribute;
import com.sanyth.portal.core.base.BaseQuery;

import java.util.List;

/**
 * 流程组织关系管理 服务接口
 * Created by ZWB on 2022-11-03.
 */
public interface IBpmOrgAttributeService extends IService<BpmOrgAttribute> {
    Page<BpmOrgAttribute> queryPage(BaseQuery query);

    List<BpmOrgAttribute> queryList(BpmOrgAttribute attribute);
}
