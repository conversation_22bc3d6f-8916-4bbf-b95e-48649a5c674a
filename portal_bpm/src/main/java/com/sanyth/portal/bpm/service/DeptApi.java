package com.sanyth.portal.bpm.service;

import com.sanyth.portal.bpm.dto.DeptRespDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 部门 API 接口
 *
 */
public interface DeptApi {

    /**
     * 获得部门信息
     *
     * @param id 部门编号
     * @return 部门信息
     */
    DeptRespDTO getDept(String id);

    /**
     * 获得部门信息数组
     *
     * @param ids 部门编号数组
     * @return 部门信息数组
     */
    List<DeptRespDTO> getDepts(Collection<String> ids);

    /**
     * 校验部门们是否有效。如下情况，视为无效：
     * 1. 部门编号不存在
     * 2. 部门被禁用
     *
     * @param ids 角色编号数组
     */
//    void validDepts(Collection<String> ids);

    /**
     * 获得指定编号的部门 Map
     *
     * @param ids 部门编号数组
     * @return 部门 Map
     */
    Map<String, DeptRespDTO> getDeptMap(Set<String> ids);

    /**
     * 获得部门信息数组
     *
     * @param orgnames 部门名称数组
     * @return 部门信息数组
     */
    List<DeptRespDTO> getDeptsByOrgnames(Collection<String> orgnames);

}
