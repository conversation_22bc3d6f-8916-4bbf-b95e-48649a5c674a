package com.sanyth.portal.bpm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmCategory;
import com.sanyth.portal.core.base.BaseQuery;

import java.util.List;

/**
 * 工作流分类，表单分类，模型分类 服务接口
 * Created by ZWB on 2022-05-19.
 */
public interface IBpmCategoryService extends IService<BpmCategory> {
    Page<BpmCategory> queryPage(BaseQuery query);

    List<BpmCategory> queryList(BpmCategory category);

}
