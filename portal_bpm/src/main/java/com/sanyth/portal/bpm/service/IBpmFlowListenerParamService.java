package com.sanyth.portal.bpm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmFlowListenerParam;
import com.sanyth.portal.core.base.BaseQuery;

/**
 * 监听器参数 服务接口
 * Created by ZWB on 2022-07-12.
 */
public interface IBpmFlowListenerParamService extends IService<BpmFlowListenerParam> {
        Page<BpmFlowListenerParam> queryPage(BaseQuery query);
}
