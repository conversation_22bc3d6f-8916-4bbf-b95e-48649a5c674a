package com.sanyth.portal.bpm.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmForm;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.Resp;
import com.sanyth.portal.util.collection.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 工作流表单 服务接口
 * Created by ZWB on 2022-05-11.
 */
public interface IBpmFormService extends IService<BpmForm> {
    Page<BpmForm> queryPage(BaseQuery query);

    List<BpmForm> getFormList(Collection<String> ids);

    default Map<String, BpmForm> getFormMap(Collection<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        return CollectionUtils.convertMap(this.getFormList(ids), BpmForm::getId);
    }

    Resp copy(String id);

    List<BpmForm> queryList(BpmForm form);

    void edit(BpmForm bpmForm);
}
