package com.sanyth.portal.bpm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmFlowListener;
import com.sanyth.portal.core.base.BaseQuery;

/**
 * 流程监听器 服务接口
 * Created by ZWB on 2022-07-12.
 */
public interface IBpmFlowListenerService extends IService<BpmFlowListener> {
        Page<BpmFlowListener> queryPage(BaseQuery query);
}
