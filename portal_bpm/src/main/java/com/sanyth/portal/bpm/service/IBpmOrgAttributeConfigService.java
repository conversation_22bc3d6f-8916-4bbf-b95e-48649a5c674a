package com.sanyth.portal.bpm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmOrgAttributeConfig;
import com.sanyth.portal.core.base.BaseQuery;

import java.util.List;

/**
 * 流程组织关系配置表 服务接口
 * Created by ZWB on 2022-11-03.
 */
public interface IBpmOrgAttributeConfigService extends IService<BpmOrgAttributeConfig> {
    Page<BpmOrgAttributeConfig> queryPage(BaseQuery query);

    void edit(BpmOrgAttributeConfig bpmOrgAttributeConfig);

    List<BpmOrgAttributeConfig> queryList(BpmOrgAttributeConfig config);
}
