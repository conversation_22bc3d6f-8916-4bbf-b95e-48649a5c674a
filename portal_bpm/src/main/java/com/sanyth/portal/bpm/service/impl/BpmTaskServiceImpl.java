package com.sanyth.portal.bpm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.sanyth.portal.bpm.constant.FlowMessageConstants;
import com.sanyth.portal.bpm.constant.ProcessConstants;
import com.sanyth.portal.bpm.constant.TaskConstants;
import com.sanyth.portal.bpm.convert.BpmTaskConvert;
import com.sanyth.portal.bpm.dto.*;
import com.sanyth.portal.bpm.enums.BpmProcessInstanceResultEnum;
import com.sanyth.portal.bpm.enums.BpmProcessInstanceStatusEnum;
import com.sanyth.portal.bpm.exception.BpmBusinessException;
import com.sanyth.portal.bpm.mapper.BpmProcessInstanceExtMapper;
import com.sanyth.portal.bpm.mapper.BpmTaskExtMapper;
import com.sanyth.portal.bpm.model.*;
import com.sanyth.portal.bpm.service.*;
import com.sanyth.portal.bpm.util.FindNextNodeUtil;
import com.sanyth.portal.bpm.util.FlowableUtils;
import com.sanyth.portal.bpm.util.FormUtils;
import com.sanyth.portal.bpm.util.PageUtils;
import com.sanyth.portal.bpm.vo.*;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.core.common.RedisUtil;
import com.sanyth.portal.core.util.SpringUtils;
import com.sanyth.portal.model.SytPermissionAccount;
import com.sanyth.portal.model.SytSysParam;
import com.sanyth.portal.service.ISytPermissionAccountService;
import com.sanyth.portal.service.SytSysParamService;
import com.sanyth.portal.util.collection.CollectionUtils;
import com.sanyth.portal.util.json.JsonUtils;
import com.sanyth.portal.util.mybatisplus.MybatisParameterUtils;
import com.sanyth.portal.util.property.PropertyPlaceholderHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.engine.*;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.event.FlowableMultiInstanceActivityCompletedEvent;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.bpmn.behavior.ParallelMultiInstanceBehavior;
import org.flowable.engine.impl.bpmn.behavior.SequentialMultiInstanceBehavior;
import org.flowable.engine.impl.persistence.entity.ActivityInstanceEntity;
import org.flowable.engine.impl.util.ProcessDefinitionUtil;
import org.flowable.engine.repository.Model;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.identitylink.api.IdentityLinkType;
import org.flowable.identitylink.api.history.HistoricIdentityLink;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.task.service.history.NativeHistoricTaskInstanceQuery;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.sanyth.portal.bpm.enums.ErrorCodeConstants.*;
import static com.sanyth.portal.bpm.util.ServiceExceptionUtil.exception;
import static com.sanyth.portal.util.collection.CollectionUtils.convertMap;
import static com.sanyth.portal.util.collection.CollectionUtils.convertSet;
import static java.util.Collections.emptyList;


/**
 * 流程任务实例 Service 实现类
 */
@Slf4j
@Service("bpmTaskService")
public class BpmTaskServiceImpl extends ServiceImpl<BpmTaskExtMapper, BpmTaskExt> implements BpmTaskService {

    @Resource
    private TaskService taskService;
    @Resource
    private HistoryService historyService;
    @Resource
    private IBpmProcessInstanceExtService processInstanceService;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private BpmTaskExtMapper taskExtMapper;
    @Resource
    private ISytPermissionAccountService userService;
    @Resource
    protected RepositoryService repositoryService;
    @Resource
    protected RuntimeService runtimeService;
    @Resource
    private BpmMessageService bpmMessageService;
    @Autowired
    protected IdentityService identityService;
    @Autowired
    private BpmModelService bpmModelService;
    @Autowired
    protected ManagementService managementService;
    @Resource
    private BpmProcessInstanceExtMapper bpmProcessInstanceExtMapper;
    @Resource
    IBpmOrgAttributeConfigService bpmOrgAttributeConfigService;
    @Autowired
    private IBpmCommentTemplateService iBpmCommentTemplateService;
    @Resource
    @Lazy // 解决循环依赖
    private BpmTaskService bpmTaskService;
    @Autowired
    private IBpmFormFieldValueService iBpmFormFieldValueService;
    @Autowired
    private ProcessEngineConfiguration processEngineConfiguration;
    @Resource
    private BpmProcessDefinitionService processDefinitionService;

    @Autowired
    private IBpmFormVariableService iBpmFormVariableService;
    @Resource
    SytSysParamService paramService;

    @Override
    public Page<BpmTaskTodoPageItemRespVO> getTodoTaskPage(CurrentUser user, BpmTaskTodoDonePageReqVO pageVO) {
        // 查询待办任务
        TaskQuery taskQuery = getTodoTaskQuery(user, pageVO);
        // 执行查询
        List<Task> tasks = taskQuery.listPage(PageUtils.getStart(pageVO), pageVO.getPageSize());
        Page<BpmTaskTodoPageItemRespVO> page = new Page<>(pageVO.getPage(), pageVO.getPageSize());
        if (CollUtil.isEmpty(tasks)) {
            page.setRecords(emptyList());
            page.setTotal(taskQuery.count());
            return page;
        }

        // 获得 ProcessInstance Map
        Map<String, ProcessInstance> processInstanceMap = processInstanceService.getProcessInstanceMap(convertSet(tasks, Task::getProcessInstanceId));
        // 获得 User Map
        Map<String, AdminUserRespDTO> userMap = adminUserApi.getUserMap(convertSet(processInstanceMap.values(), instance -> instance.getStartUserId()));
        // 拼接结果
        List<BpmTaskTodoPageItemRespVO> list = BpmTaskConvert.INSTANCE.convertList1(tasks, processInstanceMap, userMap);
        Map<String, BpmProcessInstanceExt> bpmProcessInstanceExtMap = processInstanceService.getBpmProcessInstanceExtMap(convertSet(tasks, Task::getProcessInstanceId));
        // 获得流程定义对应的第一个发起人节点标识
        Map<String, String> processDefIdAndTaskKeyMap = getProcessDefIdAndTaskKey(convertSet(tasks, Task::getProcessDefinitionId));
        list.forEach(item->{
            BpmProcessInstanceExt bpmProcessInstanceExt = bpmProcessInstanceExtMap.get(item.getProcessInstance().getId());
            item.setFormVariables(bpmProcessInstanceExt.getFormVariables());
            String startUserTaskKey = processDefIdAndTaskKeyMap.get(bpmProcessInstanceExt.getProcessDefinitionId());
            item.setIsStartUserTask(Objects.equals(item.getTaskDefinitionKey(), startUserTaskKey));
        });
        page.setRecords(list);
        page.setTotal(taskQuery.count());
        return page;
    }

    @Override
    public Map<String, String> getProcessDefIdAndTaskKey(Set<String> processDefinitionIds) {
        Map<String, String> map = new HashMap<>();
        processDefinitionIds.forEach(defId ->{
            BpmnModel bpmnModel = repositoryService.getBpmnModel(defId);
            String taskKey = FlowableUtils.getBpmnModelStartUserTaskKey(bpmnModel);
            if(org.apache.commons.lang3.StringUtils.isNotBlank(taskKey)){
                map.put(defId, taskKey);
            }
        });
        return map;
    }

    private TaskQuery getTodoTaskQuery(CurrentUser user, BpmTaskTodoDonePageReqVO pageVO) {
        TaskQuery taskQuery = taskService.createTaskQuery().active().includeProcessVariables().taskCandidateOrAssigned(user.getHumanCode()) // 分配给自己
                .taskCandidateGroupIn(FlowableUtils.getCandidateGroup(user))    //在候选组
                .orderByTaskCreateTime().desc(); // 创建时间倒序
        if (StrUtil.isNotBlank(pageVO.getName())) {
            taskQuery.taskNameLike("%" + pageVO.getName() + "%");
        }
        if (pageVO.getBeginCreateTime() != null) {
            taskQuery.taskCreatedAfter(pageVO.getBeginCreateTime());
        }
        if (pageVO.getEndCreateTime() != null) {
            taskQuery.taskCreatedBefore(pageVO.getEndCreateTime());
        }
        if (StrUtil.isNotBlank(pageVO.getProcessDefinitionName())) {
            taskQuery.processDefinitionNameLike("%" + pageVO.getProcessDefinitionName() + "%");
        }
        if (StrUtil.isNotBlank(pageVO.getProcessDefinitionId())) {
            taskQuery.processDefinitionId(pageVO.getProcessDefinitionId());
        }
        StringBuilder builder = new StringBuilder();
        if (StringUtils.isNotBlank(pageVO.getStartUserId())) {
            builder.append(" and t.humancode='").append(pageVO.getStartUserId()).append("' ");
        }
        if (StringUtils.isNotBlank(pageVO.getStartUserName())) {
            builder.append(" and t.humanname like '%").append(pageVO.getStartUserName()).append("%' ");
        }
        if (StringUtils.isNotBlank(pageVO.getStartUserDept())) {
            builder.append(" and t.organizationnames like '%").append(pageVO.getStartUserDept()).append("%' ");
        }
        if (builder.length() > 0) {
            String sql = "select * from ACT_HI_PROCINST where EXISTS (";
            sql += " select 1 from (";
            sql += " select  bpm_a.PROCESSINSTANCEID " +
                    " from BPM_PROCESS_INSTANCE_EXT bpm_a, SYT_PERMISSION_ACCOUNT t " +
                    " where bpm_a.STARTUSERID = t.humancode ";
            if (StringUtils.isNotBlank(pageVO.getStartUserId())) {
                sql += " and bpm_a.STARTUSERID='" + pageVO.getStartUserId() + "' ";
            }
            if (StrUtil.isNotBlank(pageVO.getProcessDefinitionId())) {
                sql += " and bpm_a.PROCESSDEFINITIONID='" + pageVO.getProcessDefinitionId() + "' ";
            }
            sql += builder.toString() + ") a where  a.PROCESSINSTANCEID = ACT_HI_PROCINST.PROC_INST_ID_ ";
            sql += ") ";
            List<HistoricProcessInstance> historicProcessInstanceList = historyService.createNativeHistoricProcessInstanceQuery().sql(sql).list();
            Set<String> processInstanceIds = historicProcessInstanceList.stream().map(HistoricProcessInstance::getId).collect(Collectors.toSet());
            if (processInstanceIds.isEmpty()) {
                processInstanceIds.add("isempty");
            }
            taskQuery.processInstanceIdIn(processInstanceIds);
        }
        return taskQuery;
    }

    @Override
    public Long getTodoTaskCount(CurrentUser user, BpmTaskTodoDonePageReqVO pageVO) {
        // 查询待办任务
        TaskQuery taskQuery = getTodoTaskQuery(user, pageVO);
        return taskQuery.count();
    }

    @Override
    public Long getDoneTaskCount(CurrentUser user, BpmTaskTodoDonePageReqVO pageVO) {
        // 查询已办任务
        // HistoricTaskInstanceQuery taskQuery = getDoneTaskQuery(user, pageVO);
        // setDoneTaskQuery(pageVO, taskQuery);
        NativeHistoricTaskInstanceQuery taskQuery = getDoneTaskQueryCount(user, pageVO);
        return taskQuery.count();
    }

    /**
     * historyService.createNativeHistoricTaskInstanceQuery().sql(sql)已可查询出已办的数据，
     * 不需要再使用historyService.createHistoricTaskInstanceQuery()再查询一遍
     * @param user
     * @param pageVO
     * @return org.flowable.task.api.history.HistoricTaskInstanceQuery
     */
    @Deprecated
    private HistoricTaskInstanceQuery getDoneTaskQuery(CurrentUser user, BpmTaskTodoDonePageReqVO pageVO) {
        String sql = "select * from ACT_HI_TASKINST where ASSIGNEE_ = #{ASSIGNEE} and exists (SELECT 1 from BPM_TASK_EXT b where (b.TASKDATATYPE <> 'INITIATOR' or b.TASKDATATYPE IS null)  and ACT_HI_TASKINST.TASK_DEF_KEY_=b.TASKDEFKEY) " +
                " and exists (SELECT 1 from ACT_HI_ACTINST b where (ACT_NAME_ <> '发起人' or ACT_NAME_ is null) and ACT_HI_TASKINST.TASK_DEF_KEY_=b.ACT_ID_) ";
        StringBuilder builder = new StringBuilder();
        if (StringUtils.isNotBlank(pageVO.getStartUserId())) {
            builder.append(" and t.humancode='").append(pageVO.getStartUserId()).append("' ");
        }
        if (StringUtils.isNotBlank(pageVO.getStartUserName())) {
            builder.append(" and t.humanname like '%").append(pageVO.getStartUserName()).append("%' ");
        }
        if (StringUtils.isNotBlank(pageVO.getStartUserDept())) {
            builder.append(" and t.organizationnames like '%").append(pageVO.getStartUserDept()).append("%' ");
        }
        if (builder.length() > 0) {
            sql += " and EXISTS ( ";
            sql += " select 1 from (select  bpm_a.PROCESSINSTANCEID, bpm_a.STARTUSERID, t.humanname " +
                    " from BPM_PROCESS_INSTANCE_EXT bpm_a, SYT_PERMISSION_ACCOUNT t " +
                    " where bpm_a.STARTUSERID = t.humancode ";
            if (StringUtils.isNotBlank(pageVO.getStartUserId())) {
                sql += " and bpm_a.STARTUSERID='" + pageVO.getStartUserId() + "' ";
            }
            sql += builder.toString() + ") a where  a.PROCESSINSTANCEID = ACT_HI_TASKINST.PROC_INST_ID_ ";
            sql += ") ";
        }
        //TODO 根据TASK_DEF_KEY_查询不是发起人的已办数据 待优化
        List<HistoricTaskInstance> historicTaskInstanceList = historyService.createNativeHistoricTaskInstanceQuery().sql(sql).parameter("ASSIGNEE", user.getHumanCode()).list();
        // Set<String> taskDefKeys = historicTaskInstanceList.stream().map(HistoricTaskInstance::getTaskDefinitionKey).collect(Collectors.toSet());
        // 2023-10-01：使用TaskDefinitionKey（流程设计时，任务节点对应的key）字段，不同的流程中会有重复的值；故修改为使用processInstanceIdIn（发起的流程实例ID）
        Set<String> processInstanceIds = historicTaskInstanceList.stream().map(HistoricTaskInstance::getProcessInstanceId).collect(Collectors.toSet());
        if (processInstanceIds.isEmpty()) {
            processInstanceIds.add("isempty");
        }
        int batchSize = 900;
        List<List<String>> batches = Lists.partition(new ArrayList<>(processInstanceIds), batchSize);
        HistoricTaskInstanceQuery taskInstanceQuery = historyService.createHistoricTaskInstanceQuery().finished().or();
        // 此处有问题，processInstanceIdIn的值是batches最后一组数据
        for (List<String> batch : batches) {
            taskInstanceQuery.processInstanceIdIn(new HashSet<>(batch));
        }
        taskInstanceQuery.endOr().taskAssignee(user.getHumanCode())
                .orderByHistoricTaskInstanceEndTime()
                .desc();
        return taskInstanceQuery;
//        return historyService.createHistoricTaskInstanceQuery().finished() // 已完成
//                // .taskDefinitionKeys(taskDefKeys)
////                .processInstanceIdIn(processInstanceIds)
//                .processInstanceIdIn(processInstanceIds)
//                .or().taskAssignee(user.getHumanCode()) // 分配给自己
////                .taskCandidateGroupIn(FlowableUtils.getCandidateGroup(user))  // 此条件，会查出多余数据；查询待办时用这个条件
//                .endOr().orderByHistoricTaskInstanceEndTime().desc();
    }
    private NativeHistoricTaskInstanceQuery getDoneTaskQueryVo(CurrentUser user, BpmTaskTodoDonePageReqVO pageVO) {
        return getNativeHistoricTaskInstanceQuery(user, pageVO, "select ACT_HI_TASKINST.* ");
    }
    private NativeHistoricTaskInstanceQuery getDoneTaskQueryCount(CurrentUser user, BpmTaskTodoDonePageReqVO pageVO) {
        return getNativeHistoricTaskInstanceQuery(user, pageVO, "select count(1) ");
    }

    private NativeHistoricTaskInstanceQuery getNativeHistoricTaskInstanceQuery(CurrentUser user, BpmTaskTodoDonePageReqVO pageVO, String sql) {
        sql += " from ACT_HI_TASKINST " +
                " where ACT_HI_TASKINST.END_TIME_ is not null and ASSIGNEE_ = #{ASSIGNEE} and exists (SELECT 1 from BPM_TASK_EXT b where (b.TASKDATATYPE <> 'INITIATOR' or b.TASKDATATYPE IS null)  and ACT_HI_TASKINST.TASK_DEF_KEY_=b.TASKDEFKEY) " +
                " and exists (SELECT 1 from ACT_HI_ACTINST b where (ACT_NAME_ <> '发起人' or ACT_NAME_ is null) and ACT_HI_TASKINST.TASK_DEF_KEY_=b.ACT_ID_) ";

        StringBuilder builder = new StringBuilder();
        if (StringUtils.isNotBlank(pageVO.getStartUserId())) {
            builder.append(" and t.humancode='").append(pageVO.getStartUserId()).append("' ");
        }
        if (StringUtils.isNotBlank(pageVO.getStartUserName())) {
            builder.append(" and t.humanname like '%").append(pageVO.getStartUserName()).append("%' ");
        }
        if (StringUtils.isNotBlank(pageVO.getStartUserDept())) {
            builder.append(" and t.organizationnames like '%").append(pageVO.getStartUserDept()).append("%' ");
        }
        if (builder.length() > 0) {
            sql += " and EXISTS ( ";
            sql += " select 1 from (select  bpm_a.PROCESSINSTANCEID, bpm_a.STARTUSERID, t.humanname " +
                    " from BPM_PROCESS_INSTANCE_EXT bpm_a, SYT_PERMISSION_ACCOUNT t " +
                    " where bpm_a.STARTUSERID = t.humancode ";
            if (StringUtils.isNotBlank(pageVO.getStartUserId())) {
                sql += " and bpm_a.STARTUSERID='" + pageVO.getStartUserId() + "' ";
            }
            sql += builder.toString() + ") a where  a.PROCESSINSTANCEID = ACT_HI_TASKINST.PROC_INST_ID_ ";
            sql += ") ";
        }
        if (StrUtil.isNotBlank(pageVO.getName())) {
            sql += " and ACT_HI_TASKINST.NAME_ like #{taskName} ";
        }
        if (pageVO.getBeginCreateTime() != null) {
            sql += " and ACT_HI_TASKINST.START_TIME_>= TO_DATE(#{beginCreateTime},'yyyy-mm-dd hh24:mi:ss') ";
        }
        if (pageVO.getEndCreateTime() != null) {
            sql += " and ACT_HI_TASKINST.START_TIME_ <= TO_DATE(#{endCreateTime},'yyyy-mm-dd hh24:mi:ss') ";
        }
        if (StrUtil.isNotBlank(pageVO.getProcessDefinitionId())) {
            sql += " and ACT_HI_TASKINST.PROC_DEF_ID_ = #{processDefinitionId} ";
        }
        // 流程定义相关查询
        StringBuilder act_re_procdef_Sql = new StringBuilder();
        if (StrUtil.isNotBlank(pageVO.getProcessDefinitionName())) {
            act_re_procdef_Sql.append(" and ACT_RE_PROCDEF.NAME_ like #{processDefinitionName} ");
        }
        if (act_re_procdef_Sql.length() > 0) {
            sql += " and EXISTS ( ";
            sql += " SELECT 1 FROM ACT_RE_PROCDEF where ACT_HI_TASKINST.PROC_DEF_ID_=ACT_RE_PROCDEF.ID_";
            sql += act_re_procdef_Sql.toString();
            sql += " ) ";
        }
        sql += " order by ACT_HI_TASKINST.END_TIME_ desc ";
        //TODO 根据TASK_DEF_KEY_查询不是发起人的已办数据 待优化
        NativeHistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createNativeHistoricTaskInstanceQuery().sql(sql);
        historicTaskInstanceQuery.parameter("ASSIGNEE", user.getHumanCode());

        if (StrUtil.isNotBlank(pageVO.getName())) {
            historicTaskInstanceQuery.parameter("taskName", "%" + pageVO.getName() + "%");
        }
        if (pageVO.getBeginCreateTime() != null) {
            historicTaskInstanceQuery.parameter("beginCreateTime", DateUtil.format(pageVO.getBeginCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (pageVO.getEndCreateTime() != null) {
            historicTaskInstanceQuery.parameter("endCreateTime", DateUtil.format(pageVO.getEndCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (StrUtil.isNotBlank(pageVO.getProcessDefinitionId())) {
            historicTaskInstanceQuery.parameter("processDefinitionId", pageVO.getProcessDefinitionId());
        }
        if (StrUtil.isNotBlank(pageVO.getProcessDefinitionName())) {
            historicTaskInstanceQuery.parameter("processDefinitionName", "%" + pageVO.getProcessDefinitionName() + "%");
        }

        return historicTaskInstanceQuery;
    }

    @Override
    public Page<BpmTaskDonePageItemRespVO> getDoneTaskPage(CurrentUser user, BpmTaskTodoDonePageReqVO pageVO) {
        // 查询已办任务 TODO 是否需要排除掉，ACT_HI_TASKINST中name='发起人'的数据（即自己发起的任务）
        // HistoricTaskInstanceQuery taskQuery = getDoneTaskQuery(user, pageVO);
        NativeHistoricTaskInstanceQuery taskQuery = getDoneTaskQueryVo(user, pageVO);
        NativeHistoricTaskInstanceQuery taskQueryCount = getDoneTaskQueryCount(user, pageVO);
        return getBpmTaskDonePageItemRespVOPage2(pageVO, taskQuery, taskQueryCount);
    }
    private Page<BpmTaskDonePageItemRespVO> getBpmTaskDonePageItemRespVOPage2(BpmTaskTodoDonePageReqVO pageVO, NativeHistoricTaskInstanceQuery taskQuery, NativeHistoricTaskInstanceQuery taskQueryCount) {
        long count = taskQueryCount.count();
        // 执行查询
        List<HistoricTaskInstance> tasks = taskQuery.listPage(PageUtils.getStart(pageVO), pageVO.getPageSize());
        Page<BpmTaskDonePageItemRespVO> page = new Page<>(pageVO.getPage(), pageVO.getPageSize());
        if (CollUtil.isEmpty(tasks)) {
            page.setRecords(emptyList());
            page.setTotal(count);
            return page;
        }

        // 获得 TaskExtDO Map
        // List<BpmTaskExt> bpmTaskExtDOs = taskExtMapper.selectListByTaskIds(convertSet(tasks, HistoricTaskInstance::getId));
        LambdaQueryWrapper<BpmTaskExt> queryWrapper = Wrappers.lambdaQuery();
        try {
            MybatisParameterUtils.cutInParameter(queryWrapper, BpmTaskExt::getTaskid, new ArrayList<>(convertSet(tasks, HistoricTaskInstance::getId)));
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<BpmTaskExt> bpmTaskExtDOs = taskExtMapper.selectList(queryWrapper);
        Map<String, BpmTaskExt> bpmTaskExtDOMap = convertMap(bpmTaskExtDOs, BpmTaskExt::getTaskid);
//        // 获得 ProcessInstance Map
        Map<String, HistoricProcessInstance> historicProcessInstanceMap = processInstanceService.getHistoricProcessInstanceMap(convertSet(tasks, HistoricTaskInstance::getProcessInstanceId));
//        // 获得 User Map
        Map<String, AdminUserRespDTO> userMap = adminUserApi.getUserMap(convertSet(historicProcessInstanceMap.values(), instance -> instance.getStartUserId()));
//        // 拼接结果
        List<BpmTaskDonePageItemRespVO> list = BpmTaskConvert.INSTANCE.convertList2(tasks, bpmTaskExtDOMap, historicProcessInstanceMap, userMap);

        Map<String, BpmProcessInstanceExt> bpmProcessInstanceExtMap = processInstanceService.getBpmProcessInstanceExtMap(convertSet(tasks, HistoricTaskInstance::getProcessInstanceId));
        list.forEach(item->{
            BpmProcessInstanceExt bpmProcessInstanceExt = bpmProcessInstanceExtMap.get(item.getProcessInstance().getId());
            item.setFormVariables(bpmProcessInstanceExt.getFormVariables());
            item.setProcessInstance(item.getProcessInstance().setStatus(bpmProcessInstanceExt.getStatus()));
        });
        page.setRecords(list);
        page.setTotal(count);
        return page;
    }

    private Page<BpmTaskDonePageItemRespVO> getBpmTaskDonePageItemRespVOPage(BpmTaskTodoDonePageReqVO pageVO, HistoricTaskInstanceQuery taskQuery) {
        setDoneTaskQuery(pageVO, taskQuery);
        // 执行查询
        List<HistoricTaskInstance> tasks = taskQuery.listPage(PageUtils.getStart(pageVO), pageVO.getPageSize());
        Page<BpmTaskDonePageItemRespVO> page = new Page<>(pageVO.getPage(), pageVO.getPageSize());
        if (CollUtil.isEmpty(tasks)) {
            page.setRecords(emptyList());
            page.setTotal(taskQuery.count());
            return page;
        }

        // 获得 TaskExtDO Map
        // List<BpmTaskExt> bpmTaskExtDOs = taskExtMapper.selectListByTaskIds(convertSet(tasks, HistoricTaskInstance::getId));
        LambdaQueryWrapper<BpmTaskExt> queryWrapper = Wrappers.lambdaQuery();
        try {
            MybatisParameterUtils.cutInParameter(queryWrapper, BpmTaskExt::getTaskid, new ArrayList<>(convertSet(tasks, HistoricTaskInstance::getId)));
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<BpmTaskExt> bpmTaskExtDOs = taskExtMapper.selectList(queryWrapper);
        Map<String, BpmTaskExt> bpmTaskExtDOMap = convertMap(bpmTaskExtDOs, BpmTaskExt::getTaskid);
//        // 获得 ProcessInstance Map
        Map<String, HistoricProcessInstance> historicProcessInstanceMap = processInstanceService.getHistoricProcessInstanceMap(convertSet(tasks, HistoricTaskInstance::getProcessInstanceId));
//        // 获得 User Map
        Map<String, AdminUserRespDTO> userMap = adminUserApi.getUserMap(convertSet(historicProcessInstanceMap.values(), instance -> instance.getStartUserId()));
//        // 拼接结果
        List<BpmTaskDonePageItemRespVO> list = BpmTaskConvert.INSTANCE.convertList2(tasks, bpmTaskExtDOMap, historicProcessInstanceMap, userMap);

        Map<String, BpmProcessInstanceExt> bpmProcessInstanceExtMap = processInstanceService.getBpmProcessInstanceExtMap(convertSet(tasks, HistoricTaskInstance::getProcessInstanceId));
        list.forEach(item->{
            BpmProcessInstanceExt bpmProcessInstanceExt = bpmProcessInstanceExtMap.get(item.getProcessInstance().getId());
            item.setFormVariables(bpmProcessInstanceExt.getFormVariables());
            item.setProcessInstance(item.getProcessInstance().setStatus(bpmProcessInstanceExt.getStatus()));
        });
        page.setRecords(list);
        page.setTotal(taskQuery.count());
        return page;
    }

    private void setDoneTaskQuery(BpmTaskTodoDonePageReqVO pageVO, HistoricTaskInstanceQuery taskQuery) {
        if (StrUtil.isNotBlank(pageVO.getName())) {
            taskQuery.taskNameLike("%" + pageVO.getName() + "%");
        }
        if (pageVO.getBeginCreateTime() != null) {
            taskQuery.taskCreatedAfter(pageVO.getBeginCreateTime());
        }
        if (pageVO.getEndCreateTime() != null) {
            taskQuery.taskCreatedBefore(pageVO.getEndCreateTime());
        }
        if (StrUtil.isNotBlank(pageVO.getProcessDefinitionId())) {
            taskQuery.processDefinitionId(pageVO.getProcessDefinitionId());
        }
        if (StrUtil.isNotBlank(pageVO.getProcessDefinitionName())) {
            taskQuery.processDefinitionNameLike("%" + pageVO.getProcessDefinitionName() + "%");
        }
        /*List<String> processInstanceIds = new ArrayList<>(((HistoricTaskInstanceQueryImpl) taskQuery).getProcessInstanceIds());

        if (CollectionUtil.isNotEmpty(processInstanceIds)) {
            double v = processInstanceIds.size() / 1000.0;
            for (int i = 0; i < v; i++) {
                int i1 = i + 1;
                List<String> subList = processInstanceIds.subList(i * 1000, (Math.min((i1 * 1000), processInstanceIds.size())));
                taskQuery.or().processInstanceIdIn(subList).endOr();
            }
        }*/

    }

    @Override
    public List<Task> getTasksByProcessInstanceIds(List<String> processInstanceIds) {
        if (CollUtil.isEmpty(processInstanceIds)) {
            return emptyList();
        }
        return taskService.createTaskQuery().processInstanceIdIn(processInstanceIds).orderByTaskCreateTime().desc().list();
    }

    @Override
    public List<BpmTaskRespVO> getTaskListByProcessInstanceId(String processInstanceId) {
        // 获得任务列表
        List<HistoricTaskInstance> tasks = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).orderByHistoricTaskInstanceStartTime().desc() // 创建时间倒序
                .list();
        if (CollUtil.isEmpty(tasks)) {
            return emptyList();
        }

        // 获得 TaskExtDO Map
        List<BpmTaskExt> bpmTaskExtDOs = taskExtMapper.selectListByTaskIds(convertSet(tasks, HistoricTaskInstance::getId));
        Map<String, BpmTaskExt> bpmTaskExtDOMap = convertMap(bpmTaskExtDOs, BpmTaskExt::getTaskid);
//        // 获得 ProcessInstance Map
        HistoricProcessInstance processInstance = processInstanceService.getHistoricProcessInstance(processInstanceId);
//        // 获得 User Map
        Set<String> userIds = convertSet(tasks, task -> task.getAssignee());
        userIds.add(processInstance.getStartUserId());
        Map<String, AdminUserRespDTO> userMap = adminUserApi.getUserMap(userIds);
//        // 拼接数据
        List<BpmTaskRespVO> bpmTaskRespVOS = BpmTaskConvert.INSTANCE.convertList3(tasks, bpmTaskExtDOMap, processInstance, userMap);
        // 审批内容
        List<Comment> commentList = taskService.getProcessInstanceComments(processInstanceId);
        Set<String> commentUserIds = convertSet(commentList, comment -> comment.getUserId());
        Map<String, AdminUserRespDTO> commentUserMap = adminUserApi.getUserMap(commentUserIds);
        // 审批人员
        bpmTaskRespVOS.forEach(objTmp -> {
            List<HistoricIdentityLink> linksForTask = historyService.getHistoricIdentityLinksForTask(objTmp.getId());
            StringBuilder stringBuilder = new StringBuilder();
            for (HistoricIdentityLink identityLink : linksForTask) {
                if (ProcessConstants.USER_TYPE_CANDIDATE.equals(identityLink.getType())) {
                    if (StringUtils.isNotBlank(identityLink.getUserId())) {
                        stringBuilder.append(identityLink.getUserId()).append(",");
                    }
                    if (StringUtils.isNotBlank(identityLink.getGroupId())) {
                        stringBuilder.append(identityLink.getGroupId()).append(",");
                    }
                }
            }
            if (stringBuilder.length() > 0) {
                objTmp.setGroupIds(stringBuilder.substring(0, stringBuilder.length() - 1));
            }

            // 获取意见评论内容
            if (CollUtil.isNotEmpty(commentList)) {
                List<BpmCommentVo> comments = new ArrayList<>();
                for (Comment comment : commentList) {
                    if (Objects.equals(comment.getTaskId(),objTmp.getId())) {
                        BpmCommentVo bpmCommentVo = BpmTaskConvert.INSTANCE.convertBpmCommentVo(comment);
                        if (commentUserMap.get(comment.getUserId()) != null) {
                            AdminUserRespDTO adminUserRespDTO = commentUserMap.get(comment.getUserId());
                            bpmCommentVo.setHumanName(adminUserRespDTO.getHumanName());
                            bpmCommentVo.setHumanId(adminUserRespDTO.getHumanId());
                            bpmCommentVo.setUser(adminUserRespDTO);
                        }
                        comments.add(bpmCommentVo);
                    }
                }
                objTmp.setCommentList(comments);
            }
        });
        Map<String, List<BpmTaskRespVO.User>> map = getTaskAssigneeUserList(bpmTaskRespVOS);
        bpmTaskRespVOS.forEach(objTmp -> {
            List<BpmTaskRespVO.User> userList = map.get(objTmp.getId());
            if(userList != null && userList.size() > 0){
                objTmp.setAssigneeUserList(map.get(objTmp.getId()));
                objTmp.setAssigneeUserStr(userList.stream().map(tmp -> tmp.getHumanName()).collect(Collectors.joining("、")));
            }
        });

        return bpmTaskRespVOS;
    }

    private Map<String, List<BpmTaskRespVO.User>> getTaskAssigneeUserList(List<BpmTaskRespVO> bpmTaskRespVOS) {
        HashMap<String, List<BpmTaskRespVO.User>> resultMap = new HashMap<>();
        bpmTaskRespVOS.forEach(objTmp -> {
            BpmnModel bpmnModel = repositoryService.getBpmnModel(objTmp.getProcessInstance().getProcessDefinitionId());
            FlowElement flowElement = FlowableUtils.findFlowNode(objTmp.getDefinitionKey(), bpmnModel, "now");
            String dataType = flowElement.getAttributeValue(BpmnXMLConstants.FLOWABLE_EXTENSIONS_NAMESPACE, "dataType");
            List<BpmTaskRespVO.User> adminUserRespDTOS = resultMap.get(objTmp.getId());
            if (adminUserRespDTOS == null) {
                adminUserRespDTOS = new ArrayList<>();
                this.getCurrentTaskAssignUserData(objTmp.getGroupIds(), objTmp.getAssigneeUser(), dataType, adminUserRespDTOS);
                resultMap.put(objTmp.getId(), new ArrayList<>(adminUserRespDTOS));
            }

        });

        return resultMap;
    }

    private void getCurrentTaskAssignUserData(String groupIds, BpmTaskRespVO.User assigneeUser, String dataType, List<BpmTaskRespVO.User> adminUserRespDTOS) {
        if (StringUtils.isNotBlank(groupIds)) {
            List<AdminUserRespDTO> list = new ArrayList<>();
            if (groupIds.startsWith("ROLE") && "ROLES".equals(dataType)) {
                groupIds = groupIds.replace("ROLE", "");
                String[] tmpArr = groupIds.split(",");
                list = adminUserApi.getUsersByRoleIds(Arrays.asList(tmpArr));
            } else if (groupIds.startsWith("DEPT") && "DEPTS".equals(dataType)) {   //部门
                groupIds = groupIds.replace("DEPT", "");
                String[] tmpArr = groupIds.split(",");
                list = adminUserApi.getUsersByDeptIds(Arrays.asList(tmpArr));
            } else if (groupIds.startsWith("HEAD") && "DEPTHEAD".equals(dataType)) {   //部门领导
                String[] tmpArr = groupIds.split(",");
                for (String tmp : tmpArr) {
                    String[] split = tmp.split("#");
                    String head = split[0];
                    head = head.replace("HEAD", "");
                    list = adminUserApi.getUsersByHumancodes(Arrays.asList(head.split("&")));
                }
            } else if (groupIds.startsWith("ATTRS") && "ATTRS".equals(dataType)) {      //发起人组织关系
                groupIds = groupIds.replace("ATTRS", "");
                String[] tmpArr = groupIds.split(",");
                List<AdminUserRespDTO> subList = new ArrayList<>();
                for (String tmp : tmpArr) {
                    BpmOrgAttributeConfig config = new BpmOrgAttributeConfig();
                    config.setAttributeId(tmp);
                    List<BpmOrgAttributeConfig> configs = bpmOrgAttributeConfigService.queryList(config);
                    for (BpmOrgAttributeConfig attributeConfig : configs) {
                        subList.addAll(adminUserApi.getUsersByHumancodes(Arrays.asList(attributeConfig.getAttributeValue().split(","))));
                    }
                }
                list = subList;
            } else if (groupIds.startsWith("ATTR") && "INITATTR".equals(dataType)) {      //组织关系
                groupIds = groupIds.replace("ATTR", "");
                String[] tmpArr = groupIds.split(",");
                QueryWrapper<BpmOrgAttributeConfig> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("id", tmpArr);
                List<BpmOrgAttributeConfig> configs = bpmOrgAttributeConfigService.list(queryWrapper);
                List<AdminUserRespDTO> subList = new ArrayList<>();
                configs.forEach(c -> {
                    subList.addAll(adminUserApi.getUsersByHumancodes(Arrays.asList(c.getAttributeValue().split(","))));
                });
                list = subList;
            }
            /*else if (dataType.contains("USERS") || dataType.contains("INITIATOR") || dataType.contains("HEAD")) {
                list = adminUserApi.getUsersByHumancodes(Arrays.asList(groupIds.split(",")));
            }*/
            else {
                list = adminUserApi.getUsersByHumancodes(Arrays.asList(groupIds.split(",")));
            }
            for (AdminUserRespDTO obj : list) {
                adminUserRespDTOS.add(BpmTaskConvert.INSTANCE.convert3(obj));
            }
        } else if (assigneeUser != null) {
            adminUserRespDTOS.add(assigneeUser);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> approveTask(String userId, @Valid BpmTaskApproveReqVO reqVO) {
        List<String> ids = Arrays.asList(reqVO.getId().split(","));
        Map<String, Object> map = new HashMap<>();
        HashSet<String> definitionIdAndTaskKeySet = new HashSet<>();
        // 跳转审批的节点
        String returnTaskDefKey = "";
        for (int i = 0; i < ids.size(); i++) {
            // 校验任务存在
            String id = ids.get(i);
            Task task = checkTask(userId, id);
            BpmTaskExt bpmTaskExt = taskExtMapper.selectListByTaskId(id);
            // TODO 2022-11-14 目前待办，会查询出驳回到发起人节点的任务：校验当前任务节点是否是第一个发起人节点，此时不可审批
            BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
            StartEvent startEvent = FlowableUtils.getBpmnModelStartEvent(bpmnModel);
            // 开始节点后面的发起人节点的id
            String targetRef = startEvent.getOutgoingFlows().get(0).getTargetRef();
            if (Objects.equals(targetRef, task.getTaskDefinitionKey()) && Objects.equals(userId, task.getAssignee())) {
                throw exception(TASK_NOT_APPROVE);
            }
            Map<String, Object> variables = taskService.getVariables(id);

            // 校验流程实例存在
            ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
            if (instance == null) {
                throw exception(PROCESS_INSTANCE_NOT_EXISTS);
            }
            BpmProcessDefinitionExt processDefinitionExt = processDefinitionService.getProcessDefinitionExt(
                    instance.getProcessDefinitionId());
            Assert.notNull(processDefinitionExt, "流程定义拓展({}) 不存在", id);
            //TODO add 2023-05-05 判断是否需要选择下一节点办理人
            // 批量审批 且不是委派的任务
            if(org.apache.commons.lang3.StringUtils.isBlank(reqVO.getDefinitionKey()) && bpmTaskExt.getResult() != 6){
                BpmTaskApproveReqVO approveReqVO = new BpmTaskApproveReqVO()
                        .setId(id)
                        .setDefinitionKey(task.getTaskDefinitionKey())
                        .setDefinitionId(task.getProcessDefinitionId())
                        .setReturnTaskDefKey(bpmTaskExt.getReturnTaskDefKey());
                approveReqVO.setExpressionVariables(variables);
                List<JSONObject> extendedProperties = bpmTaskExt.getExtendedPropertiesJson();
                if(CollUtil.isNotEmpty(extendedProperties)){
                    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(extendedProperties.get(0)), JSONObject.class);
                    if(jsonObject.containsKey(TaskConstants.TASK_CHANGE_NEXT_USER) && jsonObject.getBoolean(TaskConstants.TASK_CHANGE_NEXT_USER)) {
                        List<FlowNodeVo> nextFlowNodeVos = getNextFlowNodeVos(approveReqVO, bpmnModel);
                        if (CollUtil.isNotEmpty(nextFlowNodeVos)) {
                            String batchError = "";
                            if (!definitionIdAndTaskKeySet.contains(task.getProcessDefinitionId() + task.getTaskDefinitionKey())) {
                                String s = instance.getName() + "[" + task.getName() + "]";
                                batchError = batchError == "" ? s : batchError + "、" + s;
                            }
                            map.put("batchError", batchError);
                            return map;
                        }
                    }
                }
            }

            if (StringUtils.isNotBlank(userId)) {
                // 设置用户
                FlowableUtils.setAuthenticatedUserId(userId);
            }
            String formCustomApproveData = "";
            Map<String, Object> expressionVariables = reqVO.getExpressionVariables();
            Map<String, Object> formVariables = reqVO.getFormVariables();

            // 批量审批时，expressionVariables、formVariables为空
            if (expressionVariables == null && formVariables == null) {
                // 流程实例扩展表数据
                BpmProcessInstanceExt instanceExt = bpmProcessInstanceExtMapper.selectByProcessInstanceId(task.getProcessInstanceId());
                String conditionIds = processDefinitionExt.getConditionIds();
                if (!ObjectUtils.isEmpty(conditionIds)) {
                    expressionVariables = new HashMap<>();
                    JSONArray conditionIdArry = JSONArray.parseArray(conditionIds);
                    Set<Object> objectSet = conditionIdArry.stream().collect(Collectors.toSet());
                    Map<String, Object> fieldsValues = (Map) instanceExt.getFormVariables().get("fieldsValues");
                    for (Object tmp : objectSet) {
                        expressionVariables.put(String.valueOf(tmp), fieldsValues.get(String.valueOf(tmp)));
                    }

                    formCustomApproveData = instanceExt.getFormVariables().get("formCustomViewData") != null ?
                            String.valueOf(instanceExt.getFormVariables().get("formCustomViewData")) : "";
                }
            }else{
                //取出formCustomViewData，赋值给bpmTaskExt的formCustomApproveData,记录每个节点的自定义表单审批数据
                formCustomApproveData = formVariables.get("formCustomViewData") != null ? JSON.toJSONString(formVariables.get("formCustomViewData")) : "";
            }
            if (reqVO.isRejectFlag()) {   // 驳回
                runtimeService.setVariable(task.getExecutionId(), TaskConstants.PROCESS_OUTCOME, BpmProcessInstanceResultEnum.RETURN.getDesc());
                runtimeService.setVariable(task.getExecutionId(), TaskConstants.PROCESS_PASSCOUNT, 0);
                Object failCount = runtimeService.getVariable(task.getExecutionId(), TaskConstants.PROCESS_FAILCOUNT);
                if (failCount != null) {
                    runtimeService.setVariable(task.getExecutionId(), TaskConstants.PROCESS_FAILCOUNT, Integer.parseInt(failCount.toString()) + 1);
                } else {
                    runtimeService.setVariable(task.getExecutionId(), TaskConstants.PROCESS_FAILCOUNT, 1);
                }
            } else {
                runtimeService.setVariable(task.getExecutionId(), TaskConstants.PROCESS_OUTCOME, "");
                Object passCount = runtimeService.getVariable(task.getExecutionId(), TaskConstants.PROCESS_PASSCOUNT);
                if (passCount != null) {
                    runtimeService.setVariable(task.getExecutionId(), TaskConstants.PROCESS_PASSCOUNT, Integer.parseInt(passCount.toString()) + 1);
                } else {
                    runtimeService.setVariable(task.getExecutionId(), TaskConstants.PROCESS_PASSCOUNT, 1);
                }
                runtimeService.setVariable(task.getExecutionId(), TaskConstants.PROCESS_FAILCOUNT, 0);
            }
            // 跳级审批时，判断流程走向是否和要跳转的路线一致
            boolean approveSkipFlag = this.isApproveSkipFlag(task.getId(), bpmTaskExt.getReturnTaskDefKey(), task.getProcessDefinitionId(), task.getTaskDefinitionKey());
            if (DelegationState.PENDING.equals(task.getDelegationState())) {
                // 被委派的任务，审批通过
                if (reqVO.isRejectFlag()) {
                    // 驳回
                    taskService.addComment(task.getId(), instance.getId(), BpmProcessInstanceResultEnum.RETURN.getResult().toString(), reqVO.getReason());
                } else {
                    taskService.addComment(task.getId(), instance.getId(), BpmProcessInstanceResultEnum.DELEGATE.getResult().toString(), reqVO.getReason());
                    // add 2023-03-28 任务拓展表由原来的【委派】更新为【处理中】，便于委派后的任务，被委派人审批时不弹框显示选择下节点办理人；而委派人审批时显示
                    taskExtMapper.updateByTaskId(new BpmTaskExt().setTaskid(task.getId()).setResult(BpmProcessInstanceResultEnum.PROCESS.getResult()).setReason(reqVO.getReason()));
                }
//                taskService.resolveTask(task.getId(), instance.getProcessVariables());
                // 2023-02-01 跳级审批
                if(approveSkipFlag){
                    changeActivity(task, bpmTaskExt.getReturnTaskDefKey(),variables);
                    returnTaskDefKey = bpmTaskExt.getReturnTaskDefKey();
                } else {
                    taskService.resolveTask(task.getId(), expressionVariables);
                    // 2022-02-01 删除历史节点：选择了驳回跳转，没有走原来的流程走向，需要删除原来走向上保留的节点
                    if(org.apache.commons.lang3.StringUtils.isNotBlank(bpmTaskExt.getDelActivityIds())){
                        bpmProcessInstanceExtMapper.deleteRunActinstsByIds(Arrays.asList(bpmTaskExt.getDelActivityIds().split(",")));
                        bpmProcessInstanceExtMapper.deleteHisActinstsByIds(Arrays.asList(bpmTaskExt.getDelActivityIds().split(",")));
                    }
                }
            } else {
                if (StringUtils.isNotBlank(userId)) {
                    taskService.setAssignee(task.getId(), userId);
                }
                if (reqVO.isRejectFlag()) {   // 驳回
                    taskService.addComment(task.getId(), instance.getId(), BpmProcessInstanceResultEnum.RETURN.getResult().toString(), reqVO.getReason());
                    // 更新任务拓展表为驳回
                    taskExtMapper.updateByTaskId(new BpmTaskExt().setTaskid(task.getId()).setResult(BpmProcessInstanceResultEnum.RETURN.getResult()).setReason(reqVO.getReason()));
                } else {
                    taskService.addComment(task.getId(), instance.getId(), BpmProcessInstanceResultEnum.APPROVE.getResult().toString(), reqVO.getReason());
                    // 更新任务拓展表为通过
                    taskExtMapper.updateByTaskId(new BpmTaskExt().setTaskid(task.getId()).setResult(BpmProcessInstanceResultEnum.APPROVE.getResult()).setReason(reqVO.getReason())
                            .setFormCustomApproveData(formCustomApproveData));
                }
                // 2023-02-01 跳级审批
                if(approveSkipFlag){
                    changeActivity(task, bpmTaskExt.getReturnTaskDefKey(),variables);
                    returnTaskDefKey = bpmTaskExt.getReturnTaskDefKey();
                } else {
                    if (ObjectUtil.isNotEmpty(instance.getProcessVariables())) {
                        taskService.complete(task.getId(), instance.getProcessVariables());
                    } else {
                        taskService.complete(task.getId(), expressionVariables);
                    }
                    // 2022-02-01 删除历史节点：选择了驳回跳转，没有走原来的流程走向，需要删除原来走向上保留的节点
                    if(org.apache.commons.lang3.StringUtils.isNotBlank(bpmTaskExt.getDelActivityIds())){
                        bpmProcessInstanceExtMapper.deleteRunActinstsByIds(Arrays.asList(bpmTaskExt.getDelActivityIds().split(",")));
                        bpmProcessInstanceExtMapper.deleteHisActinstsByIds(Arrays.asList(bpmTaskExt.getDelActivityIds().split(",")));
                    }
                }

                //更新流程实例表单
                updateProcessInstanceFormVariables(reqVO, task,bpmTaskExt,userId);
                // TODO 更新表单值:看后期是否需要更新flowable表单变量值；或者发起流程的时候，不保存申请表单的值
//                runtimeService.setVariables(processInstanceId, variables);
            }
            // 清理
            FlowableUtils.clearAuthenticatedUserId();

            //是否存在抄送设置 20230131改由任务监听器实现抄送
            /*BpmTaskExt bpmTaskExt = taskExtMapper.selectListByTaskId(task.getId());
            List<JSONObject> copyListJson = bpmTaskExt.getCopyListJson();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(copyListJson)) {
                bpmMessageService.saveNewCopyMessage(task, instance, copyListJson);
            }*/
            reqVO.setInstanceId(instance.getProcessInstanceId());
            // 设置下级审批人
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(reqVO.getSelectNextUserList())) {
                designateNextApprover(reqVO.getSelectNextUserList(), reqVO, bpmnModel);
            }
            // 下一节点审批人发送消息：方法中有判断是委派任务时，给委派人发送消息
            this.pushMsgToNextTaskAssign(instance.getStartUserId(), instance, reqVO.getSelectNextUserList(), bpmnModel, task);
            if (i == 0) {  //单个任务手工指派下个节点时使用
                map.put("definitionKey", task.getTaskDefinitionKey());
                map.put("definitionId", task.getProcessDefinitionId());
                map.put("instanceId", instance.getId());
                map.put("taskId", task.getId());
                map.put("variables", expressionVariables);
                // 借用此字段，传参跳转的任务节点
                map.put("returnTaskDefKey", returnTaskDefKey);
            }
        }
        return map;
    }

    /**
     * 跳级审批时，判断流程走向是否和指定要跳转的路线一致
     * @param taskId
     * @param returnTaskDefKey
     * @return boolean
     */
    private boolean isApproveSkipFlag(String taskId, String returnTaskDefKey, String processDefId, String activityId) {
        boolean approveSkipFlag = false;
        if(org.apache.commons.lang3.StringUtils.isNotBlank(returnTaskDefKey)){
            // 2023-03-15 不判断流程走向
            approveSkipFlag = true;
            /*
            // 获取当前任务节点的下一个任务节点；有流转的，会根据流转条件判断流程走向对应的下一个任务节点
            // List<FlowNodeVo> nodeVoList = this.getNextNodesByTaskId(new BpmTaskApproveReqVO().setId(taskId));
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            *//*
             * 当前任务还未审批,可以得到表单变量
             * 审批页面因为有当前task信息，并且在审批前就调用了这个接口,所以可以查询到节点
             * 发起流程那里因为还没有task表信息,所以使用了getNextNodesByProcessDefinitionIdAndTaskDefinitionKey
             * *//*
            Map<String, Object> variables = taskService.getVariables(task.getId());
            List<UserTask> nextUserTasks = FindNextNodeUtil.getNextUserTasks(repositoryService, task, variables);
            if(CollUtil.isEmpty(nextUserTasks)){
                approveSkipFlag = true;
            } else {
                // TODO 获取下个任务节点的后续的任务节点：用于判断跳级审批时，跳转的节点是否包含在查询到到这些节点中
                List<UserTask> list = new ArrayList<>();
                for (UserTask obj : nextUserTasks) {
                    FlowNode flowNode = bpmModelService.findFlowNodeByActivityId(processDefId, obj.getId());
                    List<UserTask> userTaskList = FlowableUtils.findChildProcessUserTasks(flowNode, null, null);
                    list.addAll(userTaskList);
                }
                nextUserTasks.addAll(list);
                for (UserTask obj : nextUserTasks) {
                    if(Objects.equals(obj.getId(), returnTaskDefKey)){
                        approveSkipFlag = true;
                        break;
                    }
                }
            }
            */
        }
        return approveSkipFlag;
    }

    /**
     * 判断是子流程内部驳回，还是普通驳回；进行流程跳转
     * @param task 当前任务节点
     * @param returnTaskDefKey 跳转的目标节点标识
     * @param variables TODO 到底是全局变量还是当前任务的变量有待商榷
     */
    private Map<String, Object> changeActivity(TaskInfo task, String returnTaskDefKey, Map<String, Object> variables) {
        Map<String, Object> map = new HashMap<>();
        /*String processInstanceId = task.getProcessInstanceId();
        List<String> executionIds = new ArrayList<>();
        // 判断节点是不是子流程内部的节点
        if (bpmModelService.checkActivitySubprocessByActivityId(task.getProcessDefinitionId(), returnTaskDefKey) && bpmModelService.checkActivitySubprocessByActivityId(task.getProcessDefinitionId(), task.getTaskDefinitionKey())) {
            //6.1 子流程内部驳回
            Execution executionTask = runtimeService.createExecutionQuery().executionId(task.getExecutionId()).singleResult();
            String parentId = executionTask.getParentId();
            List<Execution> executions = runtimeService.createExecutionQuery().parentId(parentId).list();
            executions.forEach(execution -> executionIds.add(execution.getId()));
            moveExecutionsToSingleActivityId(processInstanceId,executionIds, returnTaskDefKey);
        } else {
            //6.2 普通驳回
            List<Execution> executions = runtimeService.createExecutionQuery().parentId(task.getProcessInstanceId()).list();
            executions.forEach(execution -> executionIds.add(execution.getId()));
            moveExecutionsToSingleActivityId(processInstanceId,executionIds, returnTaskDefKey);
        }*/
        BpmTaskModelQuery query = new BpmTaskModelQuery();
        query.setDefineId(task.getProcessDefinitionId());
        UserTaskModelDTO userTaskModelsDTO = getUserTaskModelDto(query);

        List<BpmTaskModelEntity> taskModelEntities = userTaskModelsDTO.getAllUserTaskModels();
        Map<String, BpmTaskModelEntity> taskModelEntitiesMap = taskModelEntities.stream().collect(
                Collectors.toMap(BpmTaskModelEntity::getTaskDefKey, a -> a, (k1, k2) -> k1));

        // 要跳转的节点 B
        List<BpmTaskModelEntity> targetNodes = new ArrayList<>(4);
        // 当前节点 A
        BpmTaskModelEntity currentNode = taskModelEntitiesMap.get(task.getTaskDefinitionKey());
        List<String> targetTaskDefineKes = Arrays.asList(returnTaskDefKey.split(","));
        targetTaskDefineKes.forEach(item -> targetNodes.add(taskModelEntitiesMap.get(item)));
        if (currentNode == null || org.springframework.util.CollectionUtils.isEmpty(targetNodes)) {
            BpmBusinessException.createByErrorMsg("currentNode or targetNodes not exists, taskId=" + task.getId());
        }
        // 设置本次全局变量信息
        if (!org.springframework.util.CollectionUtils.isEmpty(variables)) {
            runtimeService.setVariables(task.getProcessInstanceId(), variables);
        }
        // （1）如果B有多个节点
        //        必须为同一个并行网关内的任务节点（网关开始、合并节点必须一致）
        //        必须不是同一条流程线上的任务节点
        FlowableUtils.checkjumpTargetNodes(targetNodes);

        // （2）如果A和B为同一条顺序流程线上（其中包含了A/B都是非并行网关上的节点 或都为并行网关中同一条流程线上的节点），则可以直接跳转
        if (targetNodes.size() == 1 &&
                currentNode.getParallelGatewayForkRef().equals(targetNodes.get(0).getParallelGatewayForkRef())) {
            runtimeService.createChangeActivityStateBuilder().processInstanceId(task.getProcessInstanceId())
                    .moveActivityIdTo(task.getTaskDefinitionKey(), targetTaskDefineKes.get(0))
                    .changeState();
            return map;
        }

        //（3）如果A非并行分支上的任务节点
        //    则根据以上判定，B一定是为并行网关上节点，需要创建其B所在并行网关内其他任务节点已完成日志
        if (!currentNode.getInParallelGateway()) {
            String forkParallelGatwayId = targetNodes.get(0).getForkParallelGatewayId();
            ParallelGatwayDTO forkGatewayB = userTaskModelsDTO.getAllForkGatewayMap().get(forkParallelGatwayId);
            // B为并行网关上节点，需要创建其B所在并行网关内其他任务节点已完成日志
            FlowableUtils.dealParallelGatewayFinishLog(forkGatewayB, task, targetNodes.size());
            // 跳转
            runtimeService.createChangeActivityStateBuilder().processInstanceId(
                            task.getProcessInstanceId())
                    .moveSingleActivityIdToActivityIds(task.getTaskDefinitionKey(), targetTaskDefineKes)
                    .changeState();
        } else {
            //（4）如果A是并行分支上的任务节点
            //    4.1 从外向里面跳转（父并网关 》子并网关）
            //    B是为并行网关上节点，需要创建其B所在并行网关内其他任务节点已完成日志
            //   4.2 从里向外面跳转 （子并网关 》父并网关 【或】 非并行网关上的节点 【或】 其他非父子关系的并行网关节点）
            //    需要清除本任务节点并行网关上（包括父网关）所有的其他未完成的用户任务
            //    B如果是为并行网关上节点，需要创建其B所在并行网关内其他任务节点已完成日志
            map = FlowableUtils.gatewayJump(userTaskModelsDTO, targetNodes, currentNode, task, targetTaskDefineKes);

        }

        /*try {
            FlowableUtils.setCurrentTaskDueDate(task.getProcessInstanceId());
        } catch (JsonProcessingException e) {
            log.error("当前任务的超时时间异常", e);
            BpmBusinessException.createByErrorMsg("当前任务的超时时间异常");
        }*/
        return map;
    }

    /**
     * 执行跳转
     */
//    protected void moveExecutionsToSingleActivityId(String processInstanceId,List<String> executionIds, String activityId) {
//        runtimeService.createChangeActivityStateBuilder().processInstanceId(processInstanceId).moveExecutionsToSingleActivityId(executionIds, activityId).changeState();
//    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void designateNextApprover(List<SelectNextUserVO> selectNextUserList, BpmTaskApproveReqVO reqVO, BpmnModel bpmnModel) {
        String definitionKey = reqVO.getDefinitionKey();
        if(StringUtils.isNotBlank(reqVO.getReturnTaskDefKey())){
            // 不为空，则表示为跳转审批（驳回到指定目标节点，指定目标节点操作完直接跳转到驳回节点）、或者驳回到指定目标节点
            // 目标节点，只需判断此节点是否开启【是否允许上节点选择办理人】
            definitionKey = reqVO.getReturnTaskDefKey();
        }
        FlowElement flowElement = FlowableUtils.findFlowNode(definitionKey, bpmnModel, "now");
        if (flowElement == null) {
            return;
        }
        String extendData = flowElement.getAttributeValue(BpmnXMLConstants.FLOWABLE_EXTENSIONS_NAMESPACE, "extendData");
        // extendData需要判空
        if (StringUtils.isNotBlank(extendData)) {
            List<JSONObject> jsonObjects = JSON.parseArray(extendData, JSONObject.class);
            boolean changeuser = false;
            if(StringUtils.isBlank(reqVO.getReturnTaskDefKey())) {
                for (JSONObject jsonObject : jsonObjects) {
                    if (jsonObject.containsKey(TaskConstants.TASK_CHANGE_NEXT_USER)) {
                        changeuser = jsonObject.getBoolean(TaskConstants.TASK_CHANGE_NEXT_USER);       //允许手动选择办理人 true
                    }
                }
            }
            if(!changeuser){
                // 不为空，则表示为跳转审批（驳回到指定目标节点，指定目标节点操作完直接跳转到驳回节点）、或者驳回到指定目标节点
                changeuser = StringUtils.isNotBlank(reqVO.getReturnTaskDefKey());
            }
            //TODO 是否动态指定下个节点审批人
//                List<UserTask> nextUserTask = FindNextNodeUtil.getNextUserTasks(repositoryService, task, variables);
            //取回任务是忽略节点changeuser，强制设置节点审批人
            if (reqVO.isRetrieveTask()) {   //判断是否是取回
                changeuser = true;
            }
            if (changeuser && org.apache.commons.collections4.CollectionUtils.isNotEmpty(selectNextUserList) && selectNextUserList.size() > 0) {
                //TODO 手工选择节点指定用户
                for (SelectNextUserVO selectNextUserVO : selectNextUserList) {
                    String taskKey = selectNextUserVO.getTaskKey();
                    MultiVo multiVo = FlowableUtils.isMultiInstance(reqVO.getDefinitionId(),taskKey);

                    UserTaskFormVO userTaskForm = selectNextUserVO.getUserTaskFormVO();
                    List<Task> list = taskService.createTaskQuery().processInstanceId(reqVO.getInstanceId()).taskDefinitionKey(taskKey).orderByTaskCreateTime().desc().list();
                    //非会签
                    if (ObjectUtil.isEmpty(multiVo)) {
                        /** 单用户
                         **/
                        if (StringUtils.isNotBlank(userTaskForm.getAssignee())) {
                            if (list.size() == 1) {
                                Task task1 = list.get(0);
                                deleteCandidateAndIdentityLink(task1);
                                updateTaskAssignee(task1.getId(), userTaskForm.getAssignee());
                            }
                        } else if (StringUtils.isNotBlank(userTaskForm.getCandidateUsers())) {
                            /** 候选用户,选择多个用户
                             **/
                            if (list.size() == 1) {
                                Task task1 = list.get(0);
                                updateTaskAssignee(task1.getId(), "");
                                deleteCandidateAndIdentityLink(task1);
                                for (String s : userTaskForm.getCandidateUsers().split(",")) {
                                    taskService.addUserIdentityLink(task1.getId(), s, IdentityLinkType.CANDIDATE);
                                }
                            }
                        }
                    } else {    //会签节点
                        /** 单用户
                         **/
                        if (StringUtils.isNotBlank(userTaskForm.getAssignee())) {
                            if (list.size() > 0) {
                                Task task1 = list.get(0);
                                deleteCandidateAndIdentityLink(task1);
                                updateTaskAssignee(task1.getId(), userTaskForm.getAssignee());
                                List<Task> tasks = list.subList(1, list.size());
                                if (tasks.size() > 0) {     //task是多个
                                    if (multiVo.getType() instanceof ParallelMultiInstanceBehavior) {
                                        tasks.forEach(t -> {
                                            deleteCandidateAndIdentityLink(t);
                                            runtimeService.deleteMultiInstanceExecution(t.getExecutionId(), false);
                                            historyService.deleteHistoricTaskInstance(t.getId());
                                            historyService.createNativeHistoricActivityInstanceQuery()
                                                    .sql("DELETE  FROM ACT_HI_ACTINST WHERE TASK_ID_='" + t.getId() + "'").singleResult();
                                        });
                                    }else if (multiVo.getType() instanceof SequentialMultiInstanceBehavior) {
                                        //TODO 串行多实例后期实现
                                    }
                                }
                            }
                        } else if (StringUtils.isNotBlank(userTaskForm.getCandidateUsers())) {
                            /** 多用户:
                             **/
                            //先减签 再加签
                            if (multiVo.getType() instanceof ParallelMultiInstanceBehavior) {
                                if (list.size() > 0) {
                                    Task task1 = list.get(0);
                                    String taskDefinitionKey = task1.getTaskDefinitionKey();
                                    String processInstanceId = task1.getProcessInstanceId();
                                    Set<String> deleteCandidateUser = new HashSet<>();
                                    Set<String> deleteCandidateGroup = new HashSet<>();
                                    list.forEach(t->{
                                        List<IdentityLink> linkList = taskService.getIdentityLinksForTask(t.getId());
                                        linkList.forEach(link->{
                                            deleteCandidateUser.add(link.getUserId());
                                            deleteCandidateGroup.add(link.getGroupId());
                                        });
                                        deleteCandidateAndIdentityLink(t);
                                        runtimeService.deleteMultiInstanceExecution(t.getExecutionId(), false);
                                        historyService.deleteHistoricTaskInstance(t.getId());
                                        historyService.createNativeHistoricActivityInstanceQuery()
                                                .sql("DELETE FROM ACT_HI_ACTINST WHERE TASK_ID_='" + t.getId() + "'").singleResult();
                                    });
                                    for (String s : userTaskForm.getCandidateUsers().split(",")) {
                                        Execution execution = runtimeService.addMultiInstanceExecution(taskDefinitionKey, processInstanceId, Collections.singletonMap(multiVo.getAssignee(), s));
                                        Task task = taskService.createTaskQuery().executionId(execution.getId()).singleResult();
                                        deleteCandidateUser.forEach(userId -> {
                                            if (ObjectUtil.isNotNull(userId) && !Objects.equals(userId, s)) {
                                                taskService.deleteCandidateUser(task.getId(), userId);
                                            }
                                        });
                                        deleteCandidateGroup.forEach(groupId -> {
                                            if (ObjectUtil.isNotNull(groupId)) {
                                                taskService.deleteCandidateGroup(task.getId(), groupId);
                                            }
                                        });
                                        for (String ss : userTaskForm.getCandidateUsers().split(",")) {
                                            taskService.addUserIdentityLink(task.getId(), ss, IdentityLinkType.CANDIDATE);
                                        }
                                    }
                                }
                            } else if (multiVo.getType() instanceof SequentialMultiInstanceBehavior) {
                                //TODO 串行
//                                DeleteSequenceMultiInstanceCmd deleteSequenceMultiInstanceCmd = new DeleteSequenceMultiInstanceCmd(task.getAssignee(), task.getExecutionId(), multiVo.getAssigneeList(), deleteMultiBo.getAssigneeIds());
//                                managementService.executeCommand(deleteSequenceMultiInstanceCmd);
                            }

                        }

                    }
                }
            }
        }
    }

    @Override
    public void deleteCandidateAndIdentityLink(Task t) {
        List<IdentityLink> linkList = taskService.getIdentityLinksForTask(t.getId());
        for (IdentityLink link : linkList) {
            try {
                taskService.deleteCandidateUser(t.getId(), link.getUserId());
            } catch (Exception e) {
                log.error("deleteCandidateUser error");
            }
            try {
                taskService.deleteCandidateGroup(t.getId(), link.getGroupId());
            } catch (Exception e) {
                log.error("deleteCandidateGroup error");
            }
        }
        /*List<HistoricIdentityLink> identityLinks = historyService.getHistoricIdentityLinksForTask(t.getId());
        for (HistoricIdentityLink identityLink : identityLinks) {
            try {
                taskService.deleteCandidateUser(t.getId(), identityLink.getUserId());
            } catch (Exception e) {
                log.error("deleteCandidateUser error");
            }
            try {
                taskService.deleteCandidateGroup(t.getId(), identityLink.getGroupId());
            } catch (Exception e) {
                log.error("deleteCandidateGroup error");
            }
        }*/

    }


    @Override
    public void approveTaskByListener(DelegateExecution delegateExecution) {
        FlowElement flowElement = delegateExecution.getCurrentFlowElement();
        BoundaryEvent boundaryEvent = (BoundaryEvent) flowElement;
        List<BpmTaskRespVO> taskRespVOList = getTaskListByProcessInstanceId(delegateExecution.getProcessInstanceId());
        List<BpmTaskApproveReqVO> approveReqVOList = CollectionUtils.convertList(taskRespVOList, bpmTaskRespVO -> {
            BpmTaskApproveReqVO approveReqVO = new BpmTaskApproveReqVO();
            approveReqVO.setId(bpmTaskRespVO.getId());
            approveReqVO.setDefinitionKey(bpmTaskRespVO.getDefinitionKey());
            approveReqVO.setReason("自动审批");
            //TODO 流程实例获取表单信息，格式化表单变量
//            approveReqVO.setFormVariables()
            return approveReqVO;
        });
        approveReqVOList.forEach(bpmTaskApproveReqVO -> {
            if (Objects.equals(boundaryEvent.getAttachedToRefId(), bpmTaskApproveReqVO.getDefinitionKey())) {
                approveTask("", bpmTaskApproveReqVO);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectTask(String userId, @Valid BpmTaskApproveReqVO reqVO) {
        List<String> ids = Arrays.asList(reqVO.getId().split(","));
        ids.forEach(id -> {
            Task task = checkTask(userId, id);
            // 校验流程实例存在
            ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
            if (instance == null) {
                throw exception(PROCESS_INSTANCE_NOT_EXISTS);
            }

            // 设置用户
            FlowableUtils.setAuthenticatedUserId(userId);
            // 保存审批意见
            taskService.addComment(task.getId(), instance.getId(), BpmProcessInstanceResultEnum.REJECT.getResult().toString(), reqVO.getReason());

            /*
             * 2023-01-16 多实例审批方式为无时，其实也是也是或签，只是任务表只有一条数据，
             * 此时表ACT_HI_TASKINST和ACT_RU_TASK生成的任务为一条记录，ASSIGNEE_字段为空
             * 如果不设置值setAssignee，已办任务则查不到此条操作数据；
             * TODO 如果是介入操作的时候，此条数据会在介入者的已办任务列表中
             */
            if (StringUtils.isBlank(task.getAssignee())) {
                taskService.setAssignee(task.getId(), userId);
            }

            // 更新流程实例为不通过
            processInstanceService.updateProcessInstanceExtReject(instance.getProcessInstanceId(), reqVO.getReason());
            // 更新任务拓展表为不通过
            taskExtMapper.updateByTaskId(new BpmTaskExt().setTaskid(task.getId()).setResult(BpmProcessInstanceResultEnum.REJECT.getResult()).setReason(reqVO.getReason()).setEndtime(new Date()));
            // 更新
            updateByTaskidAndResult(task, BpmProcessInstanceResultEnum.REJECT);
            BpmTaskExt bpmTaskExt = taskExtMapper.selectListByTaskId(task.getId());
            updateProcessInstanceFormVariables(reqVO, task,bpmTaskExt,userId);
            // 清理
            FlowableUtils.clearAuthenticatedUserId();
            // 保存审批意见到个人模板
            iBpmCommentTemplateService.addMyComment(task.getId());
        });

    }

    private void updateProcessInstanceFormVariables(BpmTaskApproveReqVO reqVO, Task task,BpmTaskExt bpmTaskExt,String userId) {
        //批量审批时，表单上没有变量，手动复制变量
        List<JSONObject> fieldsPermissionJson = bpmTaskExt.getFieldsPermissionJson();
        Map<String, Object> map = new HashMap<>();

        //更新流程实例表单
        BpmProcessInstanceExt instanceExt = bpmProcessInstanceExtMapper.selectByProcessInstanceId(task.getProcessInstanceId());
        Map<String, Object> formVariables = instanceExt.getFormVariables();
        JSONObject form = (JSONObject) JSONObject.toJSON(formVariables);
//        JSONArray widgetList = form.getJSONArray("widgetList");
        String widgetList = form.getString("widgetList");
        String fieldsValues = form.getString("fieldsValues");
        // 判断当前审批节点是否是多任务节点
        MultiVo multiVo = FlowableUtils.isMultiInstance(instanceExt.getProcessDefinitionId(), task.getTaskDefinitionKey());

        if (MapUtil.isEmpty(reqVO.getFormVariables())) {
            if (CollectionUtil.isNotEmpty(fieldsPermissionJson)) {
                map.put("humancode", userId);
                map.put("reason", reqVO.getReason());
                String defaultValue = "";
                JSONObject jsonObject = new JSONObject(fieldsPermissionJson.get(0));
                JSONArray jsonArray = jsonObject.getJSONArray("value");
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject1 = jsonArray.getJSONObject(i);
                    String name = jsonObject1.getString("name");
                    Boolean reason = jsonObject1.getBoolean("reason");
                    if (reason) {
                        jsonObject1.put("defaultValue", "${flow.reason}");
                    }
                }
                if (ObjectUtil.isNotEmpty(multiVo)) {
                    String spxx = this.getApproveInfoByProcessInstanceIdAndTaskDefinitionKey(task.getProcessInstanceId(), task.getTaskDefinitionKey(), null);
                    map.put("reason", spxx);
                }
                //格式化后的任务节点信息
                String s = iBpmFormVariableService.formatVariable(jsonArray.toJSONString(), map);
                JSONArray formVariableArray = JSONArray.parseArray(s);
                try {
                    widgetList = replaceValue(formVariableArray, widgetList);
                    fieldsValues = replaceFieldsValues(JSONArray.parseArray(widgetList), fieldsValues);
                    fieldsValues = replaceFieldsValues(formVariableArray, fieldsValues);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
            formVariables.put("widgetList", JSONArray.parseArray(widgetList));
            formVariables.put("fieldsValues", JSONObject.parseObject(fieldsValues));
//            reqVO.setFormVariables(formVariables);
            instanceExt.setFormVariables(formVariables);
        } else {
            String spxx = reqVO.getReason();
            if (ObjectUtil.isNotEmpty(multiVo)) {
                spxx = this.getApproveInfoByProcessInstanceIdAndTaskDefinitionKey(task.getProcessInstanceId(), task.getTaskDefinitionKey(), null);
            }
            map.put("flow.reason", spxx);
            reqVO.formatFormVariables(map);
            instanceExt.setFormVariables(reqVO.getFormVariables());
        }
        bpmProcessInstanceExtMapper.updateByProcessInstanceId(instanceExt);
        // 保存表单变量的值
        saveFormFieldValueData(instanceExt);
    }

    private static String replaceValue(JSONArray formVariableArray, String widgetList) throws JsonProcessingException {
        Map<String, String> map = new HashMap<>();
        for (Object obj : formVariableArray) {
            JSONObject jsonObject = (JSONObject) obj;
            String name = jsonObject.getString("name");
            String defaultValue = jsonObject.getString("defaultValue");
            map.put(name, defaultValue);
        }
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(widgetList);
        for (String s : map.keySet()) {
            if (StringUtils.isNotEmpty(map.get(s))) {
                FormUtils.updateValue(rootNode, null,s, map.get(s));
            }
        }
        return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode);
    }
    private static String replaceFieldsValues(JSONArray formVariableArray, String fieldsValues) throws JsonProcessingException {
        Map<String, String> map = new HashMap<>();
        for (Object obj : formVariableArray) {
            JSONObject jsonObject = (JSONObject) obj;
            String name = jsonObject.getString("name");
            String defaultValue = jsonObject.getString("defaultValue");
            map.put(name, defaultValue);
        }
        JSONObject fieldsValuesObj = JSONObject.parseObject(fieldsValues);
        for (String s : map.keySet()) {
            if (fieldsValuesObj.containsKey(s) && StringUtils.isNotEmpty(map.get(s))) {
                fieldsValuesObj.put(s, map.get(s));
            }
        }
        return fieldsValuesObj.toJSONString();
    }

    /*
     * 更新指定节点待处理的任务扩展表数据
     * @param task
     * @param resultEnum
     */
    private void updateByTaskidAndResult(Task task, BpmProcessInstanceResultEnum resultEnum) {
        String processInstanceId = task.getProcessInstanceId();
        String activityId = task.getTaskDefinitionKey();
        HistoricTaskInstanceQuery taskQuery = historyService.createHistoricTaskInstanceQuery()
//                    .finished()
                .processInstanceId(processInstanceId).taskDefinitionKey(activityId);
        List<HistoricTaskInstance> taskList = taskQuery.list();
        Map<String, HistoricTaskInstance> historicTaskMap = convertMap(taskList, HistoricTaskInstance::getId);
        List<BpmTaskExt> taskExtList = taskExtMapper.selectListByTaskIds(convertSet(taskList, HistoricTaskInstance::getId));
        HistoricTaskInstance historicTaskInstance = historicTaskMap.get(task.getId());
        // 获取当前任务的处理状态
        for (BpmTaskExt obj : taskExtList) {
            HistoricTaskInstance historicTask = historicTaskMap.get(obj.getTaskid());
            if (BpmProcessInstanceResultEnum.PROCESS.getResult().equals(obj.getResult())
                    && obj.getEndtime() == null
                    && !Objects.equals(task.getId(), obj.getTaskid())
                    && Objects.equals(historicTaskInstance.getDeleteReason(), historicTask.getDeleteReason())
            ) {
                BpmTaskExt taskExtDO2 = new BpmTaskExt().setTaskid(obj.getTaskid()).setEndtime(historicTask.getEndTime()).setResult(resultEnum.getResult());
                taskExtMapper.updateByTaskidAndResultPROCESS(taskExtDO2);
            }
        }
    }

    @Override
    public void updateTaskAssignee(CurrentUser currentUser, BpmTaskUpdateAssigneeReqVO reqVO) {
        // 校验任务存在
        Task task = checkTask(currentUser.getHumanCode(), reqVO.getId());

        StringBuilder commentBuilder = new StringBuilder(currentUser.getHumanName()).append("->");
        SytPermissionAccount account = userService.getByHumancode(reqVO.getAssigneeUserId());
        if (ObjectUtil.isNotNull(account)) {
            commentBuilder.append(account.getHumanname());
        } else {
            commentBuilder.append(currentUser.getHumanName());
        }
        if (StringUtils.isNotBlank(reqVO.getComment())) {
            commentBuilder.append(": ").append(reqVO.getComment());
        }

        // 设置工作流的用户
        if (currentUser != null) {
            FlowableUtils.setAuthenticatedUserId(currentUser.getHumanCode());
        }
        // 添加审批意见
        taskService.addComment(reqVO.getId(), task.getProcessInstanceId(), BpmProcessInstanceResultEnum.TRANSFER.getResult().toString(), commentBuilder.toString());
        // 设置拥有者为当前登录人
        taskService.setOwner(reqVO.getId(), currentUser.getHumanCode());
        // 更新负责人
        updateTaskAssignee(task.getId(), reqVO.getAssigneeUserId());

        // 清理
        FlowableUtils.clearAuthenticatedUserId();
        // 更新任务扩展表
        taskExtMapper.updateByTaskId(new BpmTaskExt().setTaskid(task.getId()).setResult(BpmProcessInstanceResultEnum.TRANSFER.getResult()).setReason(commentBuilder.toString()));
        //更新流程实例表单
        BpmProcessInstanceExt instanceExt = bpmProcessInstanceExtMapper.selectByProcessInstanceId(task.getProcessInstanceId());
        Map<String, Object> map = new HashMap<>();
        map.put("flow.reason", reqVO.getComment());
        reqVO.formatFormVariables(map);
        instanceExt.setFormVariables(reqVO.getFormVariables());
        bpmProcessInstanceExtMapper.updateByProcessInstanceId(instanceExt);
        // 保存表单变量的值
        saveFormFieldValueData(instanceExt);
        // 任务转办，给被转办人发送消息
        ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        BpmProcessDefinitionExt processDefinitionExt = processDefinitionService.getProcessDefinitionExt(instance.getProcessDefinitionId());
        Model model = repositoryService.getModel(processDefinitionExt.getModelId());
        BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class);
        HashSet<String> toUsers = new HashSet<>();
        toUsers.add(reqVO.getAssigneeUserId());
        this.pushMsgToUser(instanceExt.getStartUserId(), instance, metaInfo, metaInfo.getPushChannel(), toUsers);
    }

    @Override
    public void updateTaskAssignee(String id, String userId) {
        taskService.setAssignee(id, userId);
    }


    @Override
    public void createTaskExt(Task task) {
        BpmTaskExt taskExtDO = BpmTaskConvert.INSTANCE.convert2TaskExt(task).setResult(BpmProcessInstanceResultEnum.PROCESS.getResult());
        saveExtensionElementsAndAttributes(task, taskExtDO,"create");
        taskExtMapper.insert(taskExtDO);
        // TODO 判断是否是发起人节点，此节点不发送消息；其他审批节点，需要根据不同的审批人类型进行数据查询
        // this.pushMsgToTaskAssign(task);
    }

    @Override
    public void pushMsgToTaskAssign(Task task) {
        // 判断是否是发起人节点，此节点不发送消息；其他审批节点，需要根据不同的审批人类型进行数据查询
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        // 获取流程中第一个发起人节点
        String startUserTaskKey = FlowableUtils.getBpmnModelStartUserTaskKey(bpmnModel);
        if (!Objects.equals(startUserTaskKey, task.getTaskDefinitionKey())) {
            // TODO 不是第一个发起人节点，可以给当前节点的办理人发送消息
            BpmProcessDefinitionExt processDefinitionExt = processDefinitionService.getProcessDefinitionExt(task.getProcessDefinitionId());
            if (processDefinitionExt == null) {
                return;
            }
            Model model = repositoryService.getModel(processDefinitionExt.getModelId());
            if (model == null) {
                return;
            }
            BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class);
            String pushChannel = metaInfo.getPushChannel();
            if (StringUtils.isBlank(pushChannel)) {
                return;
            }
            ProcessInstance processInstance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
            AdminUserRespDTO startUserDto = adminUserApi.getUser(processInstance.getStartUserId());
            FlowElement flowElement = FlowableUtils.findFlowNode(task.getTaskDefinitionKey(), bpmnModel, "now");
            String dataType = flowElement.getAttributeValue(BpmnXMLConstants.FLOWABLE_EXTENSIONS_NAMESPACE, "dataType");
            List<HistoricIdentityLink> linksForTask = historyService.getHistoricIdentityLinksForTask(task.getId());
            List<IdentityLink> linkList = taskService.getIdentityLinksForTask(task.getId());
            StringBuilder stringBuilder = new StringBuilder();
            for (HistoricIdentityLink identityLink : linksForTask) {
                if (ProcessConstants.USER_TYPE_CANDIDATE.equals(identityLink.getType())) {
                    if (StringUtils.isNotBlank(identityLink.getUserId())) {
                        stringBuilder.append(identityLink.getUserId()).append(",");
                    }
                    if (StringUtils.isNotBlank(identityLink.getGroupId())) {
                        stringBuilder.append(identityLink.getGroupId()).append(",");
                    }
                }
            }
            String groupIds = null;
            if (stringBuilder.length() > 0) {
                groupIds = stringBuilder.substring(0, stringBuilder.length() - 1);
            }
            BpmTaskRespVO.User assigneeUser = null;
            if (StringUtils.isNotBlank(task.getAssignee())) {
                AdminUserRespDTO user = adminUserApi.getUser(task.getAssignee());
                if (user != null) {
                    assigneeUser = BpmTaskConvert.INSTANCE.convert3(user);
                }
            }
            // 查询当前任务节点对应的办理人
            List<BpmTaskRespVO.User> adminUserRespDTOS = new ArrayList<>();
            this.getCurrentTaskAssignUserData(groupIds, assigneeUser, dataType, adminUserRespDTOS);
            if (CollectionUtil.isNotEmpty(adminUserRespDTOS)) {
                //包装格式化参数
                Map<String, Object> map = new HashMap<>();
                map.put("humanname", startUserDto.getHumanName());
                map.put("dwmc", startUserDto.getOrganizationnames());
                map.put("name", processInstance.getName());
                map.put("result", "待审核");
                String title = PropertyPlaceholderHelperUtil.placeholderHelper("${", "}", map, FlowMessageConstants.FLOW_MESSAGE_TEMPLATE_TITLE);
                String content = StringUtils.isBlank(metaInfo.getContent()) ? FlowMessageConstants.FLOW_MESSAGE_TEMPLATE_CONTENT : metaInfo.getContent();
                Set<String> toUsers = new HashSet<>();
                adminUserRespDTOS.forEach(adminUserRespDTO -> {
                    // System.err.println(" To: " + adminUserRespDTO.getHumanCode() + " From: " + PropertyPlaceholderHelperUtil.placeholderHelper("${", "}", map, content) );
                    toUsers.add(adminUserRespDTO.getHumanCode());
                });
                bpmMessageService.pushMessage(pushChannel, title, content,
                        FlowMessageConstants.FLOW_MESSAGE_TYPE_NOTICE, null, map, toUsers.toArray(new String[toUsers.size()]));
            }

        }
    }

    /**
     * 给下一节点的办理人发送消息
     * @param startUserId 发起人
     * @param instance 流程实例
     * @param selectNextUserList 选择的下一节点办理人
     * @param bpmnModel 流程模型
     * @param currentTask 当前节点任务
     */
    @Override
    public void pushMsgToNextTaskAssign(String startUserId, ProcessInstance instance, List<SelectNextUserVO> selectNextUserList, BpmnModel bpmnModel, Task currentTask) {
        BpmProcessDefinitionExt processDefinitionExt = processDefinitionService.getProcessDefinitionExt(instance.getProcessDefinitionId());
        Model model = repositoryService.getModel(processDefinitionExt.getModelId());
        BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class);
        String pushChannel = metaInfo.getPushChannel();
        if (StringUtils.isNotBlank(pushChannel)) {
            Set<String> toUsers = new HashSet<>();
            if (DelegationState.PENDING.equals(currentTask.getDelegationState())) {
                // 被委派的任务，审批通过
                // 给委派人发送消息
                toUsers.add(currentTask.getOwner());
            } else {
                if (CollUtil.isNotEmpty(selectNextUserList)) {
                    Set<String> toUsers2 = new HashSet<>();
                    selectNextUserList.forEach(selectNextUser -> {
                        if (StringUtils.isNotBlank(selectNextUser.getUserTaskFormVO().getAssignee())) {
                            toUsers2.add(selectNextUser.getUserTaskFormVO().getAssignee());
                        } else if (StringUtils.isNotBlank(selectNextUser.getUserTaskFormVO().getCandidateUsers())) {
                            toUsers2.addAll(Arrays.asList(selectNextUser.getUserTaskFormVO().getCandidateUsers().split(",")));
                        }
                    });
                    toUsers = toUsers2;
                } else {
                    // 判断下一审批节点是否是默认的
                    List<Task> list = taskService.createTaskQuery().processInstanceId(instance.getProcessInstanceId()).orderByTaskCreateTime().desc().list();
                    Set<String> taskDefinitionKeySet = new HashSet<>();
                    HashSet<BpmTaskRespVO.User> identityLinkHashSet = new HashSet<>();
                    for (Task taskTmp : list) {
                        if (Objects.equals(currentTask.getTaskDefinitionKey(), taskTmp.getTaskDefinitionKey())) {
                            // 待审批的节点和当前节点相同，则跳过
                            continue;
                        }
                        UserTask userTask = FlowableUtils.getBpmnModelUserTaskByTaskKey(bpmnModel, taskTmp.getTaskDefinitionKey());
                        if (userTask.hasMultiInstanceLoopCharacteristics()) {
                            // 会签、或签的任务节点
                            // continue;
                        }
                        if (StringUtils.isNotBlank(taskTmp.getAssignee())) {
                            toUsers.add(taskTmp.getAssignee());
                        }
                        if (taskDefinitionKeySet.contains(taskTmp.getTaskDefinitionKey())) {
                            continue;
                        }
                        taskDefinitionKeySet.add(taskTmp.getTaskDefinitionKey());
                        List<IdentityLink> linkList = taskService.getIdentityLinksForTask(taskTmp.getId());
                        FlowElement flowElement = FlowableUtils.findFlowNode(taskTmp.getTaskDefinitionKey(), bpmnModel, "now");
                        String dataType = flowElement.getAttributeValue(BpmnXMLConstants.FLOWABLE_EXTENSIONS_NAMESPACE, "dataType");
                        StringBuilder stringBuilder = new StringBuilder();
                        linkList.forEach(identityLink -> {
                            if (ProcessConstants.USER_TYPE_CANDIDATE.equals(identityLink.getType())) {
                                if (StringUtils.isNotBlank(identityLink.getUserId())) {
                                    stringBuilder.append(identityLink.getUserId()).append(",");
                                }
                                if (StringUtils.isNotBlank(identityLink.getGroupId())) {
                                    stringBuilder.append(identityLink.getGroupId()).append(",");
                                }
                            }
                        });
                        String groupIds = null;
                        if (stringBuilder.length() > 0) {
                            groupIds = stringBuilder.substring(0, stringBuilder.length() - 1);
                            // 查询当前任务节点对应的办理人
                            List<BpmTaskRespVO.User> adminUserRespDTOS = new ArrayList<>();
                            this.getCurrentTaskAssignUserData(groupIds, null, dataType, adminUserRespDTOS);
                            identityLinkHashSet.addAll(adminUserRespDTOS);
                        }
                    }
                    if (identityLinkHashSet.size() > 0) {
                        Set<String> toUsers2 = new HashSet<>();
                        identityLinkHashSet.forEach(bpmTaskRespVOUser -> {
                            toUsers2.add(bpmTaskRespVOUser.getHumanCode());
                        });
                        toUsers = toUsers2;
                    }
                }
            }
            pushMsgToUser(startUserId, instance, metaInfo, pushChannel, toUsers);
        }
    }

    private void pushMsgToUser(String startUserId, ProcessInstance instance, BpmModelMetaInfoRespDTO metaInfo, String pushChannel, Set<String> toUsers) {
        if (toUsers.size() > 0) {
            String instanceName = instance.getName();
            if (StringUtils.isBlank(instanceName)) {
                ProcessDefinition definition = processDefinitionService.getProcessDefinition(instance.getProcessDefinitionId());
                instanceName = definition.getName();
            }
            AdminUserRespDTO startUserDto = adminUserApi.getUser(startUserId);
            Map<String, Object> map = new HashMap<>();
            map.put("humanname", startUserDto.getHumanName());
            map.put("dwmc", startUserDto.getOrganizationnames());
            map.put("name", instanceName);
            map.put("result", "待审核");
            String title = PropertyPlaceholderHelperUtil.placeholderHelper("${", "}", map, FlowMessageConstants.FLOW_MESSAGE_TEMPLATE_TITLE);
            String content = StringUtils.isBlank(metaInfo.getContent()) ? FlowMessageConstants.FLOW_MESSAGE_TEMPLATE_CONTENT : metaInfo.getContent();
            // toUsers.forEach(adminUserRespDTO -> {
            //     System.err.println(" To: " + adminUserRespDTO + " From: " + PropertyPlaceholderHelperUtil.placeholderHelper("${", "}", map, content) );
            // });
            bpmMessageService.pushMessage(pushChannel, title, content,
                    FlowMessageConstants.FLOW_MESSAGE_TYPE_NOTICE, null, map, toUsers.toArray(new String[toUsers.size()]));
        }
    }

    private void saveExtensionElementsAndAttributes(Task task, BpmTaskExt taskExtDO,String event) {
        taskExtDO.setTaskDefKey(task.getTaskDefinitionKey());
        //保存extensionElements,attributes到扩展表
        Process process = ProcessDefinitionUtil.getProcess(task.getProcessDefinitionId());
        process.getFlowElements().forEach(el -> {
            if (Objects.equals(el.getId(), task.getTaskDefinitionKey())) {
//                taskExtDO.setExtensionElements(el.getExtensionElements());
//                taskExtDO.setAttributes(el.getAttributes());
                // 扩展属性，比如控制每个节点的审批意见是否必填
                String extendData = el.getAttributeValue(BpmnXMLConstants.FLOWABLE_EXTENSIONS_NAMESPACE, "extendData");
                List<JSONObject> jsonObjects = JSON.parseArray(extendData, JSONObject.class);
                List<JSONObject> copyListJson = FlowableUtils.buildExtensionElement(FlowableUtils.getMyExtensionElementList(el.getExtensionElements(), "copyItemList", "copyItem"), "type");
                List<JSONObject> fieldsPermissionJson = FlowableUtils.buildExtensionElement(FlowableUtils.getMyExtensionElementList(el.getExtensionElements(), "fieldsPermission", ""), "value");
                if ("create".equals(event)) {
                    String buttons = el.getAttributeValue(BpmnXMLConstants.FLOWABLE_EXTENSIONS_NAMESPACE, "buttons");
                    taskExtDO.setButtonsJson(JSON.parseArray(buttons, JSONObject.class));
                    taskExtDO.setFieldsPermissionJson(fieldsPermissionJson);
                    taskExtDO.setCopyListJson(copyListJson);
                    taskExtDO.setExtendedPropertiesJson(jsonObjects);
                }

                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(jsonObjects)) {
                    JSONObject object = jsonObjects.get(0);
                    String copyTime = object.getString("copyTime");
                    if (("before".equals(copyTime) && "create".equals(event)) || ("after".equals(copyTime) && "complete".equals(event))) {
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(copyListJson)) {
                            ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
                            bpmMessageService.saveNewCopyMessage(task, instance, copyListJson);
                        }
                    }
                }
            }
        });
    }

    @Override
    public void updateTaskExtComplete(Task task) {
        BpmTaskExt taskExtDO = BpmTaskConvert.INSTANCE.convert2TaskExt(task).setEndtime(new Date());
        taskExtMapper.updateByTaskId(taskExtDO);
        //TODO 任务完成时抄送
        saveExtensionElementsAndAttributes(task, taskExtDO, "complete");

    }

    @Override
    public void updateTaskExtAssign(Task task) {
        BpmTaskExt taskExtDO = new BpmTaskExt().setAssigneeuserid(task.getAssignee()).setTaskid(task.getId());
        taskExtMapper.updateByTaskId(taskExtDO);
        // 发送通知。在事务提交时，批量执行操作，所以直接查询会无法查询到 ProcessInstance，所以这里是通过监听事务的提交来实现。
        /*TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                ProcessInstance processInstance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
                AdminUserRespDTO startUser = adminUserApi.getUser(processInstance.getStartUserId());
//                messageService.sendMessageWhenTaskAssigned(BpmTaskConvert.INSTANCE.convert(processInstance, startUser, task));
            }
        });*/
    }

    /**
     * 委托任务
     *
     * @param currentUser 当前用户
     * @param reqVO       申请签证官
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delegateTask(CurrentUser currentUser, BpmTaskUpdateAssigneeReqVO reqVO) {
        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(reqVO.getId()).singleResult();
        if (ObjectUtil.isEmpty(task)) {
            throw exception(TASK__NOT_EXISTS);
        }
        StringBuilder commentBuilder = new StringBuilder(currentUser.getHumanName()).append("->");
        SytPermissionAccount account = userService.getByHumancode(reqVO.getAssigneeUserId());
        if (ObjectUtil.isNotNull(account)) {
            commentBuilder.append(account.getHumanname());
        } else {
            commentBuilder.append(currentUser.getHumanName());
        }
        if (StringUtils.isNotBlank(reqVO.getComment())) {
            commentBuilder.append(": ").append(reqVO.getComment());
        }

        // 设置工作流的用户
        if (currentUser != null) {
            FlowableUtils.setAuthenticatedUserId(currentUser.getHumanCode());
        }
        // 添加审批意见
        taskService.addComment(reqVO.getId(), task.getProcessInstanceId(), BpmProcessInstanceResultEnum.DELEGATE.getResult().toString(), commentBuilder.toString());
        // 设置办理人为当前登录人
        taskService.setOwner(reqVO.getId(), currentUser.getHumanCode());
        // 执行委派
        taskService.delegateTask(reqVO.getId(), reqVO.getAssigneeUserId());

        // 清理
        FlowableUtils.clearAuthenticatedUserId();
        // 更新任务扩展表
        taskExtMapper.updateByTaskId(new BpmTaskExt().setTaskid(task.getId()).setResult(BpmProcessInstanceResultEnum.DELEGATE.getResult()).setReason(commentBuilder.toString()));
        //更新流程实例表单
        BpmProcessInstanceExt instanceExt = bpmProcessInstanceExtMapper.selectByProcessInstanceId(task.getProcessInstanceId());
        Map<String, Object> map = new HashMap<>();
        map.put("flow.reason", reqVO.getComment());
        reqVO.formatFormVariables(map);
        instanceExt.setFormVariables(reqVO.getFormVariables());
        bpmProcessInstanceExtMapper.updateByProcessInstanceId(instanceExt);
        // 保存表单变量的值
        saveFormFieldValueData(instanceExt);
        // 任务委派，给被委派人发送消息
        ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        BpmProcessDefinitionExt processDefinitionExt = processDefinitionService.getProcessDefinitionExt(instance.getProcessDefinitionId());
        Model model = repositoryService.getModel(processDefinitionExt.getModelId());
        BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class);
        HashSet<String> toUsers = new HashSet<>();
        toUsers.add(reqVO.getAssigneeUserId());
        this.pushMsgToUser(instanceExt.getStartUserId(), instance, metaInfo, metaInfo.getPushChannel(), toUsers);

    }

    /**
     * 校验任务是否存在， 并且是否是分配给自己的任务
     *
     * @param userId 用户 id
     * @param taskId task id
     */
    private Task checkTask(String userId, String taskId) {
        Task task = getTask(taskId);
        if (task == null) {
            throw exception(TASK_COMPLETE_FAIL_NOT_EXISTS);
        }
        return task;
    }

    private Task getTask(String id) {
        return taskService.createTaskQuery().taskId(id).singleResult();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTaskExt(BpmTaskExt entity) {
        QueryWrapper<BpmTaskExt> queryWrapper = new QueryWrapper<BpmTaskExt>();
        if (StringUtils.isNotBlank(entity.getAssigneeuserid())) {
            queryWrapper.eq("ASSIGNEEUSERID", entity.getAssigneeuserid());
        }
        if (StringUtils.isNotBlank(entity.getProcessinstanceid())) {
            queryWrapper.eq("PROCESSINSTANCEID", entity.getProcessinstanceid());
        }
        if (StringUtils.isNotBlank(entity.getTaskid())) {
            queryWrapper.in("TASKID", entity.getTaskid().split(","));
        }
        taskExtMapper.delete(queryWrapper);
        System.out.println("删除taskExtMapper.delete(queryWrapper)");
    }

    @Override
    public List<BpmTaskExt> getFirstTasks(List<String> processInstanceIds) {
        StringBuilder sb = new StringBuilder();
        for (String processInstanceId : processInstanceIds) {
            sb.append("'").append(processInstanceId).append("',");
        }
        sb.deleteCharAt(sb.length() - 1);
        QueryWrapper<BpmTaskExt> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("PROCESSINSTANCEID", processInstanceIds).eq("TASKDATATYPE", "INITIATOR")
                .or().inSql("TASKID", "select TASK_ID_ from ACT_HI_ACTINST where PROC_INST_ID_ in (" + sb + ") and ACT_NAME_ = '发起人'")
        .orderByDesc("CREATETIME"); // 按创建时间降序排序
        return taskExtMapper.selectList(queryWrapper);
    }

    @Override
    public List<BpmTaskModelEntity> listBackTaskModel(BpmTaskApproveReqVO query) {
        List<BpmTaskModelEntity> resultTasks = new ArrayList<>(4);
        Task task = taskService.createTaskQuery().taskId(query.getId()).singleResult();
        Map<String, BpmTaskModelEntity> allTaskMap = initLimitBackTaskModel(query, task);

        Map<String, FlowElement> flowElementMap = FlowableUtils.getAllFlowElement(task.getProcessDefinitionId());
        FlowElement targetFlowElement = flowElementMap.get(task.getTaskDefinitionKey());
        // 目标节点的上游节点集合
        List<UserTask> tasks = new ArrayList<>(8);
        Map<String, UserTask> taskMap = new HashMap<>(8);
        FlowableUtils.loopIncomingFlows(((FlowNode) targetFlowElement).getIncomingFlows(),
                tasks, taskMap, flowElementMap, query, true);
        tasks.forEach(item -> {
            if (allTaskMap.containsKey(item.getId())) {
                resultTasks.add(allTaskMap.get(item.getId()));
            }
        });

        return resultTasks;
    }

    @Override
    public List<BpmTaskModelEntity> listAllBackTaskModel(BpmTaskApproveReqVO query) {
        List<BpmTaskModelEntity> resultTasks = new LinkedList<>();
        Task task = taskService.createTaskQuery().taskId(query.getId()).singleResult();
        Map<String, BpmTaskModelEntity> allTaskMap = initLimitBackTaskModel(query, task);

        Map<String, FlowElement> flowElementMap = FlowableUtils.getAllFlowElement(task.getProcessDefinitionId());
        FlowElement targetFlowElement = flowElementMap.get(task.getTaskDefinitionKey());
        // 目标节点的上游节点集合
        List<UserTask> tasks = new LinkedList<>();
        Map<String, UserTask> taskMap = new HashMap<>(16);
        FlowableUtils.loopIncomingFlows(((FlowNode) targetFlowElement).getIncomingFlows(),
                tasks, taskMap, flowElementMap, query, false);

        /*tasks.forEach(item -> {
            if (allTaskMap.containsKey(item.getId())) {
                resultTasks.add(allTaskMap.get(item.getId()));
            }
        });*/
        allTaskMap.forEach((key,val)->{
            tasks.forEach(item -> {
                if (Objects.equals(key, item.getId())) {
                    resultTasks.add(val);
                }
            });
        });
        return resultTasks;
    }

    /**
     * 启动第一个任务(流程发起人对应的任务)
     *
     * @param processInstance 流程实例
     * @param variables       流程参数
     */
    @Override
    public Task startFirstTask(ProcessInstance processInstance, Map<String, Object> variables, String message) {
        // 给第一步申请人节点设置任务执行人和意见
        Task task = taskService.createTaskQuery().processInstanceId(processInstance.getProcessInstanceId()).singleResult();
        if (Objects.nonNull(task)) {
            String userIdStr = (String) variables.get(TaskConstants.PROCESS_INITIATOR);
            if (!StrUtil.equalsAny(task.getAssignee(), userIdStr)) {
                throw new RuntimeException("数据验证失败，该工作流第一个用户任务的指派人并非当前用户、或当前任务节点不在发起节点，不能执行该操作！");
            }
            if (StringUtils.isBlank(message)) {
                message = userIdStr + "发起流程申请";
            }
            taskService.addComment(task.getId(), processInstance.getProcessInstanceId(), BpmProcessInstanceResultEnum.APPROVE.getResult().toString(), message);
            // TODO 2023-02-01 跳级审批 还需要判定流程走向是否是原来的走向
            BpmTaskExt bpmTaskExt = taskExtMapper.selectListByTaskId(task.getId());
            if(this.isApproveSkipFlag(task.getId(), bpmTaskExt.getReturnTaskDefKey(), task.getProcessDefinitionId(), task.getTaskDefinitionKey())){
                changeActivity(task, bpmTaskExt.getReturnTaskDefKey(), variables);
                // 借用此字段，传参跳转的任务节点
                task.setDescription(bpmTaskExt.getReturnTaskDefKey());
            } else {
                // 2022-11-09：发起流程时将流转条件保存为全局变量；此处不设置流转条件变量
//             runtimeService.setVariablesLocal(task.getExecutionId(), variables);
                taskService.complete(task.getId());
                // 2022-02-01 删除历史节点：选择了驳回跳转，没有走原来的流程走向，需要删除原来走向上保留的节点
                if(org.apache.commons.lang3.StringUtils.isNotBlank(bpmTaskExt.getDelActivityIds())){
                    bpmProcessInstanceExtMapper.deleteRunActinstsByIds(Arrays.asList(bpmTaskExt.getDelActivityIds().split(",")));
                    bpmProcessInstanceExtMapper.deleteHisActinstsByIds(Arrays.asList(bpmTaskExt.getDelActivityIds().split(",")));
                }
                // 借用此字段，传参跳转的任务节点
                task.setDescription(null);
            }

            // 更新任务拓展表为通过
            taskExtMapper.updateByTaskId(new BpmTaskExt().setTaskid(task.getId()).setResult(BpmProcessInstanceResultEnum.APPROVE.getResult()).setReason(message).setTaskDataType("INITIATOR").setEndtime(new Date()));
        }
        return task;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskReturn(CurrentUser currentUser, BpmTaskReturnReqVO reqVO) {
        if (StringUtils.isEmpty(reqVO.getTargetKey())) {
            BpmBusinessException.createByErrorMsg("跳转节点为空！");
        }
        // 当前任务 task
       // Task task = taskService.createTaskQuery().taskId(reqVO.getId()).singleResult();
        TaskEntity taskEntity = (TaskEntity) taskService.createTaskQuery().taskId(reqVO.getId()).singleResult();
        Map<String, Object> variables = taskService.getVariables(reqVO.getId());
        if (ObjectUtil.isNull(taskEntity)) {
            BpmBusinessException.createByErrorMsg("获取任务信息异常！");
        }
        if (taskEntity.isSuspended()) {
            BpmBusinessException.createByErrorMsg("任务处于挂起状态");
        }

        // TODO 2023-02-01 流程审批跳级标识
        boolean approveSkipFlag = Objects.equals(ProcessConstants.APPROVE_WAY_SKIP , reqVO.getApproveWay());
        // 设置工作流的用户
        FlowableUtils.setAuthenticatedUserId(currentUser.getHumanCode());
        //2.设置审批人
        taskEntity.setAssignee(currentUser.getHumanCode());
        taskService.saveTask(taskEntity);
        //3.添加驳回意见
        taskService.addComment(taskEntity.getId(), taskEntity.getProcessInstanceId(), BpmProcessInstanceResultEnum.RETURN.getResult().toString(), reqVO.getReason());

        //4.处理发起人节点
        FlowNode distActivity = bpmModelService.findFlowNodeByActivityId(taskEntity.getProcessDefinitionId(), reqVO.getTargetKey());
        QueryWrapper<BpmTaskExt> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("TASKDEFKEY", distActivity.getId()).eq("processinstanceid",taskEntity.getProcessInstanceId());
        List<BpmTaskExt> bpmTaskExts = taskExtMapper.selectList(queryWrapper);
        BpmTaskExt taskExt = new BpmTaskExt();
        if (bpmTaskExts.size() > 0) {
            taskExt = bpmTaskExts.get(0);
        }
        if (distActivity != null) {
            if (ProcessConstants.FLOW_SUBMITTER.equals(distActivity.getName()) || "INITIATOR".equals(taskExt.getTaskDataType())) {
                ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(taskEntity.getProcessInstanceId()).singleResult();
                runtimeService.setVariable(taskEntity.getProcessInstanceId(), TaskConstants.PROCESS_INITIATOR, processInstance.getStartUserId());
            }
        }
        //5.删除节点
        List<String> delActivityIds = deleteActivity(reqVO.getTargetKey(), taskEntity, approveSkipFlag);

        //6.流程跳转
        Map<String, Object> changeActivityMap = changeActivity(taskEntity, reqVO.getTargetKey(), variables);

        /**
         * 驳回后的审批方式: 默认是逐级审批的、直接到驳回操作的节点
         * 学校目前的需求是驳回之后的节点审批后，直接返回到驳回者（不用在经过中间的审批节点，
         * 比如第5个任务节点进行驳回到第1个任务节点，第1个任务节点审批或修改之后直接推送到第5个任务节点）
         * add by 2023-02-01
         */
        if(approveSkipFlag){
            // 获取创建的驳回节点任务：在此处保存被驳回节点（即操作节点）的信息
            List<Task> taskList = taskService.createTaskQuery().taskDefinitionKey(reqVO.getTargetKey()).processInstanceId(taskEntity.getProcessInstanceId()).list();
            List<BpmTaskExt> bpmTaskExtDOs = taskExtMapper.selectListByTaskIds(convertSet(taskList, Task::getId));
            RedisUtil redisUtil = SpringUtils.getObject(RedisUtil.class);
            List<String> returnTaskDefKey = (List<String>) changeActivityMap.get("returnTaskDefKey");
            bpmTaskExtDOs.forEach(item ->{
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(returnTaskDefKey)) {
                    item.setReturnTaskDefKey(StringUtils.join(returnTaskDefKey, ","));
                } else {
                    item.setReturnTaskDefKey(taskEntity.getTaskDefinitionKey());
                }
                if(CollUtil.isNotEmpty(delActivityIds)){
                    item.setDelActivityIds(StringUtils.join(delActivityIds, ","));
                }
            });
            saveOrUpdateBatch(bpmTaskExtDOs);
        }
        // 更新任务扩展表
        taskExtMapper.updateByTaskId(new BpmTaskExt().setTaskid(taskEntity.getId()).setResult(BpmProcessInstanceResultEnum.RETURN.getResult()).setReason(reqVO.getReason()).setEndtime(new Date()));
        // 更新当前节点的其他任务扩展数据
        updateByTaskidAndResult(taskEntity, BpmProcessInstanceResultEnum.RETURN);
        //更新流程实例表单
        BpmProcessInstanceExt instanceExt = bpmProcessInstanceExtMapper.selectByProcessInstanceId(taskEntity.getProcessInstanceId());
        Map<String, Object> map = new HashMap<>();
        map.put("flow.reason", reqVO.getReason());
        reqVO.formatFormVariables(map);
        instanceExt.setFormVariables(reqVO.getFormVariables());
        bpmProcessInstanceExtMapper.updateByProcessInstanceId(instanceExt);
        //删除bpmTaskExt
        taskExt = new BpmTaskExt();
        taskExt.setTaskid(taskEntity.getId());
        this.deleteTaskExt(taskExt);
        // 保存表单变量的值
        saveFormFieldValueData(instanceExt);
        // 清理
        FlowableUtils.clearAuthenticatedUserId();

        BpmnModel bpmnModel = repositoryService.getBpmnModel(taskEntity.getProcessDefinitionId());
        // 设置下级审批人
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(reqVO.getSelectNextUserList())) {
            BpmTaskApproveReqVO approveReqVO = new BpmTaskApproveReqVO();
            approveReqVO.setInstanceId(taskEntity.getProcessInstanceId());
            approveReqVO.setDefinitionId(taskEntity.getProcessDefinitionId());
            approveReqVO.setDefinitionKey(reqVO.getTargetKey());
            approveReqVO.setReturnTaskDefKey(reqVO.getTargetKey());
            designateNextApprover(reqVO.getSelectNextUserList(), approveReqVO, bpmnModel);
        }
        ProcessInstance instance = processInstanceService.getProcessInstance(taskEntity.getProcessInstanceId());
        this.pushMsgToNextTaskAssign(instanceExt.getStartUserId(), instance, reqVO.getSelectNextUserList(), bpmnModel, taskEntity);
    }

    /**
     * 保存表单变量的值
     */
    protected void saveFormFieldValueData(BpmProcessInstanceExt instanceExt) {
        String humancode = instanceExt.getStartUserId();
        Map<String, Object> formvariables = instanceExt.getFormVariables();
        String recordId = instanceExt.getProcessInstanceId();
        String projectId = instanceExt.getProcessDefinitionId();
        // 2023-01-03 保存表单变量的值
        if(formvariables != null && formvariables.size() > 0){
            iBpmFormFieldValueService.saveFormFieldValueData(humancode, formvariables, recordId, projectId);
        }
    }

    /**
     * 删除跳转的历史节点信息
     * @param disActivityId     跳转的节点id
     * @param taskEntity 当前任务节点
     * @param approveSkipFlag
     */
    protected List<String> deleteActivity(String disActivityId, TaskEntity taskEntity, boolean approveSkipFlag) {
        // 流程实例id
        String processInstanceId = taskEntity.getProcessInstanceId();
        // 当前节点id
        String taskDefinitionKey = taskEntity.getTaskDefinitionKey();
        String tableName = managementService.getTableName(ActivityInstanceEntity.class);
        String sql = "select t.* from " + tableName + " t where t.PROC_INST_ID_=#{processInstanceId} and t.ACT_ID_ = #{disActivityId} " + " order by t.END_TIME_ ASC";
        List<ActivityInstance> disActivities = runtimeService.createNativeActivityInstanceQuery().sql(sql).parameter("processInstanceId", processInstanceId).parameter("disActivityId", disActivityId).list();
        List<String> delActivityIds = new ArrayList<>();    // 如果驳回后，流程走向改变了，删除跳转驳回时保留的记录
        //删除运行时和历史节点信息
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(disActivities)) {
            ActivityInstance activityInstance = disActivities.get(0);
            //TODO 并行会误删其他节点
            sql = "select t.* from " + tableName + " t where t.PROC_INST_ID_=#{processInstanceId} and (t.END_TIME_ >= #{endTime} or t.END_TIME_ is null)";
            List<ActivityInstance> datas = runtimeService.createNativeActivityInstanceQuery().sql(sql).parameter("processInstanceId", processInstanceId).parameter("endTime", activityInstance.getEndTime()).list();
            List<String> runActivityIds = new ArrayList<>();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(datas)) {
                if(approveSkipFlag){
                    // TODO 如果跳级，则只删除当前节点和跳转的的节点；历史节点只删除跳转的节点
                    List<String> hisActivityIds = new ArrayList<>();
                    datas.forEach(ai -> {
                        if (Objects.equals(ai.getActivityId(), disActivityId) || Objects.equals(ai.getActivityId(), taskDefinitionKey)) {
                            runActivityIds.add(ai.getId());
                        }
                        if (Objects.equals(ai.getActivityId(), disActivityId)) {
                            hisActivityIds.add(ai.getId());
                        }
                        delActivityIds.add(ai.getId());
                    });
                    if(org.apache.commons.collections.CollectionUtils.isNotEmpty(runActivityIds)){
                        bpmProcessInstanceExtMapper.deleteRunActinstsByIds(runActivityIds);
                    }
                    if(org.apache.commons.collections.CollectionUtils.isNotEmpty(hisActivityIds)){
                        bpmProcessInstanceExtMapper.deleteHisActinstsByIds(hisActivityIds);  // 流程图上回显数据的话，不删除历史记录
                    }
                } else {

                    datas.forEach(ai -> runActivityIds.add(ai.getId()));
                    bpmProcessInstanceExtMapper.deleteRunActinstsByIds(runActivityIds);
                    bpmProcessInstanceExtMapper.deleteHisActinstsByIds(runActivityIds);
                }
            }
        }
        return delActivityIds;
    }

    @Override
    public void updateTaskExt(FlowableMultiInstanceActivityCompletedEvent event) {
        String activityType = event.getActivityType();
        if (Objects.equals(BpmnXMLConstants.ELEMENT_TASK_USER, activityType) && !Objects.equals(ProcessConstants.FLOW_SUBMITTER, event.getActivityName())) {
            String processInstanceId = event.getProcessInstanceId();    // 流程实例ID
            String activityId = event.getActivityId();  // 节点实例ID
            HistoricTaskInstanceQuery taskQuery = historyService.createHistoricTaskInstanceQuery()
//                    .finished()
                    .processInstanceId(processInstanceId).taskDefinitionKey(activityId);
            List<HistoricTaskInstance> taskList = taskQuery.list();
            List<BpmTaskExt> taskExtList = taskExtMapper.selectListByTaskIds(taskList.stream().map(obj -> obj.getId()).collect(Collectors.toList()));
            BpmTaskExt bpmTaskExt = taskExtList.stream().filter(obj -> obj.getResult() != 1 && obj.getEndtime() != null).findFirst().orElse(null);
            if (bpmTaskExt != null) {
                Date time = Calendar.getInstance().getTime();
                // 获取当前任务的处理状态
                for (BpmTaskExt obj : taskExtList) {
                    if (obj.getEndtime() == null) {
                        BpmTaskExt taskExtDO2 = new BpmTaskExt().setTaskid(obj.getTaskid()).setEndtime(time).setResult(bpmTaskExt.getResult());
                        taskExtMapper.updateByTaskidAndResultPROCESS(taskExtDO2);
                    }
                }
            }
        }
    }

    /**
     * 得到下一个节点列表
     * 当前任务还未审批时可以使用此方法,审批过后的ru_task表已无数据了，
     * 需要使用getNextNodesByProcessDefinitionIdAndTaskDefinitionKey方法来找下个节点
     *
     * @param reqVO 申请签证官
     * @return {@link List}<{@link FlowNodeVo}>
     */
    @Override
    public List<FlowNodeVo> getNextNodesByTaskId(BpmTaskApproveReqVO reqVO) {
        Task task = taskService.createTaskQuery().taskId(reqVO.getId()).singleResult();
        /*
         * 当前任务还未审批,可以得到表单变量
         * 审批页面因为有当前task信息，并且在审批前就调用了这个接口,所以可以查询到节点
         * 发起流程那里因为还没有task表信息,所以使用了getNextNodesByProcessDefinitionIdAndTaskDefinitionKey
         * */
        Map<String, Object> variables = taskService.getVariables(task.getId());
        List<UserTask> nextUserTasks = FindNextNodeUtil.getNextUserTasks(repositoryService, task, variables);
        List<FlowNodeVo> backNods = new ArrayList<>();
        nextUserTasks.forEach(userTask -> {
            String extendData = userTask.getAttributeValue(BpmnXMLConstants.FLOWABLE_EXTENSIONS_NAMESPACE, "extendData");
            List<JSONObject> jsonObjects = new ArrayList<>();
            // extendData需要判空
            boolean ischangeuser = false;
            if (StringUtils.isNotBlank(extendData)) {
                jsonObjects = JSON.parseArray(extendData, JSONObject.class);
                for (JSONObject jsonObject : jsonObjects) {
                    if (jsonObject.containsKey(TaskConstants.TASK_IS_CHANGE_NEXT_USER)) {
                        ischangeuser = jsonObject.getBoolean(TaskConstants.TASK_IS_CHANGE_NEXT_USER);       //允许手动选择办理人 true
                    }
                }
            }
            if (ischangeuser) {
                FlowNodeVo node = new FlowNodeVo();
                node.setExtendData(jsonObjects);
                node.setId(userTask.getId());
                node.setName(userTask.getName());
//            node.setUserName(userTask.getAssignee());
                UserTaskFormVO userTaskFormVO = new UserTaskFormVO();
                userTaskFormVO.setAssignee(userTask.getAssignee());
                userTaskFormVO.setCandidateUsers(userTask.getCandidateUsers().stream().collect(Collectors.joining(",")));
                userTaskFormVO.setCandidateGroups(userTask.getCandidateGroups().stream().collect(Collectors.joining(",")));
                String dataType = userTask.getAttributeValue(BpmnXMLConstants.FLOWABLE_EXTENSIONS_NAMESPACE, "dataType");
                userTaskFormVO.setDataType(dataType);
                node.setUserTaskForm(userTaskFormVO);
                backNods.add(node);
            }

        });
        return backNods;
    }

    /**
     * 得到下一个节点（发起流程时使用）
     *
     * @param reqVO 申请签证官
     * @return {@link List}<{@link FlowNodeVo}>
     */
    @Override
    public List<FlowNodeVo> getNextNodesByProcessDefinitionIdAndTaskDefinitionKey(BpmTaskApproveReqVO reqVO) {
        /*
         * returnTaskDefKey不为空，表示某个节点操作后，能确定下一任务节点的情况；
         * 比如：
         * 1.驳回后，发起人修改、或者审批人重新审批后，直接跳转到驳回操作节点，选择该节点办理人；
         * 2.驳回到指定节点时，选择该节点办理人；
         *
         * */

        List<UserTask> nextUserTasks = new ArrayList<>();
        if(org.apache.commons.lang3.StringUtils.isBlank(reqVO.getReturnTaskDefKey())){
            // 正常发起、审批操作后流程，选择下一节点办理人人
            //根据taskId查询历史任务和变量
//            Map<String, Object> map = taskService.getVariables(reqVO.getId());
            Map<String, Object> map = reqVO.getExpressionVariables();
            nextUserTasks = FindNextNodeUtil.getNextUserTasks(repositoryService, reqVO.getDefinitionId(), reqVO.getDefinitionKey(), map);
        } else {
            // 驳回后，发起人修改、或者审批人重新审批后：选择下一审批人；
            // 指定驳回到某节点，该节点要选择的审批人
            BpmnModel bpmnModel = repositoryService.getBpmnModel(reqVO.getDefinitionId());
            // 获取要驳回的节点
            String[] split = reqVO.getReturnTaskDefKey().split(",");
            for (int i = 0; i < split.length; i++) {
                UserTask userTask = FlowableUtils.getBpmnModelUserTaskByTaskKey(bpmnModel, split[i]);
                if(userTask != null){
                    if(Objects.equals("${initiator}", userTask.getAssignee())){
                        // 流转到发起人节点，不需要选择办理人
                        return emptyList();
                    }
                    nextUserTasks.add(userTask);
                }
            }

        }
        List<FlowNodeVo> backNods = new ArrayList<>();
        nextUserTasks.forEach(userTask -> {
            String extendData = userTask.getAttributeValue(BpmnXMLConstants.FLOWABLE_EXTENSIONS_NAMESPACE, "extendData");
//            boolean ischangeuser = false;
            List<JSONObject> jsonObjects = new ArrayList<>();
            if (StringUtils.isNotBlank(extendData)) {
                jsonObjects = JSON.parseArray(extendData, JSONObject.class);
                /*for (JSONObject jsonObject : jsonObjects) {
                    if (jsonObject.containsKey(TaskConstants.TASK_IS_CHANGE_NEXT_USER)) {
                        ischangeuser = jsonObject.getBoolean(TaskConstants.TASK_IS_CHANGE_NEXT_USER);       //允许参与手动选择办理人 true
                    }
                }*/
            }
            /*
            * 过滤放在getNextNodesByProcessDefinitionIdAndTaskDefinitionKey接口
            * 取回等调用此接口时无需判断ischangeuser属性。
            * */
//            if (ischangeuser) {
                FlowNodeVo node = new FlowNodeVo();
                node.setExtendData(jsonObjects);
                node.setId(userTask.getId());
                node.setName(userTask.getName());
//            node.setUserName(userTask.getAssignee());
                UserTaskFormVO userTaskFormVO = new UserTaskFormVO();
                userTaskFormVO.setAssignee(userTask.getAssignee());
                userTaskFormVO.setCandidateUsers(userTask.getCandidateUsers().stream().collect(Collectors.joining(",")));
                userTaskFormVO.setCandidateGroups(userTask.getCandidateGroups().stream().collect(Collectors.joining(",")));
                String dataType = userTask.getAttributeValue(BpmnXMLConstants.FLOWABLE_EXTENSIONS_NAMESPACE, "dataType");
                userTaskFormVO.setDataType(dataType);
                node.setUserTaskForm(userTaskFormVO);
                backNods.add(node);
//            }

        });
        return backNods;
    }

    @Override
    public void setNextApprover(BpmTaskApproveReqVO reqVO) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(reqVO.getDefinitionId());
        bpmTaskService.designateNextApprover(reqVO.getSelectNextUserList(), reqVO, bpmnModel);
    }

    @Override
    public Page<BpmTaskDonePageItemRespVO> getOwerTaskPage(CurrentUser currentUser, BpmTaskTodoDonePageReqVO pageVO) {
        HistoricTaskInstanceQuery taskQuery = historyService.createHistoricTaskInstanceQuery().taskOwner(currentUser.getHumanCode()).orderByHistoricTaskInstanceStartTime().desc().orderByHistoricActivityInstanceId().desc(); // 审批时间倒序
        return getBpmTaskDonePageItemRespVOPage(pageVO, taskQuery);
    }

    @Override
    public Long getOwerTaskCount(CurrentUser currentUser, BpmTaskTodoDonePageReqVO pageVO) {
        HistoricTaskInstanceQuery taskQuery = historyService.createHistoricTaskInstanceQuery().taskOwner(currentUser.getHumanCode()).orderByHistoricTaskInstanceEndTime().desc(); // 审批时间倒序
        setDoneTaskQuery(pageVO, taskQuery);
        return taskQuery.count();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskRetrieve(CurrentUser currentUser, BpmTaskReturnReqVO reqVO) {
        //TODO 查询下级是否审批
        BpmTaskApproveReqVO taskApproveReqVO = new BpmTaskApproveReqVO();
        //点击取回的task,已审核故在hi_task里。即要退回的目标key在此数据上
        HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(reqVO.getId()).singleResult();

        reqVO.setTargetKey(historicTaskInstance.getTaskDefinitionKey());
        taskApproveReqVO.setDefinitionId(historicTaskInstance.getProcessDefinitionId());
        taskApproveReqVO.setDefinitionKey(historicTaskInstance.getTaskDefinitionKey());

        Map<String, Object> variablesMap = new HashMap<>();
        List<Task> list = taskService.createTaskQuery().processInstanceId(reqVO.getProcessInstanceId()).list();
        list.forEach(t->{
            if (Objects.equals(t.getTaskDefinitionKey(), historicTaskInstance.getTaskDefinitionKey())) {
                BpmBusinessException.createByErrorMsg("当前任务已取回,无需再次取回！");
            }
        });
        if (list.size() > 0) {
            Task task = list.get(0);
            variablesMap = taskService.getVariables(task.getId());
        } else {
            BpmBusinessException.createByErrorMsg("任务已结束无法取回！");
        }
        taskApproveReqVO.setExpressionVariables(variablesMap);
        //下级节点
        List<FlowNodeVo> nextNodes = getNextNodesByProcessDefinitionIdAndTaskDefinitionKey(taskApproveReqVO);
        //根据taskDefinitionKeys查询下级是否已办理
        Set<String> taskDefinitionKeys = nextNodes.stream().map(FlowNodeVo::getId).collect(Collectors.toSet());
        long count = historyService.createHistoricTaskInstanceQuery().finished() // 已完成并且删除原因为空的
                .taskWithoutDeleteReason()
                .processInstanceId(reqVO.getProcessInstanceId())
                .taskCompletedAfter(historicTaskInstance.getEndTime()) // 在当前操作节点办理时间后的数据
                .taskDefinitionKeys(taskDefinitionKeys).count();
        if (count > 0) {   //已审批
            BpmBusinessException.createByErrorMsg("下级已审批无法取回！");
        }
        //根据下级节点查询ru_task
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(reqVO.getProcessInstanceId()).taskDefinitionKeys(taskDefinitionKeys).list();
        taskList.forEach(FlowableUtils::deleteRetrieveTaskLinkedData);
        //TODO 调用驳回流程
        if (taskList.size() > 0) {
            // 设置工作流的用户
            FlowableUtils.setAuthenticatedUserId(currentUser.getHumanCode());
            Task t = taskList.get(0);
            // 添加取回意见
            taskService.addComment(t.getId(), t.getProcessInstanceId(), BpmProcessInstanceResultEnum.RETRIEVE.getResult().toString(), BpmProcessInstanceResultEnum.RETRIEVE.getDesc());
            // 更新任务扩展表
            taskExtMapper.updateByTaskId(new BpmTaskExt().setTaskid(t.getId()).setResult(BpmProcessInstanceResultEnum.RETRIEVE.getResult()).setReason(reqVO.getReason()).setEndtime(new Date()));
            Map<String, Object> variables = taskService.getVariables(t.getId());
            changeActivity(t, reqVO.getTargetKey(), variables);
            //删除历史Task数据
//            historyService.deleteHistoricTaskInstance(historicTaskInstance.getId());
//            FlowableUtils.deleteRetrieveTaskLinkedData(historicTaskInstance);
            taskList.forEach(tt->{
                BpmTaskExt taskExt = new BpmTaskExt();
                taskExt.setTaskid(tt.getId());
                deleteTaskExt(taskExt);
            });
            // 清理
            FlowableUtils.clearAuthenticatedUserId();
            //TODO 修改节点的审批人为取回人自己
            BpmnModel bpmnModel = repositoryService.getBpmnModel(t.getProcessDefinitionId());
            BpmTaskApproveReqVO approveReqVO = new BpmTaskApproveReqVO();
            approveReqVO.setInstanceId(t.getProcessInstanceId());
            approveReqVO.setDefinitionId(t.getProcessDefinitionId());
            approveReqVO.setDefinitionKey(historicTaskInstance.getTaskDefinitionKey());
            approveReqVO.setReturnTaskDefKey("");
            List<SelectNextUserVO> selectNextUserList = new ArrayList<>();
            SelectNextUserVO userVO = new SelectNextUserVO();
            userVO.setTaskKey(historicTaskInstance.getTaskDefinitionKey());
            UserTaskFormVO formVO = new UserTaskFormVO();
            formVO.setAssignee(currentUser.getHumanCode());
            formVO.setDataType("USERS");
            userVO.setUserTaskFormVO(formVO);
            selectNextUserList.add(userVO);
            approveReqVO.setRetrieveTask(true);
            bpmTaskService.designateNextApprover(selectNextUserList, approveReqVO, bpmnModel);
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void retrieveTaskForInitiate(CurrentUser currentUser, BpmTaskReturnReqVO reqVO) {
        List<String> processInstanceIds = new ArrayList<>();
        processInstanceIds.add(reqVO.getProcessInstanceId());
        // 按创建时间降序排序的发起人任务，会取到最新的操作时间
        List<BpmTaskExt> bpmTaskExts = getFirstTasks(processInstanceIds);
        if (bpmTaskExts.size() > 0) {
            BpmTaskExt taskExt = bpmTaskExts.get(0);    //发起人任务
            //查询当前ru_task,判断是否已取回过了
            List<Task> tasks = taskService.createTaskQuery().processInstanceId(reqVO.getProcessInstanceId()).taskDefinitionKey(taskExt.getTaskDefKey()).list();
            tasks.forEach(t->{
                if (Objects.equals(t.getTaskDefinitionKey(), taskExt.getTaskDefKey())) {
                    BpmBusinessException.createByErrorMsg("当前任务已取回,无需再次取回！");
                }
            });

            // 根据发起人节点查找下个节点，
            BpmTaskApproveReqVO taskApproveReqVO = new BpmTaskApproveReqVO();
            taskApproveReqVO.setDefinitionId(taskExt.getProcessdefinitionid());
            taskApproveReqVO.setDefinitionKey(taskExt.getTaskDefKey());
            //下级节点
            Map<String, Object> variablesMap = taskService.getVariables(reqVO.getId());

            taskApproveReqVO.setExpressionVariables(variablesMap);
            List<FlowNodeVo> nextNodes = getNextNodesByProcessDefinitionIdAndTaskDefinitionKey(taskApproveReqVO);
            //根据taskDefinitionKeys查询下级是否已办理
            Set<String> taskDefinitionKeys = nextNodes.stream().map(FlowNodeVo::getId).collect(Collectors.toSet());
            // 判断下个节点否是已审批
            long count = historyService.createHistoricTaskInstanceQuery().finished() // 已完成并且删除原因为空
                    .taskWithoutDeleteReason()
                    .processInstanceId(reqVO.getProcessInstanceId())
                    .taskCompletedAfter(taskExt.getCreateTime())    // 在发起人节点办理时间后数据
                    .taskDefinitionKeys(taskDefinitionKeys).count();
            if (count > 0) {   //已审批
                BpmBusinessException.createByErrorMsg("下级已审批无法取回！");
            }

            //根据下级节点查询ru_task
            List<Task> taskList = taskService.createTaskQuery().processInstanceId(reqVO.getProcessInstanceId()).taskDefinitionKeys(taskDefinitionKeys).list();
            taskList.forEach(FlowableUtils::deleteRetrieveTaskLinkedData);
            //TODO 调用驳回流程
            if (taskList.size() > 0) {
                // 设置工作流的用户
                FlowableUtils.setAuthenticatedUserId(currentUser.getHumanCode());
                Task t = taskList.get(0);
                // 添加取回意见
                taskService.addComment(t.getId(), t.getProcessInstanceId(), BpmProcessInstanceResultEnum.RETRIEVE.getResult().toString(), BpmProcessInstanceResultEnum.RETRIEVE.getDesc());
                // 更新任务扩展表
                taskExtMapper.updateByTaskId(new BpmTaskExt().setTaskid(t.getId()).setResult(BpmProcessInstanceResultEnum.RETRIEVE.getResult()).setReason(reqVO.getReason()).setEndtime(new Date()));
                Map<String, Object> variables = taskService.getVariables(t.getId());
                changeActivity(t, taskExt.getTaskDefKey(), variables);
                //删除历史Task数据
//                historyService.deleteHistoricTaskInstance(reqVO.getProcessInstanceId());
//            FlowableUtils.deleteRetrieveTaskLinkedData(historicTaskInstance);
                taskList.forEach(tt->{
                    BpmTaskExt taskExt1 = new BpmTaskExt();
                    taskExt1.setTaskid(tt.getId());
                    deleteTaskExt(taskExt1);
                });
                // 清理
                FlowableUtils.clearAuthenticatedUserId();
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void forceRecallTask(CurrentUser currentUser,BpmTaskApproveReqVO reqVO) {
        List<BpmTaskExt> bpmTaskExts = bpmTaskService.getFirstTasks(ListUtil.toList(reqVO.getInstanceId()));
        if (bpmTaskExts.size() > 0) {
            BpmTaskExt taskExt = bpmTaskExts.get(0);
            BpmTaskReturnReqVO returnReqVO = new BpmTaskReturnReqVO();
            returnReqVO.setTargetKey(taskExt.getTaskDefKey());
            returnReqVO.setId(reqVO.getId());
            returnReqVO.setReason("强制撤回");
            taskReturn(currentUser, returnReqVO);
            BpmProcessInstanceExt processInstanceExt = bpmProcessInstanceExtMapper.selectByProcessInstanceId(reqVO.getInstanceId());
            processInstanceExt.setStatus(BpmProcessInstanceStatusEnum.RECALL.getStatus());
            processInstanceExt.setResult(BpmProcessInstanceResultEnum.PROCESS.getResult());
            processInstanceService.saveOrUpdate(processInstanceExt);
        }
    }

    @Override
    public void setDueDate(String taskId, Date dueDate) {
        taskService.setDueDate(taskId, dueDate);
    }

    @Override
    public UserTaskModelDTO getUserTaskModelDto(BpmTaskModelQuery query) {
        UserTaskModelDTO dto = new UserTaskModelDTO();
        if (org.apache.commons.lang3.StringUtils.isBlank(query.getDefineId())) {
            BpmBusinessException.createByErrorMsg("param defineId is null");
        }
        List<BpmTaskModelEntity> resultUserTaskModels = new ArrayList<>(4);
        ProcessDefinition processDefinition = repositoryService
                .createProcessDefinitionQuery()
                .processDefinitionId(query.getDefineId()).singleResult();
        if (processDefinition == null) {
            return dto;
        }
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
        Process process = bpmnModel.getProcesses().get(0);
        dto.setProcess(process);
        // 先查询出所有符合条件的用户任务
        List<BpmTaskMinModelEntity> allUserTasks = FlowableUtils.queryMinUserTasks(query, process, processDefinition);
        if (org.springframework.util.CollectionUtils.isEmpty(allUserTasks)) {
            return dto;
        }

        // 查询出所有并行网关中的用户任务
        Map<String, ParallelGatwayDTO> forkGatewayMap = FlowableUtils.getAllParallelGatewayUserTask(query, processDefinition);

        // 并行网关的用户任务节点
        Map<String, BpmTaskModelEntity> pGatewayUserTaskModelsMap = new LinkedHashMap<>(4);
        forkGatewayMap.forEach((k, v) -> pGatewayUserTaskModelsMap.putAll(v.getUserTaskModels()));

        // 非并行网关的用户任务节点
        List<BpmTaskModelEntity> aloneUserTaskModels = new ArrayList<>(4);
        allUserTasks.stream()
                .filter(item -> !pGatewayUserTaskModelsMap.containsKey(item.getTaskDefKey()))
                .collect(Collectors.toList())
                .forEach(item -> aloneUserTaskModels.add((BpmTaskModelEntity) item));

        // 合并两个结果集
        resultUserTaskModels.addAll(aloneUserTaskModels);
        resultUserTaskModels.addAll(new ArrayList<>(pGatewayUserTaskModelsMap.values()));
        dto.setAllUserTaskModels(resultUserTaskModels);
        dto.setAllForkGatewayMap(forkGatewayMap);
        return dto;
    }

    @Override
    public List<BpmTaskModelEntity> listUserTaskModels(BpmTaskModelQuery query) {
        return getUserTaskModelDto(query).getAllUserTaskModels();
    }


    public Map<String, BpmTaskModelEntity> initLimitBackTaskModel(BpmTaskApproveReqVO query, Task task) {
        // 历史用户任务
        Set<String> taskKeysSet = historyService.createHistoricTaskInstanceQuery()
                .finished()
                .processInstanceId(task.getProcessInstanceId())
                .list().stream().map(HistoricTaskInstance::getTaskDefinitionKey).collect(Collectors.toSet());
        BpmTaskModelQuery taskModelQuery = new BpmTaskModelQuery();
        taskModelQuery.setDefineId(task.getProcessDefinitionId());
        List<BpmTaskModelEntity> allTasks = listUserTaskModels(taskModelQuery);
        if (org.springframework.util.CollectionUtils.isEmpty(allTasks)) {
            return new HashMap<>(2);
        }

        return allTasks.stream().filter(item -> taskKeysSet.contains(item.getTaskDefKey()))
                .collect(Collectors.toMap(BpmTaskModelEntity::getTaskDefKey, a -> a, (k1, k2) -> k1));
    }

    @Override
    public List<FlowNodeVo> getNextFlowNodeVos(BpmTaskApproveReqVO reqVO, BpmnModel bpmnModel) {
        boolean changeuser = false;
        if (org.apache.commons.lang3.StringUtils.isEmpty(reqVO.getReturnTaskDefKey())) {     //非驳回跳转时
            FlowElement flowElement = FlowableUtils.findFlowNode(reqVO.getDefinitionKey(), bpmnModel, "now");
            String currentExtData = flowElement.getAttributeValue(BpmnXMLConstants.FLOWABLE_EXTENSIONS_NAMESPACE, "extendData");
            if (StringUtils.isNotBlank(currentExtData)) {
                List<JSONObject> jsonObjects = JSON.parseArray(currentExtData, JSONObject.class);
                for (JSONObject jsonObject : jsonObjects) {
                    if (jsonObject.containsKey(TaskConstants.TASK_CHANGE_NEXT_USER)) {
                        changeuser = jsonObject.getBoolean(TaskConstants.TASK_CHANGE_NEXT_USER);       //允许手动选择办理人 true
                    }
                }
            }
        } else {        //驳回跳转时考虑当前节点允许选择办理人开关,查询的指定ReturnTaskDefKey的节点(指定驳回的节点)放入flowNodeVoList后再判断允许参与手动选择办理人开关
            changeuser = true;
        }

        List<FlowNodeVo> flowNodeVoList = new ArrayList<>();
        if (changeuser) {
            flowNodeVoList = getNextNodesByProcessDefinitionIdAndTaskDefinitionKey(reqVO);
            Iterator<FlowNodeVo> iterator = flowNodeVoList.iterator();
            while (iterator.hasNext()) {
                FlowNodeVo next = iterator.next();
                List<JSONObject> extendData = next.getExtendData();
                boolean ischangeuser = false;
                for (JSONObject jsonObject : extendData) {
                    if (jsonObject.containsKey(TaskConstants.TASK_IS_CHANGE_NEXT_USER)) {
                        ischangeuser = jsonObject.getBoolean(TaskConstants.TASK_IS_CHANGE_NEXT_USER);       //允许参与手动选择办理人 true
                    }
                }
                if (!ischangeuser) {
                    iterator.remove();
                }
            }
        }
        return flowNodeVoList;
    }

    @Override
    public String getApproveInfoByProcessInstanceIdAndTaskDefinitionKey(String processInstanceId, String taskDefinitionKey, String format) {
        String spxx = "";
        // 获得任务列表
        List<HistoricTaskInstance> tasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId).taskDefinitionKey(taskDefinitionKey).orderByHistoricTaskInstanceEndTime().asc() // 结束时间升序
                .list();
        if (CollUtil.isEmpty(tasks)) {
            return spxx;
        }
        // 获得 User Map
        Set<String> taskIds = convertSet(tasks, task -> task.getId());
        // 审批内容
        List<Comment> commentList = taskService.getProcessInstanceComments(processInstanceId);
        if (CollectionUtil.isEmpty(commentList)) {
            return spxx;
        }
        List<Comment> currentNodeCommentList = new ArrayList<>();
        for (Comment comment : commentList) {
            if (taskIds.contains(comment.getTaskId())) {
                currentNodeCommentList.add(comment);
            }
        }
        if (CollectionUtil.isEmpty(currentNodeCommentList)) {
            return spxx;
        }
        currentNodeCommentList.sort(Comparator.comparing(Comment :: getTime));
        Set<String> commentUserIds = convertSet(currentNodeCommentList, comment -> comment.getUserId());
        Map<String, AdminUserRespDTO> commentUserMap = adminUserApi.getUserMap(commentUserIds);
        Map<String, String> spjgMap = BpmProcessInstanceResultEnum.getMapData();
        format = StringUtils.isBlank(format) ? "yyyy-MM-dd" : format;
        String spxxTemplate = "审批结果：${spjg}  审批人：${spr}  审批意见：${spyj}  审批日期：${sprq} ";
        // 任务节点多人审批信息模板
        SytSysParam sysParam = paramService.get(ProcessConstants.BPM_MULTI_TASK_COMMENT_TEMPLATE);
        if (sysParam != null && StringUtils.isNotBlank(sysParam.getValue())) {
            spxxTemplate = sysParam.getValue();
        }
        for (Comment comment : currentNodeCommentList) {
            if (taskIds.contains(comment.getTaskId())) {
                AdminUserRespDTO adminUserRespDTO = commentUserMap.get(comment.getUserId());
                if (adminUserRespDTO != null) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("spjg", (spjgMap.get(comment.getType()) == null ? "" : spjgMap.get(comment.getType())));
                    map.put("spr", adminUserRespDTO.getHumanName());
                    map.put("spyj", comment.getFullMessage());
                    map.put("sprq", DateUtil.format(comment.getTime(), format));
                    spxx += PropertyPlaceholderHelperUtil.placeholderHelper("${", "}", map, spxxTemplate);
                    spxx += " \n";
                }
            }
        }
        return spxx;
    }
}
