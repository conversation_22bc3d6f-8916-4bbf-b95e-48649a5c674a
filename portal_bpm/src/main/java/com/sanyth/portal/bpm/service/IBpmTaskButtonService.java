package com.sanyth.portal.bpm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmTaskButton;
import com.sanyth.portal.core.base.BaseQuery;

import java.util.List;

/**
 * 流程按钮 服务接口
 * Created by ZWB on 2022-06-16.
 */
public interface IBpmTaskButtonService extends IService<BpmTaskButton> {
    Page<BpmTaskButton> queryPage(BaseQuery query);

    List<BpmTaskButton> queryList(BpmTaskButton button);
}
