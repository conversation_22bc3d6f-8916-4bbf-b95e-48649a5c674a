package com.sanyth.portal.bpm.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.bpm.mapper.BpmFlowListenerParamMapper;
import com.sanyth.portal.bpm.model.BpmFlowListenerParam;
import com.sanyth.portal.bpm.service.IBpmFlowListenerParamService;
import com.sanyth.portal.core.base.BaseQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 监听器参数 服务实现
 * Created by ZWB on 2022-07-12.
 */
@Service
public class BpmFlowListenerParamServiceImpl extends ServiceImpl<BpmFlowListenerParamMapper, BpmFlowListenerParam> implements IBpmFlowListenerParamService {

    @Override
    public Page<BpmFlowListenerParam> queryPage(BaseQuery query) {
        Page<BpmFlowListenerParam> page = new Page<>(query.getPage(), query.getPageSize());
        BpmFlowListenerParam obj = JSON.toJavaObject(query.getQueryParam(), BpmFlowListenerParam.class);
        Wrapper<BpmFlowListenerParam> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    private Wrapper<BpmFlowListenerParam> buildWrapper(BpmFlowListenerParam bpmFlowListenerParam){
        QueryWrapper<BpmFlowListenerParam> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(bpmFlowListenerParam.getName())) {
            wrapper.like("NAME", bpmFlowListenerParam.getName());
        }
        if (StringUtils.isNotBlank(bpmFlowListenerParam.getListenerid())) {
            wrapper.eq("LISTENERID", bpmFlowListenerParam.getListenerid());
        }
        if (StringUtils.isNotBlank(bpmFlowListenerParam.getParamType())) {
            wrapper.eq("PARAMTYPE", bpmFlowListenerParam.getParamType());
        }
        return wrapper;
    }
}
