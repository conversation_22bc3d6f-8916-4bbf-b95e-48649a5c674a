package com.sanyth.portal.bpm.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.bpm.mapper.BpmCommentTemplateMapper;
import com.sanyth.portal.bpm.mapper.BpmTaskExtMapper;
import com.sanyth.portal.bpm.model.BpmCommentTemplate;
import com.sanyth.portal.bpm.model.BpmTaskExt;
import com.sanyth.portal.bpm.service.IBpmCommentTemplateService;
import com.sanyth.portal.core.base.BaseQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 审批意见模板 服务实现
 * Created by WDL on 2022-11-11.
 */
@Service
public class BpmCommentTemplateServiceImpl extends ServiceImpl<BpmCommentTemplateMapper, BpmCommentTemplate> implements IBpmCommentTemplateService {

    @Resource
    private BpmTaskExtMapper taskExtMapper;

    @Override
    public Page<BpmCommentTemplate> queryPage(BaseQuery query) {
        Page<BpmCommentTemplate> page = new Page<>(query.getPage(), query.getPageSize());
        BpmCommentTemplate obj = JSON.toJavaObject(query.getQueryParam(), BpmCommentTemplate.class);
        Wrapper<BpmCommentTemplate> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public List<BpmCommentTemplate> queryList(BpmCommentTemplate bpmCommentTemplate) {
        Wrapper<BpmCommentTemplate> wrapper = buildWrapper(bpmCommentTemplate);
        return list(wrapper);
    }

    @Override
    public Long getCount(BpmCommentTemplate bpmCommentTemplate) {
        Wrapper<BpmCommentTemplate> wrapper = buildWrapper(bpmCommentTemplate);
        return count(wrapper);
    }

    @Override
    public void addMyComment(String taskId) {
        if(StringUtils.isNotBlank(taskId)) {
            BpmTaskExt bpmTaskExt = taskExtMapper.selectListByTaskId(taskId);
            if(bpmTaskExt == null){
                return;
            }
            if(StringUtils.isNotBlank(bpmTaskExt.getReason()) && StringUtils.isNotBlank(bpmTaskExt.getAssigneeuserid())){
                // 保存审批意见到个人模板
                BpmCommentTemplate bpmCommentTemplate = new BpmCommentTemplate();
                bpmCommentTemplate.setUserid(bpmTaskExt.getAssigneeuserid());
                bpmCommentTemplate.setReason(bpmTaskExt.getReason());
                Long count = this.getCount(bpmCommentTemplate);
                if (count == 0) {
                    bpmCommentTemplate.setSort(100);
                    this.save(bpmCommentTemplate);
                }
            }
        }
    }

    private Wrapper<BpmCommentTemplate> buildWrapper(BpmCommentTemplate bpmCommentTemplate){
        QueryWrapper<BpmCommentTemplate> wrapper = new QueryWrapper<>();
        // Query condition...
        if(StringUtils.isNotBlank(bpmCommentTemplate.getId())){
            wrapper.eq("ID", bpmCommentTemplate.getId());
        }
        if(StringUtils.isNotBlank(bpmCommentTemplate.getUserid())){
            wrapper.eq("USERID", bpmCommentTemplate.getUserid());
        }
        if(StringUtils.isNotBlank(bpmCommentTemplate.getReason())){
            wrapper.like("REASON", bpmCommentTemplate.getReason());
        }
        wrapper.orderByAsc("SORT");
        return wrapper;
    }
}
