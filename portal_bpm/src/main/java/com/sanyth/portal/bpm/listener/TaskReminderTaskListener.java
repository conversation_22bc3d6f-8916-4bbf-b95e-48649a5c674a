package com.sanyth.portal.bpm.listener;

import org.flowable.engine.ManagementService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 任务提醒监听
 * java类等实现JavaDelegate接口
 * <AUTHOR>
 * @date 2022/07/26
 */
@Component(value = "taskReminderTaskListener")
public class TaskReminderTaskListener implements JavaDelegate {

    @Resource
    private ManagementService managementService;

    @Override
    public void execute(DelegateExecution execution) {
        System.out.println("payne进入事件(Java服务)监听器环节提醒");
//        Date date = new Date();
//        managementService.executeCommand(new TimeOutJobCmd(execution.getProcessInstanceId(), execution.getCurrentFlowElement().getId(),
//                execution.getId(), date, "", 3, "R3/PT5S"));
    }

}
