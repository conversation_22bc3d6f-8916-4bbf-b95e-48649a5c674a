package com.sanyth.portal.bpm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.bpm.convert.BpmTaskAssignRuleConvert;
import com.sanyth.portal.bpm.mapper.BpmTaskAssignRuleMapper;
import com.sanyth.portal.bpm.model.BpmTaskAssignRule;
import com.sanyth.portal.bpm.service.BpmModelService;
import com.sanyth.portal.bpm.service.BpmProcessDefinitionService;
import com.sanyth.portal.bpm.service.IBpmTaskAssignRuleService;
import com.sanyth.portal.bpm.util.FlowableUtils;
import com.sanyth.portal.bpm.vo.BpmTaskAssignRuleCreateReqVO;
import com.sanyth.portal.bpm.vo.BpmTaskAssignRuleRespVO;
import com.sanyth.portal.bpm.vo.BpmTaskAssignRuleUpdateReqVO;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.util.collection.CollectionUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.UserTask;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.sanyth.portal.bpm.enums.ErrorCodeConstants.*;
import static com.sanyth.portal.bpm.util.ServiceExceptionUtil.exception;

/**
 * 任务分配的规则表 服务实现
 * Created by ZWB on 2022-05-16.
 */
@Service
public class BpmTaskAssignRuleServiceImpl extends ServiceImpl<BpmTaskAssignRuleMapper, BpmTaskAssignRule> implements IBpmTaskAssignRuleService {

    @Resource
    private BpmTaskAssignRuleMapper taskRuleMapper;
    @Resource
    @Lazy // 解决循环依赖
    private BpmModelService modelService;
    @Resource
    @Lazy // 解决循环依赖
    private BpmProcessDefinitionService processDefinitionService;
//    @Resource
//    private BpmUserGroupService userGroupService;
//    @Resource
//    private RoleApi roleApi;
//    @Resource
//    private DeptApi deptApi;
//    @Resource
//    private PostApi postApi;
//    @Resource
//    private AdminUserApi adminUserApi;
//    @Resource
//    private DictDataApi dictDataApi;

    @Override
    public Page<BpmTaskAssignRule> queryPage(BaseQuery query) {
        Page<BpmTaskAssignRule> page = new Page<>(query.getPage(), query.getPageSize());
        BpmTaskAssignRule obj = JSON.toJavaObject(query.getQueryParam(), BpmTaskAssignRule.class);
        Wrapper<BpmTaskAssignRule> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    private Wrapper<BpmTaskAssignRule> buildWrapper(BpmTaskAssignRule bpmTaskAssignRule){
        QueryWrapper<BpmTaskAssignRule> wrapper = new QueryWrapper<>();
        // Query condition...

        return wrapper;
    }

    @Override
    public List<BpmTaskAssignRule> getTaskAssignRuleListByProcessDefinitionId(String processDefinitionId, String taskDefinitionKey) {
        return taskRuleMapper.selectListByProcessDefinitionId(processDefinitionId, taskDefinitionKey);
    }

    @Override
    public List<BpmTaskAssignRule> getTaskAssignRuleListByModelId(String modelId) {
        return taskRuleMapper.selectListByModelId(modelId);
    }

    @Override
    public List<BpmTaskAssignRuleRespVO> getTaskAssignRuleList(String modelId, String processDefinitionId) {
        // 获得规则
        List<BpmTaskAssignRule> rules = Collections.emptyList();
        BpmnModel model = null;
        if (StrUtil.isNotEmpty(modelId)) {
            rules = getTaskAssignRuleListByModelId(modelId);
            model = modelService.getBpmnModel(modelId);
        } else if (StrUtil.isNotEmpty(processDefinitionId)) {
            rules = getTaskAssignRuleListByProcessDefinitionId(processDefinitionId, null);
            model = processDefinitionService.getBpmnModel(processDefinitionId);
        }
        if (model == null) {
            return Collections.emptyList();
        }
        // 获得用户任务，只有用户任务才可以设置分配规则
        List<UserTask> userTasks = FlowableUtils.getBpmnModelElements(model, UserTask.class);
        if (CollUtil.isEmpty(userTasks)) {
            return Collections.emptyList();
        }
        // 转换数据
        return BpmTaskAssignRuleConvert.INSTANCE.convertList(userTasks, rules);
    }

    @Override
    public String createTaskAssignRule(@Valid BpmTaskAssignRuleCreateReqVO reqVO) {
        // 校验参数
//        validTaskAssignRuleOptions(reqVO.getType(), reqVO.getOptions());
        // 校验是否已经配置
        BpmTaskAssignRule existRule = taskRuleMapper.selectListByModelIdAndTaskDefinitionKey(
                reqVO.getModelId(), reqVO.getTaskDefinitionKey());
        if (existRule != null) {
            throw exception(TASK_ASSIGN_RULE_EXISTS, reqVO.getModelId(), reqVO.getTaskDefinitionKey());
        }

        // 存储
        BpmTaskAssignRule rule = BpmTaskAssignRuleConvert.INSTANCE.convert(reqVO);
        rule.setProcessdefinitionid(BpmTaskAssignRule.PROCESS_DEFINITION_ID_NULL); // 只有流程模型，才允许新建
        taskRuleMapper.insert(rule);
        return rule.getId();
    }

    @Override
    public void updateTaskAssignRule(@Valid BpmTaskAssignRuleUpdateReqVO reqVO) {
        // 校验参数
//        validTaskAssignRuleOptions(reqVO.getType(), reqVO.getOptions());
        // 校验是否存在
        BpmTaskAssignRule existRule = taskRuleMapper.selectById(reqVO.getId());
        if (existRule == null) {
            throw exception(TASK_ASSIGN_RULE_NOT_EXISTS);
        }
        // 只允许修改流程模型的规则
        if (!Objects.equals(BpmTaskAssignRule.PROCESS_DEFINITION_ID_NULL, existRule.getProcessdefinitionid())) {
            throw exception(TASK_UPDATE_FAIL_NOT_MODEL);
        }

        // 执行更新
        taskRuleMapper.updateById(BpmTaskAssignRuleConvert.INSTANCE.convert(reqVO));
    }

    @Override
    public boolean isTaskAssignRulesEquals(String modelId, String processDefinitionId) {
        // 调用 VO 接口的原因是，过滤掉流程模型不需要的规则，保持和 copyTaskAssignRules 方法的一致性
        List<BpmTaskAssignRuleRespVO> modelRules = getTaskAssignRuleList(modelId, null);
        List<BpmTaskAssignRuleRespVO> processInstanceRules = getTaskAssignRuleList(null, processDefinitionId);
        if (modelRules.size() != processInstanceRules.size()) {
            return false;
        }

        // 遍历，匹配对应的规则
        Map<String, BpmTaskAssignRuleRespVO> processInstanceRuleMap = CollectionUtils.convertMap(processInstanceRules,
                BpmTaskAssignRuleRespVO::getTaskDefinitionKey);
        for (BpmTaskAssignRuleRespVO modelRule : modelRules) {
            BpmTaskAssignRuleRespVO processInstanceRule = processInstanceRuleMap.get(modelRule.getTaskDefinitionKey());
            if (processInstanceRule == null) {
                return false;
            }
            if (!ObjectUtil.equals(modelRule.getType(), processInstanceRule.getType())
                    || !ObjectUtil.equal(modelRule.getOptions(), processInstanceRule.getOptions())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void copyTaskAssignRules(String fromModelId, String toProcessDefinitionId) {
        List<BpmTaskAssignRuleRespVO> rules = getTaskAssignRuleList(fromModelId, null);
        if (CollUtil.isEmpty(rules)) {
            return;
        }
        // 开始复制
        List<BpmTaskAssignRule> newRules = BpmTaskAssignRuleConvert.INSTANCE.convertList2(rules);
        newRules.forEach(rule -> {
            rule.setProcessdefinitionid(toProcessDefinitionId);
            rule.setId(null);
        });
        taskRuleMapper.insertBatch(newRules);
    }

    @Override
    public void checkTaskAssignRuleAllConfig(String id) {
        // 一个用户任务都没配置，所以无需配置规则
        List<BpmTaskAssignRuleRespVO> taskAssignRules = getTaskAssignRuleList(id, null);
        if (CollUtil.isEmpty(taskAssignRules)) {
            return;
        }
        // 校验未配置规则的任务
        taskAssignRules.forEach(rule -> {
            if (CollUtil.isEmpty(rule.getOptions())) {
                throw exception(MODEL_DEPLOY_FAIL_TASK_ASSIGN_RULE_NOT_CONFIG, rule.getTaskDefinitionName());
            }
        });
    }

//    private void validTaskAssignRuleOptions(Integer type, Set<Long> options) {
//        if (Objects.equals(type, BpmTaskAssignRuleTypeEnum.ROLE.getType())) {
//            roleApi.validRoles(options);
//        } else if (ObjectUtils.equalsAny(type, BpmTaskAssignRuleTypeEnum.DEPT_MEMBER.getType(),
//                BpmTaskAssignRuleTypeEnum.DEPT_LEADER.getType())) {
//            deptApi.validDepts(options);
//        } else if (Objects.equals(type, BpmTaskAssignRuleTypeEnum.POST.getType())) {
//            postApi.validPosts(options);
//        } else if (Objects.equals(type, BpmTaskAssignRuleTypeEnum.USER.getType())) {
//            adminUserApi.validUsers(options);
//        } else if (Objects.equals(type, BpmTaskAssignRuleTypeEnum.USER_GROUP.getType())) {
//            userGroupService.validUserGroups(options);
//        } else if (Objects.equals(type, BpmTaskAssignRuleTypeEnum.SCRIPT.getType())) {
//            dictDataApi.validDictDatas(DictTypeConstants.TASK_ASSIGN_SCRIPT,
//                    CollectionUtils.convertSet(options, String::valueOf));
//        } else {
//            throw new IllegalArgumentException(StrUtil.format("未知的规则类型({})", type));
//        }
//    }
}
