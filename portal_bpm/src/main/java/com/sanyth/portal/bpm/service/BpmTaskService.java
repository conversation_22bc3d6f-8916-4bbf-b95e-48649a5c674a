package com.sanyth.portal.bpm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.bpm.dto.BpmTaskModelQuery;
import com.sanyth.portal.bpm.dto.UserTaskModelDTO;
import com.sanyth.portal.bpm.model.BpmTaskExt;
import com.sanyth.portal.bpm.model.BpmTaskModelEntity;
import com.sanyth.portal.bpm.vo.*;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.util.collection.CollectionUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.event.FlowableMultiInstanceActivityCompletedEvent;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 流程任务实例 Service 接口
 *
 */
public interface BpmTaskService {
    /**
     * 获得待办的流程任务分页
     *
     * @param user 用户
     * @param pageReqVO 分页请求
     * @return 流程任务分页
     */
    Page<BpmTaskTodoPageItemRespVO> getTodoTaskPage(CurrentUser user, BpmTaskTodoDonePageReqVO pageReqVO);
    /**
     * 获得待办的流程数量
     *
     * @param user 用户
     * @param pageReqVO 参数
     * @return 流程任务数量
     */
    Long getTodoTaskCount(CurrentUser user, BpmTaskTodoDonePageReqVO pageReqVO);
    /**
     * 获得已办的流程数量
     *
     * @param user 用户
     * @param pageReqVO 参数
     * @return 流程任务数量
     */
    Long getDoneTaskCount(CurrentUser user, BpmTaskTodoDonePageReqVO pageReqVO);
    /**
     * 获得已办的流程任务分页
     *
     * @param user 用户编号
     * @param pageReqVO 分页请求
     * @return 流程任务分页
     */
    Page<BpmTaskDonePageItemRespVO> getDoneTaskPage(CurrentUser user, BpmTaskTodoDonePageReqVO pageReqVO);

    /**
     * 获得流程任务 Map
     *
     * @param processInstanceIds 流程实例的编号数组
     * @return 流程任务 Map
     */
    default Map<String, List<Task>> getTaskMapByProcessInstanceIds(List<String> processInstanceIds) {
        return CollectionUtils.convertMultiMap(getTasksByProcessInstanceIds(processInstanceIds),
                Task::getProcessInstanceId);
    }

    /**
     * 获得流程任务列表
     *
     * @param processInstanceIds 流程实例的编号数组
     * @return 流程任务列表
     */
    List<Task> getTasksByProcessInstanceIds(List<String> processInstanceIds);

    /**
     * 获得指令流程实例的流程任务列表，包括所有状态的
     *
     * @param processInstanceId 流程实例的编号
     * @return 流程任务列表
     */
    List<BpmTaskRespVO> getTaskListByProcessInstanceId(String processInstanceId);

    /**
     * 通过任务
     *
     * @param userId 用户编号
     * @param reqVO 通过请求
     */
    Map<String, Object> approveTask(String userId, @Valid BpmTaskApproveReqVO reqVO);

    void approveTaskByListener(DelegateExecution delegateExecution);

    /**
     * 不通过任务
     *
     * @param userId 用户编号
     * @param reqVO 不通过请求
     */
    void rejectTask(String userId, @Valid BpmTaskApproveReqVO reqVO);

    /**
     * 将流程任务分配给指定用户
     *
     * @param currentUser 用户编号
     * @param reqVO 分配请求
     */
    void updateTaskAssignee(CurrentUser currentUser, BpmTaskUpdateAssigneeReqVO reqVO);

    /**
     * 将流程任务分配给指定用户
     *
     * @param id 流程任务编号
     * @param userId 用户编号
     */
    void updateTaskAssignee(String id, String userId);

    /**
     * 删除候选人和身份链接
     *
     * @param task 任务
     */
    void deleteCandidateAndIdentityLink(Task task);

    /**
     * 指定下一个审批人
     *
     * @param selectNextUserList 选择next用户列表
     * @param task               任务
     * @param bpmnModel          bpmn模型
     */
    void designateNextApprover(List<SelectNextUserVO> selectNextUserList,BpmTaskApproveReqVO reqVO, BpmnModel bpmnModel);

    /**
     * 创建 Task 拓展记录
     *
     * @param task 任务实体
     */
    void createTaskExt(Task task);

    /**
     * 更新 Task 拓展记录为完成
     *
     * @param task 任务实体
     */
    void updateTaskExtComplete(Task task);

    /**
     * 更新 Task 拓展记录，并发送通知
     *
     * @param task 任务实体
     */
    void updateTaskExtAssign(Task task);

    /**
     * 任务委派
     */
    void delegateTask(CurrentUser currentUser, BpmTaskUpdateAssigneeReqVO reqVO);

    /**
     * 任务驳回
     */
    void taskReturn(CurrentUser currentUser, BpmTaskReturnReqVO reqVO);

    /**
     * 删除任务实例扩展
     * @param bpmTaskExt
     */
    void deleteTaskExt(BpmTaskExt bpmTaskExt);

    /**
     * 获取第一个任务(流程发起人对应的任务)
     * @param processInstance 流程实例
     */
    List<BpmTaskExt> getFirstTasks(List<String> processInstanceIds);

    /**
     * 启动第一个任务(流程发起人对应的任务)
     * @param processInstance 流程实例
     * @param variables 流程参数
     */
    Task startFirstTask(ProcessInstance processInstance, Map<String, Object> variables, String message);

    /**
     * 更新任务扩展表Endtime、Result
     * @param event
     */
    void updateTaskExt(FlowableMultiInstanceActivityCompletedEvent event);

    /**
     * 得到下一个节点列表
     * 当前任务还未审批时可以使用此方法,审批过后的ru_task表已无数据了，
     * 需要使用getNextNodesByProcessDefinitionIdAndTaskDefinitionKey方法来找下个节点
     * @param reqVO 申请签证官
     * @return {@link List}<{@link FlowNodeVo}>
     */
    List<FlowNodeVo> getNextNodesByTaskId(BpmTaskApproveReqVO reqVO);

    /**
     * 得到下一个节点列表
     *
     * @param reqVO 申请签证官
     * @return {@link List}<{@link FlowNodeVo}>
     */
    List<FlowNodeVo> getNextNodesByProcessDefinitionIdAndTaskDefinitionKey(BpmTaskApproveReqVO reqVO);

    void setNextApprover(BpmTaskApproveReqVO reqVO);

    /**
     * 查询我转办、委派的任务
     * @param currentUser
     * @param pageVO
     */
    Page<BpmTaskDonePageItemRespVO>  getOwerTaskPage(CurrentUser currentUser, BpmTaskTodoDonePageReqVO pageVO);
    /**
     * 查询我转办、委派的任务
     * @param currentUser
     * @param pageVO
     */
    Long  getOwerTaskCount(CurrentUser currentUser, BpmTaskTodoDonePageReqVO pageVO);

    /**
     * 任务取回
     *
     * @param currentUser 当前用户
     * @param reqVO
     */
    void taskRetrieve(CurrentUser currentUser, BpmTaskReturnReqVO reqVO);

    /**
     * 设置某个任务的超时截止日期
     *
     * @param taskId  任务id
     * @param dueDate 超时截止日期
     */
    void setDueDate(String taskId, Date dueDate);

    /**
     * 获取可驳回节点列表
     * @param reqVO
     */
//    List<FlowNodeVo> getBackNodesByTaskId(BpmTaskApproveReqVO reqVO);

    /**
     * 查询流程所有的用户任务节点信息，分并行网关节点和非并行网关节点
     *
     * @param query 查询参数
     * @return List BpmTaskModelEntity
     */
    UserTaskModelDTO getUserTaskModelDto(BpmTaskModelQuery query);

    /**
     * 查询流程所有的用户任务节点信息，分并行网关节点和非并行网关节点
     *
     * @param query 查询参数
     * @return List BpmTaskModelEntity
     */
    List<BpmTaskModelEntity> listUserTaskModels(BpmTaskModelQuery query);

    /**
     * 【本任务节点】的上一步【历史任务节点】列表
     *
     * @param query ignore
     * @return ignore
     */
    List<BpmTaskModelEntity> listBackTaskModel(BpmTaskApproveReqVO query);

    /**
     * 【本任务节点】之前的【历史任务节点】列表
     *
     * @param query ignore
     * @return ignore
     */
    List<BpmTaskModelEntity> listAllBackTaskModel(BpmTaskApproveReqVO query);

    /**
     * 发起人取回
     *
     * @param currentUser 当前用户
     * @param reqVO       申请签证官
     */
    void retrieveTaskForInitiate(CurrentUser currentUser, BpmTaskReturnReqVO reqVO);

    /**
     * 强制撤回 到发起人
     *
     * @param reqVO 申请签证官
     */
    void forceRecallTask(CurrentUser currentUser,BpmTaskApproveReqVO reqVO);

    /**
     * 获得流程定义对应的第一个发起人节点标识
     * @param processDefinitionIds
     * @return java.util.Map<java.lang.String,java.lang.String>
     */
    Map<String, String> getProcessDefIdAndTaskKey(Set<String> processDefinitionIds);

    /**
     * 获取下个节点列表
     * @param reqVO
     * @return java.util.List<com.sanyth.portal.bpm.vo.FlowNodeVo>
     */
    List<FlowNodeVo> getNextFlowNodeVos(BpmTaskApproveReqVO reqVO, BpmnModel bpmnModel);

    /**
     * 获取指定审批节点的审批信息
     * @date 2024-1-9/0009
     * @time 11:51
     * @param processInstanceId 流程实例ID
     * @param taskDefinitionKey 流程节点标识
     * @param format 日期格式
     * @return java.lang.String
     */
    String getApproveInfoByProcessInstanceIdAndTaskDefinitionKey(String processInstanceId, String taskDefinitionKey, String format);
    /**
     * 给发起人推送消息
     */
    void pushMsgToTaskAssign(Task task);
    /**
     * 给审批人推送消息
     * @param startUserId 发起人
     * @param instance 流程实例
     * @param selectNextUserList 选择的下一节点发起人
     * @param bpmnModel 流程模型
     * @param currentTask 当前审批节点
     */
    void pushMsgToNextTaskAssign(String startUserId, ProcessInstance instance, List<SelectNextUserVO> selectNextUserList, BpmnModel bpmnModel, Task currentTask);
}
