package com.sanyth.portal.bpm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.bpm.constant.TaskConstants;
import com.sanyth.portal.bpm.convert.BpmProcessInstanceConvert;
import com.sanyth.portal.bpm.convert.BpmTaskConvert;
import com.sanyth.portal.bpm.dto.AdminUserRespDTO;
import com.sanyth.portal.bpm.dto.DeptRespDTO;
import com.sanyth.portal.bpm.enums.BpmProcessInstanceDeleteReasonEnum;
import com.sanyth.portal.bpm.enums.BpmProcessInstanceResultEnum;
import com.sanyth.portal.bpm.enums.BpmProcessInstanceStatusEnum;
import com.sanyth.portal.bpm.event.BpmProcessInstanceResultEventPublisher;
import com.sanyth.portal.bpm.mapper.BpmProcessInstanceExtMapper;
import com.sanyth.portal.bpm.mapper.BpmTaskExtMapper;
import com.sanyth.portal.bpm.mapper.LambdaQueryWrapperX;
import com.sanyth.portal.bpm.model.BpmOrgAttributeConfig;
import com.sanyth.portal.bpm.model.BpmProcessDefinitionExt;
import com.sanyth.portal.bpm.model.BpmProcessInstanceExt;
import com.sanyth.portal.bpm.model.BpmTaskExt;
import com.sanyth.portal.bpm.pojo.PageResult;
import com.sanyth.portal.bpm.service.*;
import com.sanyth.portal.bpm.util.FlowableUtils;
import com.sanyth.portal.bpm.vo.*;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.model.SytPermissionAccount;
import com.sanyth.portal.util.system.RequestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.event.FlowableCancelledEvent;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

import static com.sanyth.portal.bpm.enums.ErrorCodeConstants.*;
import static com.sanyth.portal.bpm.util.ServiceExceptionUtil.exception;
import static com.sanyth.portal.util.collection.CollectionUtils.*;
import static java.util.Collections.emptyList;

/**
 * Bpm 流程实例的拓展表
 *
 * <AUTHOR>
 * @since 2022-05-16
 */
@Service
public class BpmProcessInstanceExtServiceImpl extends ServiceImpl<BpmProcessInstanceExtMapper, BpmProcessInstanceExt> implements IBpmProcessInstanceExtService {
    @Resource
    private RuntimeService runtimeService;
    @Resource
    private BpmProcessInstanceExtMapper processInstanceExtMapper;
    @Resource
    @Lazy // 解决循环依赖
    private BpmTaskService bpmTaskService;
    @Resource
    private BpmProcessDefinitionService processDefinitionService;
    @Resource
    private HistoryService historyService;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private DeptApi deptApi;
    @Resource
    private BpmProcessInstanceResultEventPublisher processInstanceResultEventPublisher;
    //    @Resource
//    private BpmMessageService messageService;
    @Resource
    protected TaskService taskService;
    @Resource
    protected RepositoryService repositoryService;
    @Resource
    protected BpmTaskExtMapper bpmTaskExtMapper;
    @Autowired
    private IBpmFormFieldValueService iBpmFormFieldValueService;
    @Resource
    @Lazy // 解决循环依赖
    private BpmMessageService bpmMessageService;
    @Resource
    IBpmOrgAttributeConfigService bpmOrgAttributeConfigService;


    @Override
    public ProcessInstance getProcessInstance(String id) {
        return runtimeService.createProcessInstanceQuery().processInstanceId(id).singleResult();
    }

    @Override
    public List<ProcessInstance> getProcessInstances(Set<String> ids) {
        return runtimeService.createProcessInstanceQuery().processInstanceIds(ids).list();
    }

    @Override
    public List<BpmProcessInstanceExt> getBpmProcessInstanceExts(Set<String> ids) {
        QueryWrapper<BpmProcessInstanceExt> wrapper = new QueryWrapper<>();
        wrapper.in("PROCESSINSTANCEID", ids);
        return list(wrapper);
    }

    @Override
    public Page<BpmProcessInstancePageItemRespVO> getMyProcessInstancePage(String userId, BpmProcessInstanceMyPageReqVO pageReqVO) {
        // 通过 BpmProcessInstanceExt 表，先查询到对应的分页
        PageResult<BpmProcessInstanceExt> pageResult = processInstanceExtMapper.selectPage(userId, pageReqVO);
        Page<BpmProcessInstancePageItemRespVO> page = new Page<>(pageReqVO.getPage(), pageReqVO.getPageSize());
        if (CollUtil.isEmpty(pageResult.getList())) {
            page.setRecords(emptyList());
            page.setTotal(pageResult.getTotal());
            return page;
        }

        // 获得流程 Task Map
        List<String> processInstanceIds = convertList(pageResult.getList(), BpmProcessInstanceExt::getProcessInstanceId);
        Map<String, List<Task>> taskMap = bpmTaskService.getTaskMapByProcessInstanceIds(processInstanceIds);

        //获得流程TaskExt
        List<BpmTaskExt> bpmTaskExts = bpmTaskService.getFirstTasks(processInstanceIds);
        Set<String> startUserIds = convertSet(pageResult.getList(), BpmProcessInstanceExt::getStartUserId);
        List<AdminUserRespDTO> users = adminUserApi.getUsersByHumancodes(startUserIds);
        Map<String, AdminUserRespDTO> userRespDTOMap = convertMap(users, AdminUserRespDTO::getHumanCode);
        // 获得流程定义对应的第一个发起人节点标识
        Map<String, String> processDefIdAndTaskKeyMap = bpmTaskService.getProcessDefIdAndTaskKey(convertSet(pageResult.getList(), BpmProcessInstanceExt::getProcessDefinitionId));
        // 转换返回
        PageResult<BpmProcessInstancePageItemRespVO> voPageResult = BpmProcessInstanceConvert.INSTANCE.convertPage(pageResult, taskMap, userRespDTOMap, bpmTaskExts, processDefIdAndTaskKeyMap);
        page.setRecords(voPageResult.getList());
        page.setTotal(pageResult.getTotal());
        return page;
    }

    @Override
    public Long getMyProcessInstanceCount(String userId, @Valid BpmProcessInstanceMyPageReqVO reqVO) {
        LambdaQueryWrapperX<BpmProcessInstanceExt> lambdaQueryWrapperX = new LambdaQueryWrapperX<BpmProcessInstanceExt>()
                .eqIfPresent(BpmProcessInstanceExt::getStartUserId, userId)
                .likeIfPresent(BpmProcessInstanceExt::getStartUserName, reqVO.getStartUserName())
                .likeIfPresent(BpmProcessInstanceExt::getName, reqVO.getName())
                .eqIfPresent(BpmProcessInstanceExt::getProcessDefinitionId, reqVO.getProcessDefinitionId())
                .eqIfPresent(BpmProcessInstanceExt::getCategory, reqVO.getCategory())
                .eqIfPresent(BpmProcessInstanceExt::getStatus, reqVO.getStatus())
                .eqIfPresent(BpmProcessInstanceExt::getResult, reqVO.getResult())
                .betweenIfPresent(BpmProcessInstanceExt::getCreateTime, reqVO.getBeginCreateTime(), reqVO.getEndCreateTime());
        return baseMapper.selectCount(lambdaQueryWrapperX);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> createProcessInstance(String userId, @Valid BpmProcessInstanceCreateReqVO createReqVO) {
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService.getProcessDefinition(createReqVO.getProcessDefinitionId());
        // 发起流程
        return createProcessInstance0(userId, definition, createReqVO);
    }

    /*@Override
    public Map<String,Object> createProcessInstance(String userId, @Valid BpmProcessInstanceCreateReqDTO createReqDTO) {
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService.getActiveProcessDefinition(createReqDTO.getProcessDefinitionKey());
        // 发起流程
        return createProcessInstance0(userId, definition, createReqDTO.getVariables(), createReqDTO.getBusinessKey(), createReqDTO.getExpressionVariables(),createReqDTO.getSelectNextUserList());
    }*/

    @Override
    public BpmProcessInstanceRespVO getProcessInstanceVO(String id) {
        // 获得流程实例
        HistoricProcessInstance processInstance = getHistoricProcessInstance(id);
        if (processInstance == null) {
            return null;
        }
        BpmProcessInstanceExt processInstanceExt = processInstanceExtMapper.selectByProcessInstanceId(id);
        Assert.notNull(processInstanceExt, "流程实例拓展({}) 不存在", id);
        BpmnModel bpmnModel = processDefinitionService.getBpmnModel(processInstance.getProcessDefinitionId());
        if (bpmnModel == null) {
            Assert.notNull(bpmnModel, "流程模型({}) 不存在", id);
        }

        // 获得流程定义
        ProcessDefinition processDefinition = processDefinitionService
                .getProcessDefinition(processInstance.getProcessDefinitionId());
        Assert.notNull(processDefinition, "流程定义({}) 不存在", processInstance.getProcessDefinitionId());
        BpmProcessDefinitionExt processDefinitionExt = processDefinitionService.getProcessDefinitionExt(
                processInstance.getProcessDefinitionId());
        Assert.notNull(processDefinitionExt, "流程定义拓展({}) 不存在", id);
        String bpmnXml = processDefinitionService.getProcessDefinitionBpmnXML(processInstance.getProcessDefinitionId());

        // 获得 User
        AdminUserRespDTO startUser = adminUserApi.getUser(processInstance.getStartUserId());
        SytPermissionAccount account = adminUserApi.getAccount(startUser.getHumanCode());
        DeptRespDTO dept = null;
        if (startUser != null) {
//            dept = deptApi.getDept(startUser.getDeptId());
        }
        Set<String> resSet = FlowableUtils.getFieldsPermissionData(bpmnModel);
        // 拼接结果
        // return BpmProcessInstanceConvert.INSTANCE.convert2(processInstance, processInstanceExt,
        //         processDefinition, processDefinitionExt, bpmnXml, startUser, dept);
        BpmProcessInstanceRespVO respVO = BpmProcessInstanceConvert.INSTANCE.convert2(processInstance, processInstanceExt,
                processDefinition, processDefinitionExt, bpmnXml, startUser, dept);
        respVO.setFieldsPermission(resSet);
        return respVO;
    }

    @Override
    public BpmProcessInstanceExt getProcessInstanceVOByPxjl(String id) {

        List<BpmProcessInstanceExt> list = processInstanceExtMapper.selectByProcessPxjlId(id);
        Assert.notNull(list, "流程实例拓展({}) 不存在", id);
        BpmProcessInstanceExt bmpProcessInstanceExt=list.get(0);

        //获取表单数据项
        // 获得流程实例
        /*HistoricProcessInstance processInstance = getHistoricProcessInstance(bmpProcessInstanceExt.getProcessInstanceId());
        if (processInstance == null) {
            return null;
        }
        // 获得流程定义
        ProcessDefinition processDefinition = processDefinitionService
                .getProcessDefinition(processInstance.getProcessDefinitionId());
        Assert.notNull(processDefinition, "流程定义({}) 不存在", processInstance.getProcessDefinitionId());
        BpmProcessDefinitionExt processDefinitionExt = processDefinitionService.getProcessDefinitionExt(
                processInstance.getProcessDefinitionId());
        Assert.notNull(processDefinitionExt, "流程定义拓展({}) 不存在", id);*/

        return bmpProcessInstanceExt;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelProcessInstance(String userId, @Valid BpmProcessInstanceCancelReqVO cancelReqVO) {
        List<Task> task = taskService.createTaskQuery().processInstanceId(cancelReqVO.getId()).list();
        if (CollectionUtils.isEmpty(task)) {
            throw new RuntimeException("流程未启动或已执行完成，撤销失败");
        }
        // 校验流程实例存在
        ProcessInstance processInstance = getProcessInstance(cancelReqVO.getId());
        if (processInstance == null) {
            throw exception(PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS);
        }
        // 只能撤销自己的
        if (!Objects.equals(processInstance.getStartUserId(), String.valueOf(userId))) {
            throw exception(PROCESS_INSTANCE_CANCEL_FAIL_NOT_SELF);
        }

        // 查询历史数据
        HistoricProcessInstance historicProcessInstance = getHistoricProcessInstance(cancelReqVO.getId());
        if (Objects.isNull(historicProcessInstance)) {
            throw new FlowableObjectNotFoundException("流程实例不存在: " + cancelReqVO.getId());
        }
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
        if (Objects.nonNull(bpmnModel)) {
            Process process = bpmnModel.getMainProcess();
            List<EndEvent> endNodes = process.findFlowElementsOfType(EndEvent.class, false);
            if (CollectionUtils.isNotEmpty(endNodes)) {
                Authentication.setAuthenticatedUserId(userId);
//                taskService.addComment(task.getId(), processInstance.getProcessInstanceId(), FlowComment.STOP.getType(),
//                        StringUtils.isBlank(flowTaskVo.getComment()) ? "撤销申请" : flowTaskVo.getComment());
                // 获取当前流程最后一个节点
                String endId = endNodes.get(0).getId();
                List<Execution> executions = runtimeService.createExecutionQuery()
                        .parentId(processInstance.getProcessInstanceId()).list();
                List<String> executionIds = new ArrayList<>();
                executions.forEach(execution -> executionIds.add(execution.getId()));
                // 变更流程为已结束状态：会触发FlowableEngineEventType.PROCESS_COMPLETED
                // runtimeService.createChangeActivityStateBuilder()
                //         .moveExecutionsToSingleActivityId(executionIds, endId).changeState();

                // add 2022.10.31 会触发事件：FlowableEngineEventType.PROCESS_CANCELLED
                deleteProcessInstance(processInstance.getProcessInstanceId(), StrUtil.format(BpmProcessInstanceDeleteReasonEnum.CANCEL_TASK.format(cancelReqVO.getReason())));
                // 清理
                FlowableUtils.clearAuthenticatedUserId();
            }
        }

        // 更新 status + result
        BpmProcessInstanceExt instanceExtDO = new BpmProcessInstanceExt();
        instanceExtDO.setProcessInstanceId(cancelReqVO.getId());
        instanceExtDO.setStatus(BpmProcessInstanceStatusEnum.FINISH.getStatus());
        instanceExtDO.setResult(BpmProcessInstanceResultEnum.CANCEL.getResult());
        instanceExtDO.setUpdateTime(new Date());
        instanceExtDO.setUpdater(userId);
        instanceExtDO.setEndTime(new Date());
        processInstanceExtMapper.updateByProcessInstanceId(instanceExtDO);
        // 更新流程任务扩展表数据 result
        List<Task> taskList = bpmTaskService.getTasksByProcessInstanceIds(Arrays.asList(cancelReqVO.getId().split(",")));
        if (taskList.size() > 0) {
            BpmTaskExt bpmTaskExt = new BpmTaskExt()
                    .setTaskid(taskList.get(0).getId())
                    .setResult(BpmProcessInstanceResultEnum.CANCEL.getResult());
            bpmTaskExtMapper.updateByTaskId(bpmTaskExt);
        }
        List<BpmTaskExt> taskExtList = bpmTaskExtMapper.selectListByProcessInstanceId(processInstance.getProcessInstanceId());
        if (taskExtList.size() > 0) {
            bpmTaskExtMapper.update(new BpmTaskExt().setResult(BpmProcessInstanceResultEnum.CANCEL.getResult()).setEndtime(new Date()),
                    new LambdaQueryWrapper<BpmTaskExt>()
                            .eq(BpmTaskExt::getProcessinstanceid, processInstance.getProcessInstanceId())
                            .isNull(BpmTaskExt::getEndtime));
        }

    }

    /**
     * 获得历史的流程实例
     *
     * @param id 流程实例的编号
     * @return 历史的流程实例
     */
    @Override
    public HistoricProcessInstance getHistoricProcessInstance(String id) {
        return historyService.createHistoricProcessInstanceQuery().processInstanceId(id).singleResult();
    }

    @Override
    public List<HistoricProcessInstance> getHistoricProcessInstances(Set<String> ids) {
        return historyService.createHistoricProcessInstanceQuery().processInstanceIds(ids).list();
    }

    @Override
    public void createProcessInstanceExt(ProcessInstance instance) {
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService.getProcessDefinition2(instance.getProcessDefinitionId());
        // 插入 BpmProcessInstanceExt 对象
        BpmProcessInstanceExt instanceExtDO = new BpmProcessInstanceExt();
        instanceExtDO.setProcessInstanceId(instance.getId());
        instanceExtDO.setProcessDefinitionId(definition.getId());
        instanceExtDO.setName(instance.getProcessDefinitionName());
        instanceExtDO.setStartUserId(instance.getStartUserId());
        AdminUserRespDTO user = adminUserApi.getUser(instance.getStartUserId());
        instanceExtDO.setStartUserName(user.getHumanName());
        instanceExtDO.setCategory(definition.getCategory());
        instanceExtDO.setStatus(BpmProcessInstanceStatusEnum.RUNNING.getStatus());
        instanceExtDO.setResult(BpmProcessInstanceResultEnum.PROCESS.getResult());
        instanceExtDO.setCreateTime(new Date());
        instanceExtDO.setUpdateTime(new Date());
        CurrentUser currentUser = RequestUtils.getLoginUser();
        if (currentUser != null) {
            instanceExtDO.setCreator(RequestUtils.getLoginUser().getHumanCode());
            instanceExtDO.setUpdater(RequestUtils.getLoginUser().getHumanCode());
        }

        processInstanceExtMapper.insert(instanceExtDO);
    }

    @Override
    public void updateProcessInstanceExtCancel(FlowableCancelledEvent event) {
        // 判断是否为 Reject 不通过，或者主动删除。如果是，则不进行更新
        if (BpmProcessInstanceDeleteReasonEnum.isRejectReason((String) event.getCause())
                || BpmProcessInstanceDeleteReasonEnum.isDeleteReason((String) event.getCause())) {
            // 推送消息
            bpmMessageService.pushMsgToStartUser(getHistoricProcessInstance(event.getProcessInstanceId()),
                    (BpmProcessInstanceDeleteReasonEnum.isRejectReason((String) event.getCause()) ? "不通过" : "已删除") );
            return;
        }

        // 需要主动查询，因为 instance 只有 id 属性
        // 另外，此时如果去查询 ProcessInstance 的话，字段是不全的，所以去查询了 HistoricProcessInstance
        HistoricProcessInstance processInstance = getHistoricProcessInstance(event.getProcessInstanceId());
        // 更新拓展表
        BpmProcessInstanceExt instanceExtDO = new BpmProcessInstanceExt();
        instanceExtDO.setProcessInstanceId(event.getProcessInstanceId());
        instanceExtDO.setEndTime(new Date()); // 由于 ProcessInstance 里没有办法拿到 endTime，所以这里设置
        instanceExtDO.setStatus(BpmProcessInstanceStatusEnum.FINISH.getStatus());
        instanceExtDO.setResult(BpmProcessInstanceResultEnum.CANCEL.getResult());
        instanceExtDO.setUpdateTime(new Date());
        CurrentUser currentUser = RequestUtils.getLoginUser();
        if (currentUser != null) {
            instanceExtDO.setUpdater(RequestUtils.getLoginUser().getHumanCode());
        }

        processInstanceExtMapper.updateByProcessInstanceId(instanceExtDO);

        // 发送流程实例的状态事件
        processInstanceResultEventPublisher.sendProcessInstanceResultEvent(
                BpmProcessInstanceConvert.INSTANCE.convert(this, processInstance, instanceExtDO.getResult()));
        // 推送消息
        bpmMessageService.pushMsgToStartUser(processInstance, BpmProcessInstanceResultEnum.CANCEL.getDesc());
    }

    @Override
    public void updateProcessInstanceExtComplete(ProcessInstance instance) {
        // 需要主动查询，因为 instance 只有 id 属性
        // 另外，此时如果去查询 ProcessInstance 的话，字段是不全的，所以去查询了 HistoricProcessInstance
        HistoricProcessInstance processInstance = getHistoricProcessInstance(instance.getId());
        // 更新拓展表
        BpmProcessInstanceExt instanceExtDO = new BpmProcessInstanceExt();
        instanceExtDO.setProcessInstanceId(instance.getProcessInstanceId());
        instanceExtDO.setEndTime(new Date()); // 由于 ProcessInstance 里没有办法拿到 endTime，所以这里设置
        instanceExtDO.setStatus(BpmProcessInstanceStatusEnum.FINISH.getStatus());
        instanceExtDO.setResult(BpmProcessInstanceResultEnum.APPROVE.getResult()); // 如果正常完全，说明审批通过
        instanceExtDO.setUpdateTime(new Date());
        CurrentUser currentUser = RequestUtils.getLoginUser();
        if (currentUser != null) {
            instanceExtDO.setUpdater(RequestUtils.getLoginUser().getHumanCode());
        }
        processInstanceExtMapper.updateByProcessInstanceId(instanceExtDO);

        // 发送流程被通过的消息
//        messageService.sendMessageWhenProcessInstanceApprove(BpmProcessInstanceConvert.INSTANCE.convert2ApprovedReq(instance));
//
//        // 发送流程实例的状态事件
        processInstanceResultEventPublisher.sendProcessInstanceResultEvent(
                BpmProcessInstanceConvert.INSTANCE.convert(this, processInstance, instanceExtDO.getResult()));
        // 推送消息
        bpmMessageService.pushMsgToStartUser(processInstance, BpmProcessInstanceResultEnum.APPROVE.getDesc());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProcessInstanceExtReject(String id, String reason) {
        // 需要主动查询，因为 instance 只有 id 属性
        ProcessInstance processInstance = getProcessInstance(id);
        // 删除流程实例，以实现驳回任务时，取消整个审批流程
        deleteProcessInstance(id, StrUtil.format(BpmProcessInstanceDeleteReasonEnum.REJECT_TASK.format(reason)));

        // 更新 status + result
        // 注意，不能和上面的逻辑更换位置。因为 deleteProcessInstance 会触发流程的取消，进而调用 updateProcessInstanceExtCancel 方法，
        // 设置 result 为 BpmProcessInstanceStatusEnum.CANCEL，显然和 result 不一定是一致的
        BpmProcessInstanceExt instanceExtDO = new BpmProcessInstanceExt();
        instanceExtDO.setProcessInstanceId(id);
        instanceExtDO.setStatus(BpmProcessInstanceStatusEnum.FINISH.getStatus());
        instanceExtDO.setResult(BpmProcessInstanceResultEnum.REJECT.getResult());
        instanceExtDO.setUpdateTime(new Date());
        CurrentUser currentUser = RequestUtils.getLoginUser();
        if (currentUser != null) {
            instanceExtDO.setUpdater(RequestUtils.getLoginUser().getHumanCode());
        }
        instanceExtDO.setEndTime(new Date());
        processInstanceExtMapper.updateByProcessInstanceId(instanceExtDO);

        // 发送流程被不通过的消息
//        messageService.sendMessageWhenProcessInstanceReject(BpmProcessInstanceConvert.INSTANCE.convert2RejectReq(processInstance, reason));
//
//        // 发送流程实例的状态事件
        processInstanceResultEventPublisher.sendProcessInstanceResultEvent(
                BpmProcessInstanceConvert.INSTANCE.convert(this, processInstance, instanceExtDO.getResult()));
    }


    private void deleteProcessInstance(String id, String reason) {
        // 会触发事件：FlowableEngineEventType.PROCESS_CANCELLED
        runtimeService.deleteProcessInstance(id, reason);
    }

    private Map<String, Object> createProcessInstance0(String userId, ProcessDefinition definition, BpmProcessInstanceCreateReqVO createReqVO) {
        // 校验流程定义
        if (definition == null) {
            throw exception(PROCESS_DEFINITION_NOT_EXISTS);
        }
        if (definition.isSuspended()) {
            throw exception(PROCESS_DEFINITION_IS_SUSPENDED);
        }

        // 设置工作流的用户
        FlowableUtils.setAuthenticatedUserId(userId);
//        variables.put(TaskConstants.PROCESS_INITIATOR, userId);
        Map<String, Object> variables = createReqVO.getVariables();
        Map<String, Object> expressionVariables = createReqVO.getExpressionVariables();
        if (expressionVariables == null) {
            expressionVariables = new HashMap<>();
        }
        expressionVariables.put(TaskConstants.PROCESS_INITIATOR, userId);
        // 创建流程实例 2022-11-09：发起流程时将流转条件保存为全局变量
        ProcessInstance instance = runtimeService.startProcessInstanceById(definition.getId(), "", expressionVariables);
        // 设置流程名字
        runtimeService.setProcessInstanceName(instance.getId(), definition.getName());
        // 给第一步申请人节点设置任务执行人和意见
        String message = userId + "发起流程申请";
        Task task = bpmTaskService.startFirstTask(instance, expressionVariables, message);


        // 补全流程实例的拓展表
        BpmProcessInstanceExt bpmProcessInstanceExt = new BpmProcessInstanceExt();
        bpmProcessInstanceExt.setProcessInstanceId(instance.getId());
        bpmProcessInstanceExt.setFormVariables(variables);

        bpmProcessInstanceExt.setPxjlId(createReqVO.getPxjlid());
        processInstanceExtMapper.updateByProcessInstanceId(bpmProcessInstanceExt);

        // 清理
        FlowableUtils.clearAuthenticatedUserId();
        // 保存表单变量的值
        iBpmFormFieldValueService.saveFormFieldValueData(userId, variables, instance.getId(), instance.getProcessDefinitionId());
        //TODO 设置下个环节办理人designateNextApprover方法里有判断是否可以设置审批人
        List<SelectNextUserVO> selectNextUserList = createReqVO.getSelectNextUserList();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        designateNextApprover(instance, task, selectNextUserList, createReqVO.getProcessDefinitionId(), createReqVO.getTaskDefinitionKey(), "", bpmnModel);
        // 下一节点审批人发送消息
        bpmTaskService.pushMsgToNextTaskAssign(userId, instance, selectNextUserList, bpmnModel, task);
        Map<String, Object> map = new HashMap<>();
        map.put("definitionKey", task.getTaskDefinitionKey());
        map.put("definitionId", task.getProcessDefinitionId());
        map.put("instanceId", instance.getId());
        map.put("taskId", task.getId());
        map.put("variables", expressionVariables);
        // 借用此字段，传参跳转的任务节点
        map.put("returnTaskDefKey", task.getDescription());
        return map;
    }

    private void getCurrentTaskAssignUserData(String groupIds, BpmTaskRespVO.User assigneeUser, String dataType, List<BpmTaskRespVO.User> adminUserRespDTOS) {
        if (StringUtils.isNotBlank(groupIds)) {
            List<AdminUserRespDTO> list = new ArrayList<>();
            if (groupIds.startsWith("ROLE") && "ROLES".equals(dataType)) {
                groupIds = groupIds.replace("ROLE", "");
                String[] tmpArr = groupIds.split(",");
                list = adminUserApi.getUsersByRoleIds(Arrays.asList(tmpArr));
            } else if (groupIds.startsWith("DEPT") && "DEPTS".equals(dataType)) {   //部门
                groupIds = groupIds.replace("DEPT", "");
                String[] tmpArr = groupIds.split(",");
                list = adminUserApi.getUsersByDeptIds(Arrays.asList(tmpArr));
            } else if (groupIds.startsWith("HEAD") && "DEPTHEAD".equals(dataType)) {   //部门领导
                String[] tmpArr = groupIds.split(",");
                for (String tmp : tmpArr) {
                    String[] split = tmp.split("#");
                    String head = split[0];
                    head = head.replace("HEAD", "");
                    list = adminUserApi.getUsersByHumancodes(Arrays.asList(head.split("&")));
                }
            } else if (groupIds.startsWith("ATTRS") && "ATTRS".equals(dataType)) {      //发起人组织关系
                groupIds = groupIds.replace("ATTRS", "");
                String[] tmpArr = groupIds.split(",");
                List<AdminUserRespDTO> subList = new ArrayList<>();
                for (String tmp : tmpArr) {
                    BpmOrgAttributeConfig config = new BpmOrgAttributeConfig();
                    config.setAttributeId(tmp);
                    List<BpmOrgAttributeConfig> configs = bpmOrgAttributeConfigService.queryList(config);
                    for (BpmOrgAttributeConfig attributeConfig : configs) {
                        subList.addAll(adminUserApi.getUsersByHumancodes(Arrays.asList(attributeConfig.getAttributeValue().split(","))));
                    }
                }
                list = subList;
            } else if (groupIds.startsWith("ATTR") && "INITATTR".equals(dataType)) {      //组织关系
                groupIds = groupIds.replace("ATTR", "");
                String[] tmpArr = groupIds.split(",");
                QueryWrapper<BpmOrgAttributeConfig> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("id", tmpArr);
                List<BpmOrgAttributeConfig> configs = bpmOrgAttributeConfigService.list(queryWrapper);
                List<AdminUserRespDTO> subList = new ArrayList<>();
                configs.forEach(c -> {
                    subList.addAll(adminUserApi.getUsersByHumancodes(Arrays.asList(c.getAttributeValue().split(","))));
                });
                list = subList;
            }
            /*else if (dataType.contains("USERS") || dataType.contains("INITIATOR") || dataType.contains("HEAD")) {
                list = adminUserApi.getUsersByHumancodes(Arrays.asList(groupIds.split(",")));
            }*/
            else {
                list = adminUserApi.getUsersByHumancodes(Arrays.asList(groupIds.split(",")));
            }
            for (AdminUserRespDTO obj : list) {
                adminUserRespDTOS.add(BpmTaskConvert.INSTANCE.convert3(obj));
            }
        } else if (assigneeUser != null) {
            adminUserRespDTOS.add(assigneeUser);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcessInstance(String humanCode, BpmProcessInstanceCancelReqVO cancelReqVO) {

        List<Task> task = taskService.createTaskQuery().processInstanceId(cancelReqVO.getId()).list();
        if (CollectionUtils.isEmpty(task)) {
            throw new RuntimeException("流程未启动或已执行完成，删除失败");
        }

        // 校验流程实例存在
        ProcessInstance processInstance = getProcessInstance(cancelReqVO.getId());
        if (processInstance == null) {
            throw exception(PROCESS_INSTANCE_DELETE_FAIL_NOT_EXISTS);
        }
        // 只能删除自己的
        if (!Objects.equals(processInstance.getStartUserId(), humanCode)) {
            throw exception(PROCESS_INSTANCE_DELETE_FAIL_NOT_SELF);
        }

        // TODO 只能删除后面操作节点未进行审核、转派等操作的
        // TODO 如何判断未操作？ --目前以审批意见表（ ACT_HI_COMMENT ）无数据判断；但前提是各任务节点的操作都保存了记录:taskService.addComment()
        // TODO 转办、委派操作保存的数据，和审批（通过、不通过）操作保存数据操作表不一样；看后期是否需要区分转办、委派和审批操作，来判断是否可删除？
        /**
         * 发起人发起申请时，会调用BpmTaskServiceImpl的startFirstTask方法，Comment会有记录；
         * 故判断大于1，则表示后续节点有操作记录，则无法删除
         * update at 2022-10-11
         */
        List<Comment> commentList = taskService.getProcessInstanceComments(cancelReqVO.getId());
        if (commentList.size() > 1) {
            throw exception(PROCESS_INSTANCE_DELETE_FAIL_PROCESSED);
        }

        // 查询历史数据
        HistoricProcessInstance historicProcessInstance = getHistoricProcessInstance(cancelReqVO.getId());
        if (Objects.isNull(historicProcessInstance)) {
            throw new FlowableObjectNotFoundException("流程实例不存在: " + cancelReqVO.getId());
        }
        if (historicProcessInstance.getEndTime() != null) {
            historyService.deleteHistoricProcessInstance(historicProcessInstance.getId());
            return;
        }

        // 通过删除流程实例，实现流程实例的撤销,
        // 删除流程实例，执行任务ACT_RU_TASK. 任务会被删除。通过历史表查询
        // 会触发事件：FlowableEngineEventType.PROCESS_CANCELLED
        deleteProcessInstance(cancelReqVO.getId(),
                BpmProcessInstanceDeleteReasonEnum.DELETE_TASK.format(cancelReqVO.getReason()));
        // 删除历史流程实例
        historyService.deleteHistoricProcessInstance(cancelReqVO.getId());
        // 删除流程实例扩展表数据
        BpmProcessInstanceExt bpmProcessInstanceExt = new BpmProcessInstanceExt()
                .setStartUserId(humanCode)
                .setProcessInstanceId(cancelReqVO.getId());
        this.deleteProcessInstanceExt(bpmProcessInstanceExt);
        // 删除流程任务扩展表数据
        BpmTaskExt bpmTaskExt = new BpmTaskExt()
                .setProcessinstanceid(cancelReqVO.getId());
        bpmTaskService.deleteTaskExt(bpmTaskExt);
    }

    @Override
    public void deleteProcessInstanceExt(BpmProcessInstanceExt entity) {
        if (StringUtils.isNotBlank(entity.getStartUserId()) || StringUtils.isNotBlank(entity.getProcessInstanceId())) {
            QueryWrapper<BpmProcessInstanceExt> queryWrapper = new QueryWrapper<>();
            if (StringUtils.isNotBlank(entity.getStartUserId())) {
                queryWrapper.eq("startUserId", entity.getStartUserId());
            }
            if (StringUtils.isNotBlank(entity.getProcessInstanceId())) {
                queryWrapper.eq("processInstanceId", entity.getProcessInstanceId());
            }
            processInstanceExtMapper.delete(queryWrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> updateProcessInstance(String humanCode, BpmProcessInstanceUpdateReqVO updateReqVO) {
        String processInstanceId = updateReqVO.getProcessInstanceId();
        // 获取流程实例数据
        ProcessInstance processInstance = this.getProcessInstance(processInstanceId);
        if (processInstance == null) {
            throw new RuntimeException("流程实例不存在");
        }
        BpmProcessInstanceExt bpmProcessInstanceExt = processInstanceExtMapper.selectByProcessInstanceId(processInstanceId);
        if (bpmProcessInstanceExt == null) {
            throw new RuntimeException("流程实例扩展数据不存在");
        }
        // 获取当前正在执行的任务节点
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        if (CollectionUtils.isEmpty(taskList)) {
            throw new RuntimeException("流程未启动或已执行完成，操作失败");
        }
        // 判断当前流程是否已审批
        if (checkApproved(processInstance)) {
            // throw new RuntimeException("已有用户审批，无法修改");
            throw new RuntimeException("当前节点不在发起人任务节点，无法修改");
        }
        // 设置工作流的用户
        FlowableUtils.setAuthenticatedUserId(humanCode);

        // 提交的表单值
        Map<String, Object> variables = updateReqVO.getVariables();
        // 流转条件变量
        Map<String, Object> expressionVariables = updateReqVO.getExpressionVariables();
        expressionVariables.put(TaskConstants.PROCESS_INITIATOR, bpmProcessInstanceExt.getStartUserId());
        // 更新表单值
        runtimeService.setVariables(processInstanceId, expressionVariables);
        // 给第一步申请人节点设置任务执行人和意见
        String message = humanCode + "修改流程申请";
        Task task = bpmTaskService.startFirstTask(processInstance, expressionVariables, message);
        //TODO 设置下个环节办理人 修改时判断任务扩展表是否有getReturnTaskDefKey
        List<SelectNextUserVO> selectNextUserList = updateReqVO.getSelectNextUserList();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        designateNextApprover(processInstance, task, selectNextUserList, updateReqVO.getProcessDefinitionId(), updateReqVO.getTaskDefinitionKey(), task.getDescription(), bpmnModel);

        // 清理
        FlowableUtils.clearAuthenticatedUserId();

        // 更新流程实例的拓展表
        bpmProcessInstanceExt.setFormVariables(variables);
        bpmProcessInstanceExt.setUpdater(humanCode);
        bpmProcessInstanceExt.setUpdateTime(new Date());
        bpmProcessInstanceExt.setStatus(BpmProcessInstanceStatusEnum.RUNNING.getStatus());
        bpmProcessInstanceExt.setResult(BpmProcessInstanceResultEnum.PROCESS.getResult());
        processInstanceExtMapper.updateByProcessInstanceId(bpmProcessInstanceExt);
        // 保存表单变量的值
        iBpmFormFieldValueService.saveFormFieldValueData(bpmProcessInstanceExt.getStartUserId(), variables, processInstanceId, processInstance.getProcessDefinitionId());

        // 下一节点审批人发送消息
        bpmTaskService.pushMsgToNextTaskAssign(humanCode, processInstance, selectNextUserList, bpmnModel, task);

        Map<String, Object> map = new HashMap<>();
        map.put("definitionKey", task.getTaskDefinitionKey());
        map.put("definitionId", task.getProcessDefinitionId());
        map.put("instanceId", processInstance.getId());
        map.put("taskId", task.getId());
        map.put("variables", expressionVariables);
        // 借用此字段，传参跳转的任务节点
        map.put("returnTaskDefKey", task.getDescription());
        return map;
    }

    private void designateNextApprover(ProcessInstance processInstance, Task task, List<SelectNextUserVO> selectNextUserList, String processDefinitionId, String taskDefinitionKey, String description, BpmnModel bpmnModel) {
        if (CollUtil.isNotEmpty(selectNextUserList)) {
            BpmTaskApproveReqVO reqVO = new BpmTaskApproveReqVO();
            reqVO.setInstanceId(processInstance.getProcessInstanceId());
            reqVO.setDefinitionId(processDefinitionId);
            reqVO.setDefinitionKey(taskDefinitionKey);
            reqVO.setReturnTaskDefKey(description);
            bpmTaskService.designateNextApprover(selectNextUserList, reqVO, bpmnModel);
        }
    }

    /**
     * 判断当前流程是否已审批
     *
     * @param processInstance
     */
    @Override
    public boolean checkApproved(ProcessInstance processInstance) {
        boolean flag = false; // 是否审批标识
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
        List<Process> processList = bpmnModel.getProcesses();
        Process process = processList.get(0);
        // 获取开始节点：目前系统只有一个开始节点，开始节点后面跟着的是发起人节点
        List<StartEvent> startEventList = process.findFlowElementsOfType(StartEvent.class);
        StartEvent startEvent = startEventList.get(0);
        List<SequenceFlow> outgoingFlows = startEvent.getOutgoingFlows();
        // 开始节点后面的发起人节点的id
        String targetRef = outgoingFlows.get(0).getTargetRef();
        // //  获取所有的任务节点
        // List<UserTask> userTaskList = process.findFlowElementsOfType(UserTask.class);
        // // 发起人任务节点
        // UserTask submitUserTask = null;
        // for (UserTask userTask : userTaskList) {
        //     if (Objects.equals(targetRef, userTask.getId()) && (Objects.equals("${initiator}", userTask.getAssignee()) || Objects.equals(ProcessConstants.FLOW_SUBMITTER, userTask.getName()))) {
        //         submitUserTask = userTask;
        //         break;
        //     }
        // }
        // 查询当前运行任务节点 ACT_RU_TASK：当前任务节点处在发起人节点，则可以修改
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstance.getProcessInstanceId()).list();
        for (Task task : taskList) {
            if (Objects.equals(task.getTaskDefinitionKey(), targetRef)) {
                flag = false;
                break;
            } else {
                flag = true;
            }
        }
        // 或者没有后续节点审批的：可以判断ACT_RU_ACTINST表：
        // 1、select * from ACT_RU_ACTINST where ACT_TYPE_='userTask' and ACT_NAME_='发起人'  查出来只有一条数据，且 END_TIME_ is null，表示当前节点在发起人节点
        // 或者2、select * from ACT_RU_ACTINST where ACT_TYPE_='userTask' and END_TIME_ is not null的数据只有一条，表示后续节点未审批
        // add at 2022.10.11
        // TODO 2022-11-09：发起人节点的后续节点未审批的情况下，先改为不允许修改流程：
        // TODO 若此处放开，需要同步调整BpmTaskServiceImpl.startFirstTask()方法；若修改流程需要先删除正在执行的后续节点的任务，以及其他相关表的数据，如任务扩展表、flowable相关的表等；
        // List<ActivityInstance> activityInstanceList = runtimeService.createActivityInstanceQuery()
        //         .processInstanceId(processInstance.getId())
        //         .activityType(BpmnXMLConstants.ELEMENT_TASK_USER)
        //         .activityName(ProcessConstants.FLOW_SUBMITTER)
        //         .list();
        // if(activityInstanceList.size() == 1 && activityInstanceList.get(0).getEndTime() == null){
        //     flag = false;
        // }
        // long count = runtimeService.createActivityInstanceQuery()
        //         .processInstanceId(processInstance.getId())
        //         .activityType(BpmnXMLConstants.ELEMENT_TASK_USER)
        //         .finished()
        //         .count();
        // if(count==1){
        //     flag = false;
        // }

        return flag;
    }

    @Override
    public boolean hasProcessInstanceBykey(String key) {
        long count = historyService.createHistoricProcessInstanceQuery().processDefinitionKey(key).count();
        return count > 0;
    }
}
