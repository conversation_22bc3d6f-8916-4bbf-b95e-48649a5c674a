package com.sanyth.portal.bpm.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.bpm.mapper.BpmOrgAttributeMapper;
import com.sanyth.portal.bpm.model.BpmOrgAttribute;
import com.sanyth.portal.bpm.service.IBpmOrgAttributeService;
import com.sanyth.portal.core.base.BaseQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 流程组织关系管理 服务实现
 * Created by ZWB on 2022-11-03.
 */
@Service
public class BpmOrgAttributeServiceImpl extends ServiceImpl<BpmOrgAttributeMapper, BpmOrgAttribute> implements IBpmOrgAttributeService {

    @Override
    public Page<BpmOrgAttribute> queryPage(BaseQuery query) {
        Page<BpmOrgAttribute> page = new Page<>(query.getPage(), query.getPageSize());
        BpmOrgAttribute obj = JSON.toJavaObject(query.getQueryParam(), BpmOrgAttribute.class);
        Wrapper<BpmOrgAttribute> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public List<BpmOrgAttribute> queryList(BpmOrgAttribute attribute) {
        Wrapper<BpmOrgAttribute> wrapper = buildWrapper(attribute);
        return list(wrapper);
    }

    private Wrapper<BpmOrgAttribute> buildWrapper(BpmOrgAttribute bpmOrgAttribute){
        QueryWrapper<BpmOrgAttribute> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(bpmOrgAttribute.getAttributeName())) {
            wrapper.like("ATTRIBUTE_NAME", bpmOrgAttribute.getAttributeName());
        }
        return wrapper;
    }
}
