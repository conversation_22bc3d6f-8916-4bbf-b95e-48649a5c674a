package com.sanyth.portal.bpm.behavior;

import com.sanyth.portal.bpm.service.AdminUserApi;
import com.sanyth.portal.bpm.service.DeptApi;
import com.sanyth.portal.bpm.service.IBpmTaskAssignRuleService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;
import lombok.ToString;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.impl.bpmn.behavior.UserTaskActivityBehavior;
import org.flowable.engine.impl.bpmn.parser.factory.DefaultActivityBehaviorFactory;

import java.util.List;

/**
 * 自定义的 ActivityBehaviorFactory 实现类，目的如下：
 * 1. 自定义 {@link #createUserTaskActivityBehavior(UserTask)}：实现自定义的流程任务的 assignee 负责人的分配
 * 由于设计人员要求人员配置必须放在模型设计器右侧，此灵活配置分配规则的方案暂时舍弃！！
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmActivityBehaviorFactory extends DefaultActivityBehaviorFactory {

    @Setter
    private IBpmTaskAssignRuleService bpmTaskRuleService;
//    @Setter
//    private BpmUserGroupService userGroupService;

//    @Setter
//    private PermissionApi permissionApi;
    @Setter
    private DeptApi deptApi;
    @Setter
    private AdminUserApi adminUserApi;
    @Setter
    private List<BpmTaskAssignScript> scripts;

    @Override
    public UserTaskActivityBehavior createUserTaskActivityBehavior(UserTask userTask) {
        BpmUserTaskActivityBehavior userTaskActivityBehavior = new BpmUserTaskActivityBehavior(userTask);
        userTaskActivityBehavior.setBpmTaskRuleService(bpmTaskRuleService);
//        userTaskActivityBehavior.setPermissionApi(permissionApi);
        userTaskActivityBehavior.setDeptApi(deptApi);
//        userTaskActivityBehavior.setUserGroupService(userGroupService);
        userTaskActivityBehavior.setAdminUserApi(adminUserApi);
        userTaskActivityBehavior.setScripts(scripts);
        return userTaskActivityBehavior;
    }
}
