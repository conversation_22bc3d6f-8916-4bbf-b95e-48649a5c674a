package com.sanyth.portal.bpm.exception;

/**
 * 业务类异常
 *
 */
public class BpmBusinessException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    public static final String FAIL = "00001";
    public static final String FAIL_MSG = "error";
    private String errorCode;
    private String errorMsg;
    private Object errorBody;

    public static void createByErrorCode(String errorCode) {
        throw new BpmBusinessException(errorCode, "FAIL");
    }

    public static void createByErrorCode(String errorCode, Object errorBody) {
        throw new BpmBusinessException(errorCode, FAIL_MSG, errorBody);
    }

    public static void createByErrorMsg(String errorMsg) {
        throw new BpmBusinessException(FAIL, errorMsg);
    }

    public static void createByErrorMsg(String errorMsg, Object errorBody) {
        throw new BpmBusinessException(FAIL, errorMsg, errorBody);
    }

    public static void create(String errorCode, String errorMsg) {
        throw new BpmBusinessException(errorCode, errorMsg);
    }

    public static void create(String errorCode, String errorMsg, Object errorBody) {
        throw new BpmBusinessException(errorCode, errorMsg, errorBody);
    }

    public BpmBusinessException() {
    }

    public BpmBusinessException(String errorCode, String errorMsg) {
        super(errorMsg);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public BpmBusinessException(String errorCode, String errorMsg, Object errorBody) {
        super(errorMsg);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.errorBody = errorBody;
    }

    @Override
    public Throwable fillInStackTrace() {
        return null;
    }

    public String getErrorCode() {
        return this.errorCode;
    }

    public String getErrorMsg() {
        return this.errorMsg;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Object getErrorBody() {
        return this.errorBody;
    }

    public void setErrorBody(Object errorBody) {
        this.errorBody = errorBody;
    }
}
