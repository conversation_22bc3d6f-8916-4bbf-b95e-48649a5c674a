package com.sanyth.portal.bpm.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.bpm.model.BpmCustomColumn;
import com.sanyth.portal.core.base.BaseQuery;

import java.util.List;

/**
 * 自定义列 服务接口
 * Created by WDL on 2022-12-23.
 */
public interface IBpmCustomColumnService extends IService<BpmCustomColumn> {
        Page<BpmCustomColumn> queryPage(BaseQuery query);

        List<BpmCustomColumn> queryList(BpmCustomColumn entity);

        /**
         * 根据项目ID删除数据
         * @param projectId
         */
        void deleteByProjectId(String projectId);

        /**
         * 保存设定的自定义列数据
         * @param param
         */
        void saveDataByProjectId(JSONObject param);
}
