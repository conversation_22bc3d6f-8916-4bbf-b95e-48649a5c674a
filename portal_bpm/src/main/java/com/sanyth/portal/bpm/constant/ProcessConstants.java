package com.sanyth.portal.bpm.constant;

/**
 * 流程常量信息
 *
 */
public class ProcessConstants {

    public static final String USER_TYPE_STARTER = "starter";
    public static final String USER_TYPE_PARTICIPANT = "participant";
    public static final String USER_TYPE_CANDIDATE = "candidate";
    /**
     * 动态数据
     */
    public static final String DATA_TYPE = "dynamic";

    /**
     * 单个审批人
     */
    public static final String USER_TYPE_ASSIGNEE = "assignee";


    /**
     * 候选人
     */
    public static final String USER_TYPE_USERS = "candidateUsers";


    /**
     * 审批组
     */
    public static final String USER_TYPE_ROUPS = "candidateGroups";

    /**
     * 单个审批人
     */
    public static final String PROCESS_APPROVAL = "approval";

    /**
     * 会签人员
     */
    public static final String PROCESS_MULTI_INSTANCE_USER = "userList";

    /**
     * nameapace
     */
    public static final String NAMASPASE = "http://flowable.org/bpmn";

    /**
     * 会签节点
     */
    public static final String PROCESS_MULTI_INSTANCE = "multiInstance";

    /**
     * 自定义属性 dataType
     */
    public static final String PROCESS_CUSTOM_DATA_TYPE = "dataType";

    /**
     * 自定义属性 userType
     */
    public static final String PROCESS_CUSTOM_USER_TYPE = "userType";

    /**
     * 自定义属性 dataField
     */
    public static final String PROCESS_CUSTOM_DATA_FIELD = "dataField";

    /**
     * 发起人组织关系
     */
    public static final String START_USER_ORGATTRIBUTE_HANDLER = "AttributeHandler";


    /**
     * 流程跳过
     */
    public static final String FLOWABLE_SKIP_EXPRESSION_ENABLED = "_FLOWABLE_SKIP_EXPRESSION_ENABLED";
    /**
     * 提交人节点名称
     */
    public static final String FLOW_SUBMITTER = "发起人";

    /**
     * 驳回方式
     */
    public static final String RETURN_WAY_FRONT = "frontNode";     // 驳回上一节点
    public static final String RETURN_WAY_ANY = "anyNode";          // 驳回任意节点
    public static final String RETURN_WAY_ASSIGN = "assignNode";    // 驳回指定节点

    /**
     * 通用审核意见模板
     */
    public static final String BPM_COMMON_COMMENT_TEMPLATE = "BPM_COMMON_COMMENT_TEMPLATE";

    /**
     * 驳回后的审批方式: 默认是逐级审批的、直接到驳回操作的节点
     * 学校目前的需求是驳回之后的节点审批后，直接返回到驳回者（不用在经过中间的审批节点，
     * 比如第5个任务节点进行驳回到第1个任务节点，第1个任务节点审批或修改之后直接推送到第5个任务节点）
     * add by 2023-02-01
     */
    public static final String APPROVE_WAY_SKIP = "approve_way_skip";   // 跳级审批

    public final static String SPECIAL_GATEWAY_BEGIN_SUFFIX = "_begin";
    public final static String SPECIAL_GATEWAY_END_SUFFIX = "_end";
    /**
     * 任务节点多人审批信息模板
     */
    public static final String BPM_MULTI_TASK_COMMENT_TEMPLATE = "任务节点多人审批信息模板";

}
