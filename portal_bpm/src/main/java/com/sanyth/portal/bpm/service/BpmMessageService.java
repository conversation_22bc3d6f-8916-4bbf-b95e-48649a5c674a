package com.sanyth.portal.bpm.service;

import com.alibaba.fastjson.JSONObject;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.service.delegate.DelegateTask;

import java.util.List;
import java.util.Map;

/**
 * BPM 消息 Service 接口
 *
 */
public interface BpmMessageService {

    /**
     * 抄送信息
     *
     * @param task         任务
     * @param instance     实例
     * @param copyListJson 复制列表json
     */
    void saveNewCopyMessage(Task task, ProcessInstance instance, List<JSONObject> copyListJson);

    /**
     * 催办消息
     *
     * @param task         任务
     * @param instance     实例
     * @param copyListJson 复制列表json
     */
    void saveNewRemindMessage(ProcessInstance instance);

    void saveTaskNoticeMessage(DelegateTask delegateTask);
    void saveTaskNoticeMessage(DelegateExecution execution);

    void pushMessage(String pushchannel, String title, String content, String msgType, String moreUrl, Map<String, Object> map, String... userIds);

    /**
     * 给发起者发送消息
     * @param processInstance
     * @param result
     */
    void pushMsgToStartUser(HistoricProcessInstance processInstance, String result);
}
