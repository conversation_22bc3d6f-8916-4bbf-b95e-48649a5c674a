package com.sanyth.portal.bpm.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.bpm.mapper.BpmOrgAttributeConfigMapper;
import com.sanyth.portal.bpm.model.BpmOrgAttribute;
import com.sanyth.portal.bpm.model.BpmOrgAttributeConfig;
import com.sanyth.portal.bpm.service.IBpmOrgAttributeConfigService;
import com.sanyth.portal.bpm.service.IBpmOrgAttributeService;
import com.sanyth.portal.core.base.BaseQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 流程组织关系配置表 服务实现
 * Created by ZWB on 2022-11-03.
 */
@Service
public class BpmOrgAttributeConfigServiceImpl extends ServiceImpl<BpmOrgAttributeConfigMapper, BpmOrgAttributeConfig> implements IBpmOrgAttributeConfigService {

    @Resource
    private IBpmOrgAttributeService bpmOrgAttributeService;

    @Override
    public Page<BpmOrgAttributeConfig> queryPage(BaseQuery query) {
        Page<BpmOrgAttributeConfig> page = new Page<>(query.getPage(), query.getPageSize());
        BpmOrgAttributeConfig obj = JSON.toJavaObject(query.getQueryParam(), BpmOrgAttributeConfig.class);
        Wrapper<BpmOrgAttributeConfig> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public void edit(BpmOrgAttributeConfig bpmOrgAttributeConfig) {
        BpmOrgAttribute bpmOrgAttribute = bpmOrgAttributeService.getById(bpmOrgAttributeConfig.getAttributeId());
        bpmOrgAttributeConfig.setAttributeId(bpmOrgAttribute.getId());
        bpmOrgAttributeConfig.setOrgType(bpmOrgAttribute.getValueType());
        bpmOrgAttributeConfig.setValueType(bpmOrgAttribute.getReturnValueType());
        bpmOrgAttributeConfig.setAttributeName(bpmOrgAttributeConfig.getAttributeName());
        saveOrUpdate(bpmOrgAttributeConfig);
    }

    @Override
    public List<BpmOrgAttributeConfig> queryList(BpmOrgAttributeConfig config) {
        Wrapper<BpmOrgAttributeConfig> wrapper = buildWrapper(config);
        return list(wrapper);
    }

    private Wrapper<BpmOrgAttributeConfig> buildWrapper(BpmOrgAttributeConfig bpmOrgAttributeConfig){
        QueryWrapper<BpmOrgAttributeConfig> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(bpmOrgAttributeConfig.getAttributeId())) {
            wrapper.eq("ATTRIBUTE_ID", bpmOrgAttributeConfig.getAttributeId());
        }
        if (StringUtils.isNotBlank(bpmOrgAttributeConfig.getAttributeName())) {
            wrapper.like("ATTRIBUTE_NAME", bpmOrgAttributeConfig.getAttributeName());
        }
        if (StringUtils.isNotBlank(bpmOrgAttributeConfig.getOrg())) {
            wrapper.eq("ORG", bpmOrgAttributeConfig.getOrg());
        }
        if (StringUtils.isNotBlank(bpmOrgAttributeConfig.getOrgName())) {
            wrapper.like("ORG_NAME", bpmOrgAttributeConfig.getOrgName());
        }
        return wrapper;
    }
}
