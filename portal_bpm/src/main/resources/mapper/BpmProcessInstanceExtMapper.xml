<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanyth.portal.bpm.mapper.BpmProcessInstanceExtMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sanyth.portal.bpm.model.BpmProcessInstanceExt">
        <id column="ID" property="id" />
        <result column="STARTUSERID" property="startuserid" />
        <result column="NAME" property="name" />
        <result column="PROCESSINSTANCEID" property="processinstanceid" />
        <result column="FORMVARIABLES" property="formvariables" />
        <result column="PROCESSDEFINITIONID" property="processdefinitionid" />
        <result column="CATEGORY" property="category" />
        <result column="STATUS" property="status" />
        <result column="RESULT" property="result" />
        <result column="ENDTIME" property="endtime" />
        <result column="PXJLID" property="pxjlId" />
        <result column="CREATETIME" property="createtime" />
        <result column="CREATOR" property="creator" />
        <result column="UPDATER" property="updater" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, STARTUSERID, NAME, PROCESSINSTANCEID, FORMVARIABLES, PROCESSDEFINITIONID, CATEGORY, STATUS, RESULT, ENDTIME,PXJLID, CREATETIME, CREATOR, UPDATER
    </sql>

    <!--删除运行时节点信息-->
    <delete id="deleteRunActinstsByIds" parameterType="java.util.List">
        delete from act_ru_actinst where ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--删除历史节点信息-->
    <delete id="deleteHisActinstsByIds" parameterType="java.util.List">
        delete from act_hi_actinst where ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

</mapper>
