package com.sanyth.portal.bpm.convert;

import com.sanyth.portal.bpm.dto.BpmProcessDefinitionCreateReqDTO;
import com.sanyth.portal.bpm.model.BpmProcessDefinitionExt;
import com.sanyth.portal.bpm.model.BpmProcessDefinitionExt.BpmProcessDefinitionExtBuilder;
import com.sanyth.portal.bpm.vo.BpmProcessDefinitionPageItemRespVO;
import com.sanyth.portal.bpm.vo.BpmProcessDefinitionRespVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.flowable.engine.repository.ProcessDefinition;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T15:27:29+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 1.8.0_452 (Azul Systems, Inc.)"
)
public class BpmProcessDefinitionConvertImpl implements BpmProcessDefinitionConvert {

    @Override
    public BpmProcessDefinitionPageItemRespVO convert(ProcessDefinition bean) {
        if ( bean == null ) {
            return null;
        }

        BpmProcessDefinitionPageItemRespVO bpmProcessDefinitionPageItemRespVO = new BpmProcessDefinitionPageItemRespVO();

        bpmProcessDefinitionPageItemRespVO.setId( bean.getId() );
        bpmProcessDefinitionPageItemRespVO.setVersion( bean.getVersion() );
        bpmProcessDefinitionPageItemRespVO.setName( bean.getName() );
        bpmProcessDefinitionPageItemRespVO.setDescription( bean.getDescription() );
        bpmProcessDefinitionPageItemRespVO.setCategory( bean.getCategory() );
        bpmProcessDefinitionPageItemRespVO.setDeploymentId( bean.getDeploymentId() );
        bpmProcessDefinitionPageItemRespVO.setKey( bean.getKey() );

        return bpmProcessDefinitionPageItemRespVO;
    }

    @Override
    public BpmProcessDefinitionExt convert2(BpmProcessDefinitionCreateReqDTO bean) {
        if ( bean == null ) {
            return null;
        }

        BpmProcessDefinitionExtBuilder bpmProcessDefinitionExt = BpmProcessDefinitionExt.builder();

        bpmProcessDefinitionExt.modelId( bean.getModelId() );
        bpmProcessDefinitionExt.description( bean.getDescription() );
        List<String> list = bean.getFormFields();
        if ( list != null ) {
            bpmProcessDefinitionExt.formFields( new ArrayList<String>( list ) );
        }
        List<String> list1 = bean.getMobilefields();
        if ( list1 != null ) {
            bpmProcessDefinitionExt.mobilefields( new ArrayList<String>( list1 ) );
        }
        bpmProcessDefinitionExt.fieldsValues( bean.getFieldsValues() );
        bpmProcessDefinitionExt.formConf( bean.getFormConf() );
        if ( bean.getFormType() != null ) {
            bpmProcessDefinitionExt.formType( String.valueOf( bean.getFormType() ) );
        }
        bpmProcessDefinitionExt.formId( bean.getFormId() );
        bpmProcessDefinitionExt.formCustomCreatePath( bean.getFormCustomCreatePath() );
        bpmProcessDefinitionExt.formCustomViewPath( bean.getFormCustomViewPath() );

        return bpmProcessDefinitionExt.build();
    }

    @Override
    public BpmProcessDefinitionRespVO convert3(ProcessDefinition bean) {
        if ( bean == null ) {
            return null;
        }

        BpmProcessDefinitionRespVO bpmProcessDefinitionRespVO = new BpmProcessDefinitionRespVO();

        bpmProcessDefinitionRespVO.setSuspensionState( convertSuspendedToSuspensionState( bean.isSuspended() ) );
        bpmProcessDefinitionRespVO.setId( bean.getId() );
        bpmProcessDefinitionRespVO.setVersion( bean.getVersion() );
        bpmProcessDefinitionRespVO.setName( bean.getName() );
        bpmProcessDefinitionRespVO.setDescription( bean.getDescription() );
        bpmProcessDefinitionRespVO.setCategory( bean.getCategory() );
        bpmProcessDefinitionRespVO.setDeploymentId( bean.getDeploymentId() );
        bpmProcessDefinitionRespVO.setKey( bean.getKey() );

        return bpmProcessDefinitionRespVO;
    }

    @Override
    public void copyTo(BpmProcessDefinitionExt from, BpmProcessDefinitionRespVO to) {
        if ( from == null ) {
            return;
        }

        to.setDescription( from.getDescription() );
        if ( from.getFormType() != null ) {
            to.setFormType( Integer.parseInt( from.getFormType() ) );
        }
        else {
            to.setFormType( null );
        }
        to.setFormId( from.getFormId() );
        to.setFormConf( from.getFormConf() );
        if ( to.getFormFields() != null ) {
            List<String> list = from.getFormFields();
            if ( list != null ) {
                to.getFormFields().clear();
                to.getFormFields().addAll( list );
            }
            else {
                to.setFormFields( null );
            }
        }
        else {
            List<String> list = from.getFormFields();
            if ( list != null ) {
                to.setFormFields( new ArrayList<String>( list ) );
            }
        }
        if ( to.getMobilefields() != null ) {
            List<String> list1 = from.getMobilefields();
            if ( list1 != null ) {
                to.getMobilefields().clear();
                to.getMobilefields().addAll( list1 );
            }
            else {
                to.setMobilefields( null );
            }
        }
        else {
            List<String> list1 = from.getMobilefields();
            if ( list1 != null ) {
                to.setMobilefields( new ArrayList<String>( list1 ) );
            }
        }
        to.setFieldsValues( from.getFieldsValues() );
        to.setFormCustomCreatePath( from.getFormCustomCreatePath() );
        to.setFormCustomViewPath( from.getFormCustomViewPath() );
        to.setConditionIds( from.getConditionIds() );
        to.setModelId( from.getModelId() );
    }
}
