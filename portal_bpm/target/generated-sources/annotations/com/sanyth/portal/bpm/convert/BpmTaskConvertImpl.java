package com.sanyth.portal.bpm.convert;

import com.alibaba.fastjson.JSONObject;
import com.sanyth.portal.bpm.dto.AdminUserRespDTO;
import com.sanyth.portal.bpm.model.BpmTaskExt;
import com.sanyth.portal.bpm.vo.BpmCommentVo;
import com.sanyth.portal.bpm.vo.BpmTaskDonePageItemRespVO;
import com.sanyth.portal.bpm.vo.BpmTaskRespVO;
import com.sanyth.portal.bpm.vo.BpmTaskRespVO.User;
import com.sanyth.portal.bpm.vo.BpmTaskTodoPageItemRespVO;
import com.sanyth.portal.core.model.Organization;
import com.sanyth.portal.core.model.Role;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T15:27:29+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 1.8.0_452 (Azul Systems, Inc.)"
)
public class BpmTaskConvertImpl implements BpmTaskConvert {

    @Override
    public BpmTaskTodoPageItemRespVO convert1(Task bean) {
        if ( bean == null ) {
            return null;
        }

        BpmTaskTodoPageItemRespVO bpmTaskTodoPageItemRespVO = new BpmTaskTodoPageItemRespVO();

        bpmTaskTodoPageItemRespVO.setSuspensionState( convertSuspendedToSuspensionState( bean.isSuspended() ) );
        bpmTaskTodoPageItemRespVO.setId( bean.getId() );
        bpmTaskTodoPageItemRespVO.setName( bean.getName() );
        bpmTaskTodoPageItemRespVO.setClaimTime( bean.getClaimTime() );
        bpmTaskTodoPageItemRespVO.setCreateTime( bean.getCreateTime() );
        bpmTaskTodoPageItemRespVO.setTaskDefinitionKey( bean.getTaskDefinitionKey() );

        return bpmTaskTodoPageItemRespVO;
    }

    @Override
    public BpmTaskDonePageItemRespVO convert2(HistoricTaskInstance bean) {
        if ( bean == null ) {
            return null;
        }

        BpmTaskDonePageItemRespVO bpmTaskDonePageItemRespVO = new BpmTaskDonePageItemRespVO();

        bpmTaskDonePageItemRespVO.setId( bean.getId() );
        bpmTaskDonePageItemRespVO.setName( bean.getName() );
        bpmTaskDonePageItemRespVO.setClaimTime( bean.getClaimTime() );
        bpmTaskDonePageItemRespVO.setCreateTime( bean.getCreateTime() );
        bpmTaskDonePageItemRespVO.setTaskDefinitionKey( bean.getTaskDefinitionKey() );
        bpmTaskDonePageItemRespVO.setEndTime( bean.getEndTime() );
        bpmTaskDonePageItemRespVO.setDurationInMillis( bean.getDurationInMillis() );

        return bpmTaskDonePageItemRespVO;
    }

    @Override
    public com.sanyth.portal.bpm.vo.BpmTaskTodoPageItemRespVO.ProcessInstance convert(ProcessInstance processInstance, AdminUserRespDTO startUser) {
        if ( processInstance == null && startUser == null ) {
            return null;
        }

        com.sanyth.portal.bpm.vo.BpmTaskTodoPageItemRespVO.ProcessInstance processInstance1 = new com.sanyth.portal.bpm.vo.BpmTaskTodoPageItemRespVO.ProcessInstance();

        if ( processInstance != null ) {
            processInstance1.setId( processInstance.getId() );
            processInstance1.setName( processInstance.getProcessDefinitionName() );
            processInstance1.setStartUserId( processInstance.getStartUserId() );
            processInstance1.setProcessDefinitionId( processInstance.getProcessDefinitionId() );
        }
        if ( startUser != null ) {
            processInstance1.setStartUserNickname( startUser.getHumanName() );
            processInstance1.setOrganizationnames( startUser.getOrganizationnames() );
        }

        return processInstance1;
    }

    @Override
    public void copyToBpmTaskRespVO(BpmTaskExt from, BpmTaskRespVO to) {
        if ( from == null ) {
            return;
        }

        to.setName( from.getName() );
        to.setCreateTime( from.getCreateTime() );
        if ( to.getFormVariables() != null ) {
            Map<String, Object> map = from.getFormVariables();
            if ( map != null ) {
                to.getFormVariables().clear();
                to.getFormVariables().putAll( map );
            }
            else {
                to.setFormVariables( null );
            }
        }
        else {
            Map<String, Object> map = from.getFormVariables();
            if ( map != null ) {
                to.setFormVariables( new HashMap<String, Object>( map ) );
            }
        }
        to.setResult( from.getResult() );
        to.setReason( from.getReason() );
        if ( to.getFieldsPermissionJson() != null ) {
            List<JSONObject> list = from.getFieldsPermissionJson();
            if ( list != null ) {
                to.getFieldsPermissionJson().clear();
                to.getFieldsPermissionJson().addAll( list );
            }
            else {
                to.setFieldsPermissionJson( null );
            }
        }
        else {
            List<JSONObject> list = from.getFieldsPermissionJson();
            if ( list != null ) {
                to.setFieldsPermissionJson( new ArrayList<JSONObject>( list ) );
            }
        }
        to.setFormCustomApproveData( from.getFormCustomApproveData() );
        if ( to.getButtonsJson() != null ) {
            List<JSONObject> list1 = from.getButtonsJson();
            if ( list1 != null ) {
                to.getButtonsJson().clear();
                to.getButtonsJson().addAll( list1 );
            }
            else {
                to.setButtonsJson( null );
            }
        }
        else {
            List<JSONObject> list1 = from.getButtonsJson();
            if ( list1 != null ) {
                to.setButtonsJson( new ArrayList<JSONObject>( list1 ) );
            }
        }
        if ( to.getCopyListJson() != null ) {
            List<JSONObject> list2 = from.getCopyListJson();
            if ( list2 != null ) {
                to.getCopyListJson().clear();
                to.getCopyListJson().addAll( list2 );
            }
            else {
                to.setCopyListJson( null );
            }
        }
        else {
            List<JSONObject> list2 = from.getCopyListJson();
            if ( list2 != null ) {
                to.setCopyListJson( new ArrayList<JSONObject>( list2 ) );
            }
        }
        if ( to.getExtendedPropertiesJson() != null ) {
            List<JSONObject> list3 = from.getExtendedPropertiesJson();
            if ( list3 != null ) {
                to.getExtendedPropertiesJson().clear();
                to.getExtendedPropertiesJson().addAll( list3 );
            }
            else {
                to.setExtendedPropertiesJson( null );
            }
        }
        else {
            List<JSONObject> list3 = from.getExtendedPropertiesJson();
            if ( list3 != null ) {
                to.setExtendedPropertiesJson( new ArrayList<JSONObject>( list3 ) );
            }
        }
        to.setReturnTaskDefKey( from.getReturnTaskDefKey() );
    }

    @Override
    public BpmTaskRespVO convert3(HistoricTaskInstance bean) {
        if ( bean == null ) {
            return null;
        }

        BpmTaskRespVO bpmTaskRespVO = new BpmTaskRespVO();

        bpmTaskRespVO.setDefinitionKey( bean.getTaskDefinitionKey() );
        bpmTaskRespVO.setId( bean.getId() );
        bpmTaskRespVO.setName( bean.getName() );
        bpmTaskRespVO.setClaimTime( bean.getClaimTime() );
        bpmTaskRespVO.setCreateTime( bean.getCreateTime() );
        bpmTaskRespVO.setTaskDefinitionKey( bean.getTaskDefinitionKey() );
        bpmTaskRespVO.setEndTime( bean.getEndTime() );
        bpmTaskRespVO.setDurationInMillis( bean.getDurationInMillis() );

        return bpmTaskRespVO;
    }

    @Override
    public User convert3(AdminUserRespDTO bean) {
        if ( bean == null ) {
            return null;
        }

        User user = new User();

        user.setHumanId( bean.getHumanId() );
        user.setHumanCode( bean.getHumanCode() );
        user.setHumanName( bean.getHumanName() );
        user.setRoleId( bean.getRoleId() );
        user.setRoleName( bean.getRoleName() );
        user.setRoleKey( bean.getRoleKey() );
        user.setSex( bean.getSex() );
        List<Role> list = bean.getRoles();
        if ( list != null ) {
            user.setRoles( new ArrayList<Role>( list ) );
        }
        List<Organization> list1 = bean.getOrganizations();
        if ( list1 != null ) {
            user.setOrganizations( new ArrayList<Organization>( list1 ) );
        }
        user.setOrganizationnames( bean.getOrganizationnames() );
        user.setDutyid( bean.getDutyid() );
        user.setTelmobile1( bean.getTelmobile1() );

        return user;
    }

    @Override
    public void copyTo(BpmTaskExt from, BpmTaskDonePageItemRespVO to) {
        if ( from == null ) {
            return;
        }

        to.setName( from.getName() );
        to.setCreateTime( from.getCreateTime() );
        if ( to.getFormVariables() != null ) {
            Map<String, Object> map = from.getFormVariables();
            if ( map != null ) {
                to.getFormVariables().clear();
                to.getFormVariables().putAll( map );
            }
            else {
                to.setFormVariables( null );
            }
        }
        else {
            Map<String, Object> map = from.getFormVariables();
            if ( map != null ) {
                to.setFormVariables( new HashMap<String, Object>( map ) );
            }
        }
        if ( to.getExtendedPropertiesJson() != null ) {
            List<JSONObject> list = from.getExtendedPropertiesJson();
            if ( list != null ) {
                to.getExtendedPropertiesJson().clear();
                to.getExtendedPropertiesJson().addAll( list );
            }
            else {
                to.setExtendedPropertiesJson( null );
            }
        }
        else {
            List<JSONObject> list = from.getExtendedPropertiesJson();
            if ( list != null ) {
                to.setExtendedPropertiesJson( new ArrayList<JSONObject>( list ) );
            }
        }
        to.setResult( from.getResult() );
        to.setReason( from.getReason() );
    }

    @Override
    public com.sanyth.portal.bpm.vo.BpmTaskTodoPageItemRespVO.ProcessInstance convert(HistoricProcessInstance processInstance, AdminUserRespDTO startUser) {
        if ( processInstance == null && startUser == null ) {
            return null;
        }

        com.sanyth.portal.bpm.vo.BpmTaskTodoPageItemRespVO.ProcessInstance processInstance1 = new com.sanyth.portal.bpm.vo.BpmTaskTodoPageItemRespVO.ProcessInstance();

        if ( processInstance != null ) {
            processInstance1.setId( processInstance.getId() );
            processInstance1.setName( processInstance.getProcessDefinitionName() );
            processInstance1.setStartUserId( processInstance.getStartUserId() );
            processInstance1.setProcessDefinitionId( processInstance.getProcessDefinitionId() );
        }
        if ( startUser != null ) {
            processInstance1.setStartUserNickname( startUser.getHumanName() );
            processInstance1.setOrganizationnames( startUser.getOrganizationnames() );
        }

        return processInstance1;
    }

    @Override
    public BpmCommentVo convertBpmCommentVo(Comment comment) {
        if ( comment == null ) {
            return null;
        }

        BpmCommentVo bpmCommentVo = new BpmCommentVo();

        bpmCommentVo.setId( comment.getId() );
        bpmCommentVo.setUserId( comment.getUserId() );
        bpmCommentVo.setTime( comment.getTime() );
        bpmCommentVo.setTaskId( comment.getTaskId() );
        bpmCommentVo.setProcessInstanceId( comment.getProcessInstanceId() );
        bpmCommentVo.setType( comment.getType() );
        bpmCommentVo.setFullMessage( comment.getFullMessage() );

        return bpmCommentVo;
    }
}
