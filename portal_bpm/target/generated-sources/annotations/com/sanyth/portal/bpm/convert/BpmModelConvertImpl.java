package com.sanyth.portal.bpm.convert;

import com.sanyth.portal.bpm.dto.BpmModelMetaInfoRespDTO;
import com.sanyth.portal.bpm.dto.BpmProcessDefinitionCreateReqDTO;
import com.sanyth.portal.bpm.vo.BpmModeImportReqVO;
import com.sanyth.portal.bpm.vo.BpmModelBaseVO;
import com.sanyth.portal.bpm.vo.BpmModelUpdateReqVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.flowable.engine.repository.ProcessDefinition;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T15:27:29+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 1.8.0_452 (Azul Systems, Inc.)"
)
public class BpmModelConvertImpl implements BpmModelConvert {

    @Override
    public BpmModelUpdateReqVO convert(BpmModeImportReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        BpmModelUpdateReqVO bpmModelUpdateReqVO = new BpmModelUpdateReqVO();

        bpmModelUpdateReqVO.setId( bean.getId() );
        bpmModelUpdateReqVO.setKey( bean.getKey() );
        bpmModelUpdateReqVO.setName( bean.getName() );
        bpmModelUpdateReqVO.setDescription( bean.getDescription() );
        bpmModelUpdateReqVO.setCategory( bean.getCategory() );
        bpmModelUpdateReqVO.setBpmnXml( bean.getBpmnXml() );
        bpmModelUpdateReqVO.setFormType( bean.getFormType() );
        bpmModelUpdateReqVO.setFormId( bean.getFormId() );
        bpmModelUpdateReqVO.setFormCustomCreatePath( bean.getFormCustomCreatePath() );
        bpmModelUpdateReqVO.setFormCustomViewPath( bean.getFormCustomViewPath() );
        bpmModelUpdateReqVO.setFileId( bean.getFileId() );
        bpmModelUpdateReqVO.setType( bean.getType() );
        List<String> list = bean.getRoleid();
        if ( list != null ) {
            bpmModelUpdateReqVO.setRoleid( new ArrayList<String>( list ) );
        }
        List<String> list1 = bean.getOrgid();
        if ( list1 != null ) {
            bpmModelUpdateReqVO.setOrgid( new ArrayList<String>( list1 ) );
        }
        List<String> list2 = bean.getFunctionbtn();
        if ( list2 != null ) {
            bpmModelUpdateReqVO.setFunctionbtn( new ArrayList<String>( list2 ) );
        }
        bpmModelUpdateReqVO.setProcessDefinition( bean.getProcessDefinition() );
        List<String> list3 = bean.getPushChannel();
        if ( list3 != null ) {
            bpmModelUpdateReqVO.setPushChannel( new ArrayList<String>( list3 ) );
        }
        bpmModelUpdateReqVO.setContent( bean.getContent() );

        return bpmModelUpdateReqVO;
    }

    @Override
    public void copyTo(BpmModelMetaInfoRespDTO from, BpmProcessDefinitionCreateReqDTO to) {
        if ( from == null ) {
            return;
        }

        to.setDescription( from.getDescription() );
        to.setFormType( from.getFormType() );
        to.setFormId( from.getFormId() );
        to.setFormCustomCreatePath( from.getFormCustomCreatePath() );
        to.setFormCustomViewPath( from.getFormCustomViewPath() );
    }

    @Override
    public void copyTo(BpmModelMetaInfoRespDTO from, BpmModelBaseVO to) {
        if ( from == null ) {
            return;
        }

        to.setDescription( from.getDescription() );
        to.setFormType( from.getFormType() );
        to.setFormId( from.getFormId() );
        to.setFormCustomCreatePath( from.getFormCustomCreatePath() );
        to.setFormCustomViewPath( from.getFormCustomViewPath() );
        to.setContent( from.getContent() );
    }

    @Override
    public com.sanyth.portal.bpm.vo.BpmModelPageItemRespVO.ProcessDefinition convert(ProcessDefinition bean) {
        if ( bean == null ) {
            return null;
        }

        com.sanyth.portal.bpm.vo.BpmModelPageItemRespVO.ProcessDefinition processDefinition = new com.sanyth.portal.bpm.vo.BpmModelPageItemRespVO.ProcessDefinition();

        processDefinition.setId( bean.getId() );
        processDefinition.setVersion( bean.getVersion() );

        return processDefinition;
    }
}
