package com.sanyth.portal.bpm.convert;

import com.sanyth.portal.bpm.dto.AdminUserRespDTO;
import com.sanyth.portal.bpm.model.BpmProcessDefinitionExt;
import com.sanyth.portal.bpm.model.BpmProcessInstanceExt;
import com.sanyth.portal.bpm.vo.BpmProcessInstancePageItemRespVO;
import com.sanyth.portal.bpm.vo.BpmProcessInstancePageItemRespVO.Task;
import com.sanyth.portal.bpm.vo.BpmProcessInstanceRespVO;
import com.sanyth.portal.bpm.vo.BpmProcessInstanceRespVO.User;
import com.sanyth.portal.core.model.Organization;
import com.sanyth.portal.core.model.Role;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T15:27:29+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 1.8.0_452 (Azul Systems, Inc.)"
)
public class BpmProcessInstanceConvertImpl implements BpmProcessInstanceConvert {

    @Override
    public List<BpmProcessInstancePageItemRespVO> convertList(List<BpmProcessInstanceExt> list) {
        if ( list == null ) {
            return null;
        }

        List<BpmProcessInstancePageItemRespVO> list1 = new ArrayList<BpmProcessInstancePageItemRespVO>( list.size() );
        for ( BpmProcessInstanceExt bpmProcessInstanceExt : list ) {
            list1.add( convert( bpmProcessInstanceExt ) );
        }

        return list1;
    }

    @Override
    public BpmProcessInstancePageItemRespVO convert(BpmProcessInstanceExt bean) {
        if ( bean == null ) {
            return null;
        }

        BpmProcessInstancePageItemRespVO bpmProcessInstancePageItemRespVO = new BpmProcessInstancePageItemRespVO();

        bpmProcessInstancePageItemRespVO.setId( bean.getProcessInstanceId() );
        bpmProcessInstancePageItemRespVO.setName( bean.getName() );
        bpmProcessInstancePageItemRespVO.setProcessDefinitionId( bean.getProcessDefinitionId() );
        bpmProcessInstancePageItemRespVO.setCategory( bean.getCategory() );
        bpmProcessInstancePageItemRespVO.setStatus( bean.getStatus() );
        bpmProcessInstancePageItemRespVO.setResult( bean.getResult() );
        bpmProcessInstancePageItemRespVO.setCreateTime( bean.getCreateTime() );
        bpmProcessInstancePageItemRespVO.setEndTime( bean.getEndTime() );
        Map<String, Object> map = bean.getFormVariables();
        if ( map != null ) {
            bpmProcessInstancePageItemRespVO.setFormVariables( new HashMap<String, Object>( map ) );
        }
        bpmProcessInstancePageItemRespVO.setStartUserId( bean.getStartUserId() );
        bpmProcessInstancePageItemRespVO.setStartUserName( bean.getStartUserName() );

        return bpmProcessInstancePageItemRespVO;
    }

    @Override
    public List<Task> convertList2(List<org.flowable.task.api.Task> tasks) {
        if ( tasks == null ) {
            return null;
        }

        List<Task> list = new ArrayList<Task>( tasks.size() );
        for ( org.flowable.task.api.Task task : tasks ) {
            list.add( taskToTask( task ) );
        }

        return list;
    }

    @Override
    public BpmProcessInstanceRespVO convert2(HistoricProcessInstance bean) {
        if ( bean == null ) {
            return null;
        }

        BpmProcessInstanceRespVO bpmProcessInstanceRespVO = new BpmProcessInstanceRespVO();

        bpmProcessInstanceRespVO.setId( bean.getId() );
        bpmProcessInstanceRespVO.setName( bean.getName() );
        bpmProcessInstanceRespVO.setEndTime( bean.getEndTime() );
        bpmProcessInstanceRespVO.setBusinessKey( bean.getBusinessKey() );

        return bpmProcessInstanceRespVO;
    }

    @Override
    public void copyTo(BpmProcessInstanceExt from, BpmProcessInstanceRespVO to) {
        if ( from == null ) {
            return;
        }

        to.setName( from.getName() );
        to.setCategory( from.getCategory() );
        to.setStatus( from.getStatus() );
        to.setResult( from.getResult() );
        to.setCreateTime( from.getCreateTime() );
        to.setEndTime( from.getEndTime() );
        if ( to.getFormVariables() != null ) {
            Map<String, Object> map = from.getFormVariables();
            if ( map != null ) {
                to.getFormVariables().clear();
                to.getFormVariables().putAll( map );
            }
            else {
                to.setFormVariables( null );
            }
        }
        else {
            Map<String, Object> map = from.getFormVariables();
            if ( map != null ) {
                to.setFormVariables( new HashMap<String, Object>( map ) );
            }
        }
    }

    @Override
    public com.sanyth.portal.bpm.vo.BpmProcessInstanceRespVO.ProcessDefinition convert2(ProcessDefinition bean) {
        if ( bean == null ) {
            return null;
        }

        com.sanyth.portal.bpm.vo.BpmProcessInstanceRespVO.ProcessDefinition processDefinition = new com.sanyth.portal.bpm.vo.BpmProcessInstanceRespVO.ProcessDefinition();

        processDefinition.setId( bean.getId() );
        processDefinition.setName( bean.getName() );

        return processDefinition;
    }

    @Override
    public void copyTo(BpmProcessDefinitionExt from, com.sanyth.portal.bpm.vo.BpmProcessInstanceRespVO.ProcessDefinition to) {
        if ( from == null ) {
            return;
        }

        if ( from.getFormType() != null ) {
            to.setFormType( Integer.parseInt( from.getFormType() ) );
        }
        else {
            to.setFormType( null );
        }
        to.setFormId( from.getFormId() );
        to.setFormConf( from.getFormConf() );
        if ( to.getFormFields() != null ) {
            List<String> list = from.getFormFields();
            if ( list != null ) {
                to.getFormFields().clear();
                to.getFormFields().addAll( list );
            }
            else {
                to.setFormFields( null );
            }
        }
        else {
            List<String> list = from.getFormFields();
            if ( list != null ) {
                to.setFormFields( new ArrayList<String>( list ) );
            }
        }
        if ( to.getMobilefields() != null ) {
            List<String> list1 = from.getMobilefields();
            if ( list1 != null ) {
                to.getMobilefields().clear();
                to.getMobilefields().addAll( list1 );
            }
            else {
                to.setMobilefields( null );
            }
        }
        else {
            List<String> list1 = from.getMobilefields();
            if ( list1 != null ) {
                to.setMobilefields( new ArrayList<String>( list1 ) );
            }
        }
        to.setFieldsValues( from.getFieldsValues() );
        to.setFormCustomCreatePath( from.getFormCustomCreatePath() );
        to.setFormCustomViewPath( from.getFormCustomViewPath() );
        to.setConditionIds( from.getConditionIds() );
    }

    @Override
    public User convert2(AdminUserRespDTO bean) {
        if ( bean == null ) {
            return null;
        }

        User user = new User();

        user.setHumanId( bean.getHumanId() );
        user.setHumanCode( bean.getHumanCode() );
        user.setHumanName( bean.getHumanName() );
        user.setRoleId( bean.getRoleId() );
        user.setRoleName( bean.getRoleName() );
        user.setRoleKey( bean.getRoleKey() );
        user.setSex( bean.getSex() );
        List<Role> list = bean.getRoles();
        if ( list != null ) {
            user.setRoles( new ArrayList<Role>( list ) );
        }
        List<Organization> list1 = bean.getOrganizations();
        if ( list1 != null ) {
            user.setOrganizations( new ArrayList<Organization>( list1 ) );
        }
        user.setOrganizationnames( bean.getOrganizationnames() );
        user.setTelmobile1( bean.getTelmobile1() );

        return user;
    }

    protected Task taskToTask(org.flowable.task.api.Task task) {
        if ( task == null ) {
            return null;
        }

        Task task1 = new Task();

        task1.setId( task.getId() );
        task1.setName( task.getName() );
        task1.setDescription( task.getDescription() );

        return task1;
    }
}
