package com.sanyth.portal.bpm.convert;

import com.sanyth.portal.bpm.vo.BpmActivityRespVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.flowable.engine.history.HistoricActivityInstance;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T15:27:29+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 1.8.0_452 (Azul Systems, Inc.)"
)
public class BpmActivityConvertImpl implements BpmActivityConvert {

    @Override
    public List<BpmActivityRespVO> convertList(List<HistoricActivityInstance> list) {
        if ( list == null ) {
            return null;
        }

        List<BpmActivityRespVO> list1 = new ArrayList<BpmActivityRespVO>( list.size() );
        for ( HistoricActivityInstance historicActivityInstance : list ) {
            list1.add( convert( historicActivityInstance ) );
        }

        return list1;
    }

    @Override
    public BpmActivityRespVO convert(HistoricActivityInstance bean) {
        if ( bean == null ) {
            return null;
        }

        BpmActivityRespVO bpmActivityRespVO = new BpmActivityRespVO();

        bpmActivityRespVO.setKey( bean.getActivityId() );
        bpmActivityRespVO.setType( bean.getActivityType() );
        bpmActivityRespVO.setStartTime( bean.getStartTime() );
        bpmActivityRespVO.setEndTime( bean.getEndTime() );
        bpmActivityRespVO.setTaskId( bean.getTaskId() );

        return bpmActivityRespVO;
    }
}
