package com.sanyth.portal.bpm.convert;

import com.sanyth.portal.bpm.model.BpmTaskAssignRule;
import com.sanyth.portal.bpm.model.BpmTaskAssignRule.BpmTaskAssignRuleBuilder;
import com.sanyth.portal.bpm.vo.BpmTaskAssignRuleCreateReqVO;
import com.sanyth.portal.bpm.vo.BpmTaskAssignRuleRespVO;
import com.sanyth.portal.bpm.vo.BpmTaskAssignRuleUpdateReqVO;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T15:27:29+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 1.8.0_452 (Azul Systems, Inc.)"
)
public class BpmTaskAssignRuleConvertImpl implements BpmTaskAssignRuleConvert {

    @Override
    public BpmTaskAssignRuleRespVO convert(BpmTaskAssignRule bean) {
        if ( bean == null ) {
            return null;
        }

        BpmTaskAssignRuleRespVO bpmTaskAssignRuleRespVO = new BpmTaskAssignRuleRespVO();

        bpmTaskAssignRuleRespVO.setType( bean.getType() );
        bpmTaskAssignRuleRespVO.setOptions( stringSetToLongSet( bean.getOptions() ) );
        bpmTaskAssignRuleRespVO.setId( bean.getId() );

        return bpmTaskAssignRuleRespVO;
    }

    @Override
    public BpmTaskAssignRule convert(BpmTaskAssignRuleCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        BpmTaskAssignRuleBuilder bpmTaskAssignRule = BpmTaskAssignRule.builder();

        bpmTaskAssignRule.type( bean.getType() );
        bpmTaskAssignRule.options( longSetToStringSet( bean.getOptions() ) );

        return bpmTaskAssignRule.build();
    }

    @Override
    public BpmTaskAssignRule convert(BpmTaskAssignRuleUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        BpmTaskAssignRuleBuilder bpmTaskAssignRule = BpmTaskAssignRule.builder();

        bpmTaskAssignRule.id( bean.getId() );
        bpmTaskAssignRule.type( bean.getType() );
        bpmTaskAssignRule.options( longSetToStringSet( bean.getOptions() ) );

        return bpmTaskAssignRule.build();
    }

    @Override
    public List<BpmTaskAssignRule> convertList2(List<BpmTaskAssignRuleRespVO> list) {
        if ( list == null ) {
            return null;
        }

        List<BpmTaskAssignRule> list1 = new ArrayList<BpmTaskAssignRule>( list.size() );
        for ( BpmTaskAssignRuleRespVO bpmTaskAssignRuleRespVO : list ) {
            list1.add( bpmTaskAssignRuleRespVOToBpmTaskAssignRule( bpmTaskAssignRuleRespVO ) );
        }

        return list1;
    }

    protected Set<Long> stringSetToLongSet(Set<String> set) {
        if ( set == null ) {
            return null;
        }

        Set<Long> set1 = new HashSet<Long>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( String string : set ) {
            set1.add( Long.parseLong( string ) );
        }

        return set1;
    }

    protected Set<String> longSetToStringSet(Set<Long> set) {
        if ( set == null ) {
            return null;
        }

        Set<String> set1 = new HashSet<String>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( Long long1 : set ) {
            set1.add( String.valueOf( long1 ) );
        }

        return set1;
    }

    protected BpmTaskAssignRule bpmTaskAssignRuleRespVOToBpmTaskAssignRule(BpmTaskAssignRuleRespVO bpmTaskAssignRuleRespVO) {
        if ( bpmTaskAssignRuleRespVO == null ) {
            return null;
        }

        BpmTaskAssignRuleBuilder bpmTaskAssignRule = BpmTaskAssignRule.builder();

        bpmTaskAssignRule.id( bpmTaskAssignRuleRespVO.getId() );
        bpmTaskAssignRule.type( bpmTaskAssignRuleRespVO.getType() );
        bpmTaskAssignRule.options( longSetToStringSet( bpmTaskAssignRuleRespVO.getOptions() ) );

        return bpmTaskAssignRule.build();
    }
}
