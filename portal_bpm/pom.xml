<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sanyth_portal</artifactId>
        <groupId>com.sanythPortal</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <name>portal_bpm</name>
    <artifactId>portal_bpm</artifactId>

    <properties>
        <flowable.version>6.6.0</flowable.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.sanythPortal</groupId>
            <artifactId>portal_service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sanythPortal</groupId>
            <artifactId>portal_utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sanythPortal</groupId>
            <artifactId>portal-biz</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sanythPortal</groupId>
            <artifactId>portal-spring-boot-starter-mq</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!-- 工作流相关 flowable -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-basic</artifactId>
            <version>${flowable.version}</version>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-actuator</artifactId>
            <version>${flowable.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sanythPortal</groupId>
            <artifactId>portal_platform</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-ui-modeler-conf</artifactId>
            <version>${flowable.version}</version>
        </dependency>-->
        <!-- 工作流相关结束 -->
    </dependencies>

</project>