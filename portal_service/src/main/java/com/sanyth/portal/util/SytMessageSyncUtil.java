package com.sanyth.portal.util;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sanyth.portal.core.util.SpringUtils;
import com.sanyth.portal.model.SytMessage;
import com.sanyth.portal.service.ISytMessageService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class SytMessageSyncUtil {

    public static void sync(String data){
        ISytMessageService sytMessageService = SpringUtils.getObject(ISytMessageService.class);
        List<SytMessage> sytMessages = JSON.parseArray(data, SytMessage.class);
        sytMessages.forEach(msg->{
            try {
                long count = sytMessageService.count(Wrappers.<SytMessage>query().lambda().eq(SytMessage::getId, msg.getId()));
                if (count == 0) {
                    //                msg.setCreatedate(new Date());
                    msg.setCreateuser("sytadmin");
                    msg.setContent(Base64.decodeStr(msg.getContent()));
                    msg.setUserIds(JSON.parseArray(msg.getUserIds().get(0), String.class));

                    sytMessageService.saveMessageSend(msg);
                }

            } catch (Exception e) {
                log.error(e.getMessage());
            }

        });
    }
}
