package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.model.SytHallStatistics;

import java.util.List;

/**
 * 服务大厅首页统计数据 服务接口
 * Created by WDL on 2022-09-02.
 */
public interface ISytHallStatisticsService extends IService<SytHallStatistics> {
    Page<SytHallStatistics> queryPage(BaseQuery query);

    List<SytHallStatistics> queryList(SytHallStatistics resource);

    List<SytHallStatistics> getListByUser(CurrentUser currentUser, SytHallStatistics resource);
}
