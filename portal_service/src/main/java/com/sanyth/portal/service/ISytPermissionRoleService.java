package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytPermissionRole;
import com.sanyth.portal.param.RolePermissonParam;
import com.sanyth.portal.result.RolePermissonResult;

import java.util.List;

/**
 * ${table.comment} 服务接口
 * Created by JIANGPING on 2020-05-15.
 */
public interface ISytPermissionRoleService extends IService<SytPermissionRole> {
        void edit(RolePermissonParam param);
        List<RolePermissonResult> listManage();
        Page<SytPermissionRole> queryPage(BaseQuery query, SytPermissionRole sytPermissionRole);

        List<SytPermissionRole> getByAccount(String accountId);

}
