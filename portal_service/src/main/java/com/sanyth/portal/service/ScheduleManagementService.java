package com.sanyth.portal.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.model.ScheduleManagement;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

@Service
public class ScheduleManagementService {

    @Autowired
    MongoTemplate mongoTemplate;

    public void save(ScheduleManagement scheduleManagement) {
        scheduleManagement.setId(UUID.randomUUID().toString().replaceAll("-",""));
        mongoTemplate.save(scheduleManagement);
    }

    public void update(ScheduleManagement scheduleManagement) {
        Update update = new Update()
                .set("type", scheduleManagement.getType())
                .set("title", scheduleManagement.getTitle())
                .set("startTime", scheduleManagement.getStartTime())
                .set("endTime", scheduleManagement.getEndTime())
                .set("isRemind", scheduleManagement.getIsRemind())
                .set("reminderTime", scheduleManagement.getReminderTime())
                .set("wholeDay", scheduleManagement.getWholeDay())
                .set("repeatType", scheduleManagement.getRepeatType())
                .set("reminderMode", scheduleManagement.getReminderMode())
                .set("remark", scheduleManagement.getRemark())
                .set("cronExpression", scheduleManagement.getCronExpression())
                .set("humanCode", scheduleManagement.getHumanCode())
                .set("humanName", scheduleManagement.getHumanName());
        mongoTemplate.upsert(new Query().addCriteria(Criteria.where("id").is(scheduleManagement.getId())),
                update, ScheduleManagement.class);
    }

    public ScheduleManagement get(String idOrName) {
        Criteria criteria = new Criteria();
        criteria.orOperator(Criteria.where("id").is(idOrName), Criteria.where("title").is(idOrName));
        return mongoTemplate.findOne(new Query().addCriteria(criteria), ScheduleManagement.class);
    }


    public void delete(String... id) {
        if (id.length > 0) {
            List<String> list = Arrays.asList(id);
            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(list));
            mongoTemplate.remove(query, ScheduleManagement.class);
        }
    }

    public Page<ScheduleManagement> queryPage(BaseQuery query) {
        ScheduleManagement scheduleManagement = JSON.toJavaObject(query.getQueryParam(), ScheduleManagement.class);
        if (query.getCurrentUser() != null) {
            scheduleManagement.setHumanCode(query.getCurrentUser().getHumanCode());
        }
        Query q = getQuery(scheduleManagement);
        long count = mongoTemplate.count(q, ScheduleManagement.class);
        Pageable pageable = PageRequest.of(query.getPage() - 1,
                query.getPageSize(),
                Sort.by(Sort.Direction.DESC, "startTime"));
        List<ScheduleManagement> list = mongoTemplate.find(q.with(pageable), ScheduleManagement.class);
        Page<ScheduleManagement> objectPage = new Page<>();
        objectPage.setRecords(list);
        objectPage.setTotal(count);
        objectPage.setCurrent(query.getPage());
        objectPage.setSize(query.getPageSize());
        return objectPage;
    }


    private Query getQuery(ScheduleManagement scheduleManagement) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(scheduleManagement.getId()))
            criteria.and("id").in(scheduleManagement.getId().split(","));
        if (StringUtils.isNotBlank(scheduleManagement.getHumanCode()))
            criteria.and("humanCode").is(scheduleManagement.getHumanCode());
        if (StringUtils.isNotBlank(scheduleManagement.getIsRemind()))
            criteria.and("isRemind").is(scheduleManagement.getIsRemind());
        if (StringUtils.isNotBlank(scheduleManagement.getHumanName())) {
            Pattern pattern = Pattern.compile("^.*" + scheduleManagement.getHumanName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("humanName").regex(pattern);
        }
        if (StringUtils.isNotBlank(scheduleManagement.getTitle())) {
            Pattern pattern = Pattern.compile("^.*" + scheduleManagement.getTitle() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("title").regex(pattern);
        }
        if (StringUtils.isNotBlank(scheduleManagement.getStartTime()) && StringUtils.isNotBlank(scheduleManagement.getEndTime())) {
            criteria.orOperator(
                    criteria.where("startTime").gte(scheduleManagement.getStartTime()).lte(scheduleManagement.getEndTime()),
                    criteria.where("endTime").gte(scheduleManagement.getStartTime()).lte(scheduleManagement.getEndTime()));
        } else if (StringUtils.isNotBlank(scheduleManagement.getStartTime())) {
            criteria.and("startTime").lte(scheduleManagement.getStartTime());
        } else if (StringUtils.isNotBlank(scheduleManagement.getEndTime())) {
            criteria.and("endTime").gte(scheduleManagement.getEndTime());
        }

        if (StringUtils.isNotBlank(scheduleManagement.getSearch_date())) {
            criteria.andOperator(Criteria.where("startTime").lte(scheduleManagement.getSearch_date()).and("endTime").gte(scheduleManagement.getSearch_date()));
        }
        query.addCriteria(criteria);
        return query;
    }

    public List<ScheduleManagement> queryList(BaseQuery query){
        ScheduleManagement scheduleManagement = JSON.toJavaObject(query.getQueryParam(), ScheduleManagement.class);
        if (query.getCurrentUser() != null) {
            scheduleManagement.setHumanCode(query.getCurrentUser().getHumanCode());
        }
        Query q = getQuery(scheduleManagement);
        return mongoTemplate.find(q, ScheduleManagement.class);
    }

    public List<String> getTypeList(BaseQuery query) {
        ScheduleManagement scheduleManagement = JSON.toJavaObject(query.getQueryParam(), ScheduleManagement.class);
        scheduleManagement = scheduleManagement == null ? new ScheduleManagement() : scheduleManagement;
        if (query.getCurrentUser() != null) {
            scheduleManagement.setHumanCode(query.getCurrentUser().getHumanCode());
        }
        Query q = getQuery(scheduleManagement);
        return mongoTemplate.findDistinct(q, "type", ScheduleManagement.class, String.class);
    }

//    @Transactional(rollbackFor=Exception.class)
    public void edit(ScheduleManagement scheduleManagement, CurrentUser currentUser) {
        scheduleManagement.setHumanCode(currentUser.getHumanCode());
        scheduleManagement.setHumanName(currentUser.getHumanName());
        if (StringUtils.isNotBlank(scheduleManagement.getId())) {
            update(scheduleManagement);
        } else {
            scheduleManagement.setCreateDate(new Date());
            save(scheduleManagement);
        }
    }

}
