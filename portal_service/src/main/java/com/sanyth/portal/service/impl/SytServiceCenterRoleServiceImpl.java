package com.sanyth.portal.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.portal.model.SytServiceCenterRole;
import com.sanyth.portal.mapper.SytServiceCenterRoleMapper;
import com.sanyth.portal.service.ISytServiceCenterRoleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.core.base.BaseQuery;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-19.
 */
@Service
public class SytServiceCenterRoleServiceImpl extends ServiceImpl<SytServiceCenterRoleMapper, SytServiceCenterRole> implements ISytServiceCenterRoleService {

    @Override
    public Page<SytServiceCenterRole> queryPage(BaseQuery query, SytServiceCenterRole sytServiceCenterRole) {
        Page<SytServiceCenterRole> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<SytServiceCenterRole> wrapper = buildWrapper(sytServiceCenterRole);
        return page(page, wrapper);
    }

    private Wrapper<SytServiceCenterRole> buildWrapper(SytServiceCenterRole sytServiceCenterRole){
        QueryWrapper<SytServiceCenterRole> wrapper = new QueryWrapper<>();
        // Query condition...

        return wrapper;
    }
}
