package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytLuckyDrawAwardsMapper;
import com.sanyth.portal.model.SytLuckyDrawAwards;
import com.sanyth.portal.service.ISytLuckyDrawAwardsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 抽奖活动奖项 服务实现
 * Created by ZWB on 2023-12-18.
 */
@Service
public class SytLuckyDrawAwardsServiceImpl extends ServiceImpl<SytLuckyDrawAwardsMapper, SytLuckyDrawAwards> implements ISytLuckyDrawAwardsService {

    @Override
    public Page<SytLuckyDrawAwards> queryPage(BaseQuery query) {
        Page<SytLuckyDrawAwards> page = new Page<>(query.getPage(), query.getPageSize());
        SytLuckyDrawAwards obj = JSON.toJavaObject(query.getQueryParam(), SytLuckyDrawAwards.class);
        Wrapper<SytLuckyDrawAwards> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    private Wrapper<SytLuckyDrawAwards> buildWrapper(SytLuckyDrawAwards sytLuckyDrawAwards){
        QueryWrapper<SytLuckyDrawAwards> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(sytLuckyDrawAwards.getLuckyId())) {
            wrapper.eq("LUCKY_ID", sytLuckyDrawAwards.getLuckyId());
        }
        return wrapper;
    }
}
