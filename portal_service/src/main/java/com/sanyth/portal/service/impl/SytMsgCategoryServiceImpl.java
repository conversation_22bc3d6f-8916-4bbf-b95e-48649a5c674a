package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytMsgCategoryMapper;
import com.sanyth.portal.model.SytDataCategory;
import com.sanyth.portal.service.ISytMsgCategoryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-27.
 */
@Service
public class SytMsgCategoryServiceImpl extends ServiceImpl<SytMsgCategoryMapper, SytDataCategory> implements ISytMsgCategoryService {

    @Autowired
    SytMsgCategoryMapper msgCategoryMapper;

    @Override
    public Page<SytDataCategory> queryPage(BaseQuery query) {
        Page<SytDataCategory> page = new Page<>(query.getPage(), query.getPageSize());
        SytDataCategory obj = JSON.toJavaObject(query.getQueryParam(), SytDataCategory.class);
        Wrapper<SytDataCategory> wrapper = buildWrapper(obj);
        page = page(page, wrapper);
        return page;
    }

    private Wrapper<SytDataCategory> buildWrapper(SytDataCategory sytDataCategory) {
        QueryWrapper<SytDataCategory> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(sytDataCategory.getName())) {
            wrapper.like("name", sytDataCategory.getName());
        }
        if (StringUtils.isNotBlank(sytDataCategory.getDesktopId())) {
            wrapper.eq("DESKTOP_ID", sytDataCategory.getDesktopId());
        }
        if (StringUtils.isNotBlank(sytDataCategory.getModel())) {
            wrapper.eq("MODEL", sytDataCategory.getModel());
        }
        if (StringUtils.isNotBlank(sytDataCategory.getType())) {
            wrapper.eq("TYPE", sytDataCategory.getType());
        }

        return wrapper;
    }
}
