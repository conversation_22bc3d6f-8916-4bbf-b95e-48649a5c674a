package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytZxzxZxflbMapper;
import com.sanyth.portal.model.SytZxzxZxflb;
import com.sanyth.portal.service.ISytZxzxZxflbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 在线咨询分类 服务实现
 * Created by ZWB on 2024-10-30.
 */
@Service
public class SytZxzxZxflbServiceImpl extends ServiceImpl<SytZxzxZxflbMapper, SytZxzxZxflb> implements ISytZxzxZxflbService {

    @Override
    public Page<SytZxzxZxflb> queryPage(BaseQuery query) {
        Page<SytZxzxZxflb> page = new Page<>(query.getPage(), query.getPageSize());
        SytZxzxZxflb obj = JSON.toJavaObject(query.getQueryParam(), SytZxzxZxflb.class);
        Wrapper<SytZxzxZxflb> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public List<SytZxzxZxflb> queryList(BaseQuery query) {
        SytZxzxZxflb obj = JSON.toJavaObject(query.getQueryParam(), SytZxzxZxflb.class);
        Wrapper<SytZxzxZxflb> wrapper = buildWrapper(obj);
        return list(wrapper);
    }

    private Wrapper<SytZxzxZxflb> buildWrapper(SytZxzxZxflb sytZxzxZxflb){
        QueryWrapper<SytZxzxZxflb> wrapper = new QueryWrapper<>();
        // Query condition...
        if (sytZxzxZxflb != null) {
            if (StringUtils.isNotBlank(sytZxzxZxflb.getName())) {
                wrapper.like("name", sytZxzxZxflb.getName());
            }
        }

        return wrapper;
    }
}
