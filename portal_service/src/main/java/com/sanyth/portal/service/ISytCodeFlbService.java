package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.model.SytCodeFlb;

import java.util.List;

/**
 * ${table.comment} 服务接口
 * Created by ZWB on 2022-10-18.
 */
public interface ISytCodeFlbService extends IService<SytCodeFlb> {
        Page<SytCodeFlb> queryPage(BaseQuery query);

        List<SytCodeFlb> queryList(SytCodeFlb sytCodeFlb);
}
