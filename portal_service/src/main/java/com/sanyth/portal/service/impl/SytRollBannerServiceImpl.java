package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.FileOperationUtil;
import com.sanyth.portal.mapper.SytRollBannerMapper;
import com.sanyth.portal.model.SytRollBanner;
import com.sanyth.portal.service.ISytRollBannerService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 轮播图信息 服务实现
 * Created by JIANGPING on 2020-05-21.
 */
@Service
public class SytRollBannerServiceImpl extends ServiceImpl<SytRollBannerMapper, SytRollBanner> implements ISytRollBannerService {

    @Autowired
    FileOperationUtil fileOperationUtil;

    @Override
    public void delete(String... id) throws Exception {
        if (id != null && id.length > 0) {
            for (String i : id) {
                SytRollBanner sytRollBanner = getById(i);
                String img = sytRollBanner.getImg();
                if (StringUtils.isNotEmpty(img)) {
                    fileOperationUtil.removeByJson(img);
                }
                removeById(i);
            }
        }
    }

    @Override
    public void edit(SytRollBanner sytRollBanner) throws Exception {
        if (StringUtils.isNotEmpty(sytRollBanner.getId())) {
            SytRollBanner rollBannerIndb = getById(sytRollBanner.getId());
            sytRollBanner.setCreateTime(rollBannerIndb.getCreateTime());
            if (!sytRollBanner.getImg().equals(rollBannerIndb.getImg())) {
                fileOperationUtil.removeByJson(rollBannerIndb.getImg());
            }
        }
//        saveOrUpdateAllColumn(sytRollBanner);
        saveOrUpdate(sytRollBanner);
    }


    @Override
    public Page<SytRollBanner> queryPage(BaseQuery query) {
        Page<SytRollBanner> page = new Page<>(query.getPage(), query.getPageSize());
        SytRollBanner sytRollBanner = JSON.toJavaObject(query.getQueryParam(), SytRollBanner.class);
        QueryWrapper<SytRollBanner> wrapper = buildWrapper(sytRollBanner);
        if (query.getCurrentUser() != null) {
            wrapper.like("ROLE_ID", query.getCurrentUser().getRoleId()).or().isNull("ROLE_ID");
        } else {
            wrapper.isNull("ROLE_ID");
        }
        return page(page, wrapper);
    }

    private QueryWrapper<SytRollBanner> buildWrapper(SytRollBanner sytRollBanner) {
        QueryWrapper<SytRollBanner> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(sytRollBanner.getTitle())) {
            wrapper.like("TITLE", sytRollBanner.getTitle());
        }
        if (StringUtils.isNotBlank(sytRollBanner.getStatus())) {
            wrapper.eq("STATUS", sytRollBanner.getStatus());
        }
        wrapper.orderByAsc("SORT");
        return wrapper;
    }
}
