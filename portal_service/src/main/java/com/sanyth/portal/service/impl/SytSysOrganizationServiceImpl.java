package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytSysOrganizationMapper;
import com.sanyth.portal.model.SytSysOrganization;
import com.sanyth.portal.service.ISytSysOrganizationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 组织机构表 服务实现
 * Created by JIANGPING on 2020-05-14.
 */
@Service
public class SytSysOrganizationServiceImpl extends ServiceImpl<SytSysOrganizationMapper, SytSysOrganization> implements ISytSysOrganizationService {

    @Autowired
    SytSysOrganizationMapper organizationMapper;

    @Override
    public List<SytSysOrganization> getOrganizationByUser(String userId) {
        return organizationMapper.getOrganizationByUser(userId);
    }

    @Override
    public Page<SytSysOrganization> queryPage(BaseQuery query) {
        Page<SytSysOrganization> page = new Page<>(query.getPage(), query.getPageSize());
        SytSysOrganization sysOrganization = JSON.toJavaObject(query.getQueryParam(), SytSysOrganization.class);
        return page(page, buildWapper(sysOrganization));
    }

    @Override
    public List<SytSysOrganization> queryList(SytSysOrganization organization) {
        return list(buildWapper(organization));
    }

    @Override
    public List<SytSysOrganization> queryParentList() {
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        wrapper.isNull("parent").or().eq("parent", "");
        return list(wrapper);
    }

    @Override
    public List<SytSysOrganization> queryOrgDataInSC() {
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        wrapper.apply(" id in(select DISTINCT ORGID from SYT_SERVICE_CENTER where ORGID is not null )");
        wrapper.orderByAsc("displayorder");
        return list(wrapper);
    }

    @Override
    public List<String> getBpmOrgAttributeConfigList(Map<String, Object> map) {
        return organizationMapper.getBpmOrgAttributeConfigList(map);
    }


    private Wrapper buildWapper(SytSysOrganization organization){
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(organization.getCode()))
            wrapper.eq("code", organization.getCode());
        if(StringUtils.isNotBlank(organization.getValid()))
            wrapper.eq("valid", organization.getValid());
        if(StringUtils.isNotBlank(organization.getCategoryId()))
            wrapper.eq("categoryId", organization.getCategoryId());
        if(StringUtils.isNotBlank(organization.getParent()) && !"search".equals(organization.getParent()))
            wrapper.eq("parent", organization.getParent());
        if(StringUtils.isNotBlank(organization.getOrgname()))
            wrapper.like("orgname", organization.getOrgname());
        if ("notEmpty".equals(organization.getOrgHeadHumaCode())) {
            wrapper.isNotNull("ORGHEADHUMACODE");
        } else if (StringUtils.isNotBlank(organization.getOrgHeadHumaCode())) {
            wrapper.like("ORGHEADHUMACODE", organization.getOrgHeadHumaCode());
        }
        wrapper.orderByAsc("displayorder");
        return wrapper;
    }
}
