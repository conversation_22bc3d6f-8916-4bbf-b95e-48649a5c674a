package com.sanyth.portal.service.impl;
import com.sanyth.portal.model.SytPermissionAccountRole;
import com.sanyth.portal.mapper.SytPermissionAccountRoleMapper;
import com.sanyth.portal.service.ISytPermissionAccountRoleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-14.
 */
@Service
@Transactional
public class SytPermissionAccountRoleServiceImpl extends ServiceImpl<SytPermissionAccountRoleMapper, SytPermissionAccountRole> implements ISytPermissionAccountRoleService {

}
