package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytDesktopBlockUserMapper;
import com.sanyth.portal.model.SytDesktopBlockUser;
import com.sanyth.portal.service.ISytDesktopBlockUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户板块关联表 服务实现
 * Created by ZWB on 2022-04-18.
 */
@Service
public class SytDesktopBlockUserServiceImpl extends ServiceImpl<SytDesktopBlockUserMapper, SytDesktopBlockUser> implements ISytDesktopBlockUserService {

    @Autowired
    SytDesktopBlockUserMapper blockUserMapper;

    @Override
    public Page<SytDesktopBlockUser> queryPage(BaseQuery query) {
        Page<SytDesktopBlockUser> page = new Page<>(query.getPage(), query.getPageSize());
        SytDesktopBlockUser obj = JSON.toJavaObject(query.getQueryParam(), SytDesktopBlockUser.class);
        Wrapper<SytDesktopBlockUser> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public List<SytDesktopBlockUser> queryList(SytDesktopBlockUser blockUser) {
        Wrapper<SytDesktopBlockUser> wrapper = buildWrapper(blockUser);
        return blockUserMapper.selectList(wrapper);
    }

    private Wrapper<SytDesktopBlockUser> buildWrapper(SytDesktopBlockUser sytDesktopBlockUser){
        QueryWrapper<SytDesktopBlockUser> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotEmpty(sytDesktopBlockUser.getAccountId())) {
            wrapper.eq("ACCOUNT_ID", sytDesktopBlockUser.getAccountId());
        }
        if (StringUtils.isNotEmpty(sytDesktopBlockUser.getRoleId())) {
            wrapper.eq("ROLE_ID", sytDesktopBlockUser.getRoleId());
        }
        if (StringUtils.isNotEmpty(sytDesktopBlockUser.getDesktopId())) {
            wrapper.eq("DESKTOP_ID", sytDesktopBlockUser.getDesktopId());
        }
        return wrapper;
    }
}
