package com.sanyth.portal.service.impl;

import com.sanyth.portal.model.SytZnjqrQuestion;
import com.sanyth.portal.service.SytZnjqrQuestionRepository;
import com.sanyth.portal.service.SytZnjqrQuestionService;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class SytZnjqrQuestionServiceImpl implements SytZnjqrQuestionService {

    @Autowired
    private SytZnjqrQuestionRepository questionRepository;
    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Override
    public Long count() {
        return questionRepository.count();
    }

    @Override
    public SytZnjqrQuestion save(SytZnjqrQuestion question) {
        return questionRepository.save(question);
    }

    @Override
    public void delete(String... id) {
        questionRepository.deleteAllById(Arrays.asList(id));
    }

    @Override
    public List<SytZnjqrQuestion> queryList(SytZnjqrQuestion question,String fieldSort,String order) {
        NativeSearchQuery searchQuery = buildSearchQuery(question,fieldSort,order);
        List<SytZnjqrQuestion> list = new ArrayList<>();
        SearchHits<SytZnjqrQuestion> searchHits = elasticsearchRestTemplate.search(searchQuery, SytZnjqrQuestion.class);
        for (SearchHit<SytZnjqrQuestion> searchHit : searchHits) {
            list.add(searchHit.getContent());
        }
        return list;
    }

    @Override
    public Page<SytZnjqrQuestion> queryByPage(Integer pageNum, Integer pageSize, SytZnjqrQuestion question,String fieldSort,String order) {
        PageRequest pageRequest = PageRequest.of(pageNum, pageSize);
        NativeSearchQuery searchQuery = buildSearchQuery(question,fieldSort,order);
        searchQuery.setPageable(pageRequest);
        List<SytZnjqrQuestion> list = new ArrayList<>();
        SearchHits<SytZnjqrQuestion> searchHits = elasticsearchRestTemplate.search(searchQuery, SytZnjqrQuestion.class);
        for (SearchHit<SytZnjqrQuestion> searchHit : searchHits) {
            list.add(searchHit.getContent());
        }
        Page<SytZnjqrQuestion> page = new PageImpl<>(list, pageRequest, searchHits.getTotalHits());
        return page;
    }

    @Override
    public SytZnjqrQuestion getByid(String id) {
        SytZnjqrQuestion question = questionRepository.findById(id).get();
        return question;
    }

    private NativeSearchQuery buildSearchQuery(SytZnjqrQuestion question,String fieldSort,String order) {
        BoolQueryBuilder defaultQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(question.getTitle())) {
//            defaultQueryBuilder.should(matchQuery("title", question.getTitle()).boost(3))
//                    .should(matchQuery("content", question.getTitle()).boost(2))
//                    .should(matchQuery("fj", question.getTitle()).boost(1));
            defaultQueryBuilder.should(QueryBuilders.multiMatchQuery(question.getTitle(), "title", "content", "fj"));
        }
        if (StringUtils.isNotBlank(question.getSftj())) {
            defaultQueryBuilder.should(QueryBuilders.termQuery("sftj", question.getSftj()));
        }

        //组装条件
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder().withQuery(defaultQueryBuilder);
        if (StringUtils.isNotBlank(fieldSort) && StringUtils.isNotBlank(order)) {
            SortOrder sortOrder = "desc".equals(order) ? SortOrder.DESC : "asc".equals(order) ? SortOrder.ASC : SortOrder.DESC;
            FieldSortBuilder sortBuilder = SortBuilders.fieldSort(fieldSort).order(sortOrder);
            nativeSearchQueryBuilder.withSort(sortBuilder);
        }
        NativeSearchQuery searchQuery = nativeSearchQueryBuilder.build();
        return searchQuery;
    }

}
