package com.sanyth.portal.service.impl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytPermissionResourceMapper;
import com.sanyth.portal.mapper.SytPermissionResourceRoleMapper;
import com.sanyth.portal.mapper.SytPermissionRoleMapper;
import com.sanyth.portal.model.SytPermissionResource;
import com.sanyth.portal.model.SytPermissionResourceRole;
import com.sanyth.portal.model.SytPermissionRole;
import com.sanyth.portal.param.RolePermissonParam;
import com.sanyth.portal.result.RolePermissonResult;
import com.sanyth.portal.service.ISytPermissionRoleService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-15.
 */
@Service
public class SytPermissionRoleServiceImpl extends ServiceImpl<SytPermissionRoleMapper, SytPermissionRole> implements ISytPermissionRoleService {

    @Autowired
    SytPermissionResourceRoleMapper resourceRoleMapper;
    @Autowired
    SytPermissionResourceMapper resourceMapper;
    @Autowired
    SytPermissionRoleMapper sytPermissionRoleMapper;

    @Transactional
    @Override
    public void edit(RolePermissonParam param) {
        Map<String, String> resourceMap = getStringStringMap();

        SytPermissionRole permissionRole = new SytPermissionRole();
        BeanUtils.copyProperties(param, permissionRole);
        saveOrUpdate(permissionRole);
        Set<String> resourceIds = param.getResourceIds();

        Set<String> temp = new HashSet<>();
        for (String resourceId : resourceIds) {
            String parentId = resourceMap.get(resourceId);
            if (!resourceIds.contains(parentId)) {
                temp.add(parentId);
            }

        }
        resourceIds.addAll(temp);
        QueryWrapper<SytPermissionResourceRole> wrapper = new QueryWrapper<>();
        wrapper.eq("ROLE_ID", permissionRole.getId());
        resourceRoleMapper.delete(wrapper);
        if (CollectionUtils.isNotEmpty(resourceIds)) {
            resourceIds.forEach(resourceId -> {
                if (StringUtils.isNotEmpty(resourceId)) {
                    SytPermissionResourceRole resourceRole = new SytPermissionResourceRole();
                    resourceRole.setResourceId(resourceId);
                    resourceRole.setRoleId(permissionRole.getId());
                    resourceRoleMapper.insert(resourceRole);
                }
            });
        }
    }

    private Map<String, String> getStringStringMap() {
        Wrapper<SytPermissionResource> resourceWrapper = new QueryWrapper<>();
        List<SytPermissionResource> resources = resourceMapper.selectList(resourceWrapper);
        Map<String, String> resourceMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(resources)) {
            for (SytPermissionResource resource : resources) {
                if (StringUtils.isNotEmpty(resource.getParentId())) {
                    resourceMap.put(resource.getId(), resource.getParentId());
                }
            }
        }
        return resourceMap;
    }

    @Override
    public List<RolePermissonResult> listManage() {
        List<SytPermissionRole> list = list(new QueryWrapper<>());
        List<RolePermissonResult> newlist = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            List<SytPermissionResource> resources = resourceMapper.selectList(new QueryWrapper<>());

            list.forEach(role -> {
                RolePermissonResult rolePermissonResult = new RolePermissonResult();
                BeanUtils.copyProperties(role, rolePermissonResult);
                QueryWrapper<SytPermissionResourceRole> resourceRoleWrapper = new QueryWrapper<>();
                resourceRoleWrapper.eq("ROLE_ID", role.getId());
                List<SytPermissionResourceRole> resourceRoles = resourceRoleMapper.selectList(resourceRoleWrapper);
                Set<String> sets = new HashSet<>();
                if (CollectionUtils.isNotEmpty(resourceRoles)) {
                    resourceRoles.forEach(rr -> {
                        if (StringUtils.isNotEmpty(rr.getResourceId())) {
                            sets.add(rr.getResourceId());
                        }
                    });
                }

                // 找出父节点id
                Set<String> parents = new HashSet<>();
                sets.forEach(id -> {
                    resources.forEach(r -> {
                        boolean flag1 = id.equals(r.getId()) && StringUtils.isEmpty(r.getParentId());
                        boolean flag2 = id.equals(r.getParentId());
                        if (flag1 || flag2) {
                            parents.add(id);
                        }
                    });
                });
                // 剔除父节点
                sets.removeAll(parents);
                rolePermissonResult.setResourceIds(sets);
                newlist.add(rolePermissonResult);
            });
        }
        return newlist;
    }

    @Override
    public Page<SytPermissionRole> queryPage(BaseQuery query, SytPermissionRole sytPermissionRole) {
        Page<SytPermissionRole> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<SytPermissionRole> wrapper = buildWrapper(sytPermissionRole);
        return page(page, wrapper);
    }

    @Override
    public List<SytPermissionRole> getByAccount(String accountId) {
        return sytPermissionRoleMapper.getByAccount(accountId);
    }

    private Wrapper<SytPermissionRole> buildWrapper(SytPermissionRole sytPermissionRole) {
        Wrapper<SytPermissionRole> wrapper = new QueryWrapper<>();
        // Query condition...

        return wrapper;
    }
}
