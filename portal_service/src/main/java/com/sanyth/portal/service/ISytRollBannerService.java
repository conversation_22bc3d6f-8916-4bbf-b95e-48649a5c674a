package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytRollBanner;

/**
 * 轮播图信息 服务接口
 * Created by JIANGPING on 2020-05-21.
 */
public interface ISytRollBannerService extends IService<SytRollBanner> {
        void delete(String...id) throws Exception;
        void edit(SytRollBanner sytRollBanner) throws Exception;
        Page<SytRollBanner> queryPage(BaseQuery query);
}
