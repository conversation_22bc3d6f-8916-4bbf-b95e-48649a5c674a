package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.Constants;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.mapper.*;
import com.sanyth.portal.model.*;
import com.sanyth.portal.result.DesktopBlockResult;
import com.sanyth.portal.result.DesktopResult;
import com.sanyth.portal.service.ISytDesktopBlockUserService;
import com.sanyth.portal.service.ISytDesktopService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-27.
 */
@Service
public class SytDesktopServiceImpl extends ServiceImpl<SytDesktopMapper, SytDesktop> implements ISytDesktopService {

    @Autowired
    SytDesktopBlockMapper blockMapper;
    @Autowired
    SytDesktopBlockTabMapper blockTabMapper;
    @Autowired
    SytMsgCategoryMapper categoryMapper;
    @Autowired
    SytRollBannerMapper rollBannerMapper;
    @Autowired
    SytNewsMapper newsMapper;
    @Autowired
    OauthClientDetailsMapper clientDetailsMapper;
    @Autowired
    SytServiceCenterMapper serviceCenterMapper;
    @Autowired
    SytServiceCenterCollectMapper serviceCenterCollectMapper;
    @Autowired
    SytDesktopAppMapper desktopAppMapper;
    @Autowired
    ISytDesktopBlockUserService blockUserService;
    @Autowired
    SytDesktopBlockUserMapper blockUserMapper;

    @Override
    public void edit(JSONObject param) {
        SytDesktop sytDesktop = JSON.toJavaObject(param, SytDesktop.class);
        JSONArray orgIds = param.getJSONArray("orgIds");
        JSONArray roleIds = param.getJSONArray("roleIds");

        if (orgIds != null && orgIds.size() > 0) {
            sytDesktop.setOrgId(StringUtils.join(orgIds.toJavaList(String.class), ","));
        } else {
            sytDesktop.setOrgId("");
        }

        if (roleIds != null && roleIds.size() > 0) {
            sytDesktop.setRoleId(StringUtils.join(roleIds.toJavaList(String.class), ","));
        } else {
            sytDesktop.setRoleId("");
        }
        saveOrUpdate(sytDesktop);
    }

    @Override
    public List<DesktopResult> get(String desktopId, CurrentUser currentUser) {
        List<DesktopResult> desktopResults = new ArrayList<>();
        List<Map<String, Object>> allBlocktabs = blockTabMapper.getBlocktabs(null);
        Map<String, List<Map<String, Object>>> blocktabMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(allBlocktabs)) {
            for (Map<String, Object> m : allBlocktabs) {
                List<Map<String, Object>> list = null;
                Object blockId = m.get("blockId");
                if (blocktabMap.containsKey(blockId)) {
                    list = blocktabMap.get(blockId);
                } else {
                    list = new ArrayList<>();
                }
                list.add(m);
                blocktabMap.put(blockId.toString(), list);
            }
        }
        SytDesktop desktop = getById(desktopId);

        DesktopResult desktopResult = new DesktopResult();
        desktopResult.setId(desktopId);

        QueryWrapper<SytDesktopBlock> blockWrapper = new QueryWrapper<>();
//        blockWrapper.and(wrapper -> wrapper.eq("DESKTOP_ID", desktopId));
        List<SytDesktopBlockUser> blockUsers = new ArrayList<>();
        QueryWrapper<SytDesktopBlockUser> blockUserWrapper = new QueryWrapper<>();

        if (currentUser != null && !"syt_visitor".equals(currentUser.getHumanId())) {  //用户登录状态时
            if (StringUtils.isNotBlank(desktop.getType()) && desktop.getType().indexOf("移动端") > -1) {
                SytDesktopBlockUser blockUser = new SytDesktopBlockUser();
                blockUser.setDesktopId(desktopId);
                blockUser.setAccountId("sytadmin");
//                blockUser.setRoleId(currentUser.getRoleId());
                blockUsers = blockUserService.queryList(blockUser);
                List<String> blockIds = new ArrayList<>();
                blockUsers.forEach(bu -> blockIds.add(bu.getBlockId()));
                blockWrapper.in("id", blockIds);
            } else {
                blockUserWrapper.eq("DESKTOP_ID", desktopId).eq("ACCOUNT_ID", currentUser.getHumanCode()).eq("ROLE_ID", currentUser.getRoleId());
                long count = blockUserService.count(blockUserWrapper);
                if (count > 0) {    //用户板块关联表里有，按关联表里的id取板块
                    SytDesktopBlockUser blockUser = new SytDesktopBlockUser();
                    blockUser.setDesktopId(desktopId);
                    blockUser.setAccountId(currentUser.getHumanCode());
                    blockUser.setRoleId(currentUser.getRoleId());
                    blockUsers = blockUserService.queryList(blockUser);
                    List<String> blockIds = new ArrayList<>();
                    blockUsers.forEach(bu -> blockIds.add(bu.getBlockId()));
                    blockWrapper.in("id", blockIds);
                } else {    //用户没有配置板块的按管理sytadmin给当前角色设置的板块显示
                    SytDesktopBlockUser blockUser = new SytDesktopBlockUser();
                    blockUser.setDesktopId(desktopId);
                    blockUser.setAccountId("sytadmin");
                    blockUser.setRoleId(currentUser.getRoleId());
                    blockUsers = blockUserService.queryList(blockUser);
                    List<String> blockIds = new ArrayList<>();
                    blockUsers.forEach(bu -> blockIds.add(bu.getBlockId()));
                    blockWrapper.in("id", blockIds);
                }
            }

            blockWrapper.and(wrapper -> wrapper.isNull("ROLE_ID").or().like("ROLE_ID", currentUser.getRoleId()));
            blockWrapper.and(wrapper -> {
                wrapper.isNull("ORG_ID").or(subWrapper -> {
                    currentUser.getOrganizations().forEach(organization -> {
                        subWrapper.or().like("ORG_ID", organization.getId());
                    });
                });
            });
        } else {

            if (StringUtils.isBlank(desktop.getType()) || desktop.getType().indexOf("移动端") == -1) {     //非移动端的查询游客设置的桌面板块
                blockUserWrapper.eq("DESKTOP_ID", desktopId).eq("ACCOUNT_ID", "syt_visitor");
            }
            blockUsers = blockUserService.list(blockUserWrapper);
            List<String> blockIds = new ArrayList<>();
            blockUsers.forEach(bu -> blockIds.add(bu.getBlockId()));
            blockWrapper.eq("VISITOR_VISIBILITY", Constants.HAS_YES);
            blockWrapper.in("id", blockIds);
            blockWrapper.and(wrapper -> wrapper.isNull("ROLE_ID").or().isNull("ORG_ID"));
        }

        // 查询启用的数据
        blockWrapper.eq("status", Constants.STATUS_ENABLE);
        blockWrapper.orderByAsc("sort");
        List<SytDesktopBlock> blocks = blockMapper.selectList(blockWrapper);

        List<DesktopBlockResult> blockVos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(blocks)) {
            for (SytDesktopBlock b : blocks) {
                DesktopBlockResult blockVo = new DesktopBlockResult();
                BeanUtils.copyProperties(b, blockVo);

                List<Map<String, Object>> blocktabs = blocktabMap.get(b.getId());

                if (CollectionUtils.isNotEmpty(blocktabs)) {
                    int index = 0;
                    for (Map<String, Object> blocktab : blocktabs) {
                        blocktab.put("data", new ArrayList<>());
                        blocktab.put("index", ++index);
                    }
                }
                blockVo.setTabs(blocktabs == null ? new ArrayList<>() : blocktabs);
                blockUsers.forEach(bu->{
                    if (Objects.equals(blockVo.getId(), bu.getBlockId())) {
                        blockVo.setFrontConfig(bu.getFrontConfig());
                        blockVo.setHeight(bu.getHeight());
                    }
                });
                blockVos.add(blockVo);
            }
        }
        desktopResult.setBlocks(blockVos);
        QueryWrapper<SytDesktopApp> appWrapper = new QueryWrapper<>();
        appWrapper.and(wrapper -> wrapper.eq("DESKTOP_ID", desktopId));
        if (currentUser != null && !"syt_visitor".equals(currentUser.getHumanId())) {
            appWrapper.and(wrapper -> wrapper.isNull("ROLE_ID").or().like("ROLE_ID", currentUser.getRoleId()));
            appWrapper.and(wrapper -> {
                wrapper.isNull("ORG_ID").or(subWrapper -> {
                    currentUser.getOrganizations().forEach(organization -> {
                        subWrapper.or().like("ORG_ID", organization.getId());
                    });
                });
            });
        } else {
            appWrapper.eq("VISITOR_VISIBILITY", Constants.HAS_YES);
            appWrapper.and(wrapper -> wrapper.isNull("ROLE_ID").or().isNull("ORG_ID"));
        }
        appWrapper.eq("STATUS", "启用");
        appWrapper.orderByAsc("sort");
        List<SytDesktopApp> apps = desktopAppMapper.selectList(appWrapper);
        desktopResult.setApps(apps);

        desktopResults.add(desktopResult);
        return desktopResults;
    }

    @Override
    public List<SytDesktop> queryList(CurrentUser currentUser,SytDesktop sytDesktop) {
        QueryWrapper<SytDesktop> desktopWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(sytDesktop.getType())) {
            desktopWrapper.eq("TYPE", sytDesktop.getType());
        }
        if (currentUser != null && !"syt_visitor".equals(currentUser.getHumanId())) {
            desktopWrapper.and(wrapper -> wrapper.isNull("ACCOUNT_ID").or().like("ACCOUNT_ID", currentUser.getHumanCode()));
            desktopWrapper.and(wrapper -> wrapper.isNull("ROLE_ID").or().like("ROLE_ID", currentUser.getRoleId()));
            desktopWrapper.and(wrapper -> {
                wrapper.isNull("ORG_ID").or(subWrapper -> {
                    currentUser.getOrganizations().forEach(organization -> {
                        subWrapper.or().like("ORG_ID", organization.getId());
                    });
                });
            });
        } else {
            desktopWrapper.and(wrapper -> wrapper.eq("VISITOR_VISIBILITY", Constants.HAS_YES).isNull("ACCOUNT_ID").isNull("ROLE_ID").isNull("ORG_ID"));
        }
        desktopWrapper.orderByAsc("SORT");
        List<SytDesktop> sytDesktops = list(desktopWrapper);
        return sytDesktops;
    }

    private List<Map<String, Object>> copyData(List<SytNews> dataList, boolean returnContent) {
        List<Map<String, Object>> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(msg -> {
                Map<String, Object> map = new HashMap<>();
                map.put("title", msg.getTitle());
                map.put("date", new SimpleDateFormat("yyyy-MM-dd").format(msg.getCreateTime()));
                map.put("detailLinks", msg.getDetailLinks());
                map.put("id", msg.getId());
//                map.put("msgType", msg.getMsgType());
                map.put("orgName", msg.getOrgName());
                map.put("icon", msg.getIcon());
                if (returnContent) {
                    map.put("content", msg.getContent());
                }
                list.add(map);
            });
        }
        return list;
    }

    private List<Map<String, Object>> copyRollbanner(List<SytRollBanner> sytRollBanners) {
        List<Map<String, Object>> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sytRollBanners)) {
            sytRollBanners.forEach(roll -> {
                Map<String, Object> newroll = new HashMap<>();
                newroll.put("title", roll.getTitle());
                newroll.put("url", roll.getUrl());
                newroll.put("sort", roll.getSort());
                newroll.put("detail", roll.getDetail());
                list.add(newroll);
            });
        }
        return list;
    }

    @Override
    public Page<Map<String, Object>> queryPage(BaseQuery query) {
        Page<SytDesktop> page = new Page<>(query.getPage(), query.getPageSize());
        SytDesktop obj = JSON.toJavaObject(query.getQueryParam(), SytDesktop.class);
        Wrapper<SytDesktop> wrapper = buildWrapper(obj);
        page = page(page, wrapper);

        List<Map<String, Object>> newlist = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            page.getRecords().forEach(d -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", d.getId());
                map.put("remark", d.getRemark());
                map.put("name", d.getName());
                map.put("editable", d.getEditable());
                map.put("icon", d.getIcon());
                map.put("sort", d.getSort());
                map.put("type", d.getType());
                map.put("visitorVisibility", d.getVisitorVisibility());
                map.put("orgIds", StringUtils.isEmpty(d.getOrgId()) ? new ArrayList<>() : d.getOrgId().split(","));
                map.put("roleIds", StringUtils.isEmpty(d.getRoleId()) ? new ArrayList<>() : d.getRoleId().split(","));
                newlist.add(map);
            });
        }
        Page newpage = new Page<>();
        BeanUtils.copyProperties(page, newpage);
        newpage.setRecords(newlist);
        return newpage;
    }

    @Transactional
    @Override
    public void removeByUser(CurrentUser currentUser, String... id) {
        QueryWrapper<SytDesktop> wrapper = new QueryWrapper();
        if (currentUser!=null) {
            wrapper.eq("ACCOUNT_ID", currentUser.getHumanCode());
        }
        wrapper.in("id", Arrays.asList(id));
        remove(wrapper);
        if (currentUser != null) {
            //用户自建桌面删除，删除某桌面下自己账号、角色的配置
            blockUserMapper.removeBatchByUser(currentUser.getHumanCode(), currentUser.getRoleId(), Arrays.asList(id));
        } else {
            //管理员后端删除桌面同时删除该桌面下所有人的配置
            blockUserMapper.removeBatchByUser(null, null, Arrays.asList(id));
        }
    }

    private Wrapper<SytDesktop> buildWrapper(SytDesktop sytDesktop) {
        QueryWrapper<SytDesktop> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(sytDesktop.getName())) {
            wrapper.like("name", sytDesktop.getName());
        }
        if (StringUtils.isNotBlank(sytDesktop.getRoleId())) {
            wrapper.like("ROLE_ID", sytDesktop.getRoleId());
        }
        if (StringUtils.isNotBlank(sytDesktop.getVisitorVisibility())) {
            wrapper.eq("VISITOR_VISIBILITY", sytDesktop.getVisitorVisibility());
        }
        wrapper.isNull("ACCOUNT_ID");       //默认后台查询排除个人自建桌面
        wrapper.orderByAsc("SORT");
        return wrapper;
    }
}
