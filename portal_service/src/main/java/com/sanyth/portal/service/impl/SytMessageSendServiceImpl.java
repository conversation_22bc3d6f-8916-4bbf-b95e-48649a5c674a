package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytMessageSendMapper;
import com.sanyth.portal.model.SytMessageSend;
import com.sanyth.portal.service.ISytMessageSendService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ${table.comment} 服务实现
 * Created by ZWB on 2022-07-14.
 */
@Service
public class SytMessageSendServiceImpl extends ServiceImpl<SytMessageSendMapper, SytMessageSend> implements ISytMessageSendService {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Override
    public Page<SytMessageSend> queryPage(BaseQuery query) {
        Page<SytMessageSend> page = new Page<>(query.getPage(), query.getPageSize());
        SytMessageSend obj = JSON.toJavaObject(query.getQueryParam(), SytMessageSend.class);
        Wrapper<SytMessageSend> wrapper = buildWrapper(obj);
        page = page(page, wrapper);
        return page;
    }

    @Override
    public void deleteByMessageId(List<String> messagesIds) {
        QueryWrapper<SytMessageSend> wrapper = new QueryWrapper<>();
        wrapper.in("MESSAGEID", messagesIds);
        remove(wrapper);
    }

    @Override
    public void send(SytMessageSend sytMessageSend) {
        save(sytMessageSend);
        messagingTemplate.convertAndSendToUser(sytMessageSend.getUserid(),"/queue/subscribe", "您收到了新的消息");
    }

    private Wrapper<SytMessageSend> buildWrapper(SytMessageSend sytMessageSend){
        QueryWrapper<SytMessageSend> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(sytMessageSend.getMessageid())) {
            wrapper.eq("MESSAGEID", sytMessageSend.getMessageid());
        }
        if (StringUtils.isNotBlank(sytMessageSend.getUserid())) {
            wrapper.eq("USERID", sytMessageSend.getUserid());
        }
        if (StringUtils.isNotBlank(sytMessageSend.getTitle())) {
            wrapper.like("TITLE", sytMessageSend.getTitle());
        }
        if (StringUtils.isNotBlank(sytMessageSend.getType())) {
            wrapper.eq("TYPE", sytMessageSend.getType());
        }
        if (sytMessageSend.getStatus() != null) {
            wrapper.eq("STATUS", sytMessageSend.getStatus());
        }
        wrapper.orderByDesc("SENDTIME");
        return wrapper;
    }
}
