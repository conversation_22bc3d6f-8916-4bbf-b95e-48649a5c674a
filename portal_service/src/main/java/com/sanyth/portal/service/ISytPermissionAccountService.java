package com.sanyth.portal.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.core.common.Resp;
import com.sanyth.portal.model.SytPermissionAccount;
import com.sanyth.portal.pojo.AccountExcel;
import com.sanyth.portal.pojo.ImportErrorInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 用户信息表 服务接口
 * Created by JIANGPING on 2020-05-14.
 */
public interface ISytPermissionAccountService extends IService<SytPermissionAccount> {

    SytPermissionAccount getByHumancode(String humancode);

    List<SytPermissionAccount> queryList(SytPermissionAccount account);

    Page<SytPermissionAccount> queryPage(BaseQuery query);

    Map<String,Object> edit(JSONObject params) throws Exception;

    void delete(String... id) throws Exception;

    Resp updateInfo(SytPermissionAccount account);

    Resp changePasswd(String oldpassword, String newpassword, String newpasswords, CurrentUser currentUser);

    List<SytPermissionAccount> getUsersByDeptIds(Collection<String> deptIds);

    List<SytPermissionAccount> getUsersByPostIds(Collection<String> postIds);

    List<SytPermissionAccount> getUsersByRoleIds(Collection<String> roleIds);

    String updatePermissionCacheByToken(SytPermissionAccount account);

    Resp resetPasswordByAdmin(JSONObject param);

    /**
     * 更新人员排序
     */
    Resp updateDisplayorder(Map<String, Object> param);

    List<ImportErrorInfo> importUpdateDisplayorder(List<AccountExcel> accountExcels);
}
