package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytLuckyDraw;
import com.sanyth.portal.model.SytLuckyDrawParticipator;

import java.util.List;

/**
 * 抽奖活动 服务接口
 * Created by ZWB on 2023-12-18.
 */
public interface ISytLuckyDrawService extends IService<SytLuckyDraw> {
        Page<SytLuckyDraw> queryPage(BaseQuery query);

        List<SytLuckyDrawParticipator> drawLottery(SytLuckyDraw luckyDraw, Integer headcount);
}
