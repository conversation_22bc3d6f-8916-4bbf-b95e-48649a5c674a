package com.sanyth.portal.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.model.SytDesktopBlock;
import com.sanyth.portal.result.DesktopBlockResult;

import java.util.List;
import java.util.Map;

/**
 * ${table.comment} 服务接口
 * Created by JIANGPING on 2020-05-28.
 */
public interface ISytDesktopBlockService extends IService<SytDesktopBlock> {
    void edit(JSONObject param);

    Page<Map<String, Object>> queryPage(BaseQuery query);

    /**
     * 用户保存自己的桌面板块
     *
     * @param currentUser 当前用户
     * @param blocks      块
     */
    void saveBatchByUser(String desktopId,CurrentUser currentUser, JSONArray blocks);

    List<DesktopBlockResult> getBlockList(String desktopId, CurrentUser currentUser);

    void pushDesktop(JSONObject param);
}
