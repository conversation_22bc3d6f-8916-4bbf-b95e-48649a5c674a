package com.sanyth.portal.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytSysParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Created by JIANGPING on 2020/5/15.
 */
@Service
public class SytSysParamService {

    @Autowired
    MongoTemplate mongoTemplate;

    public void save(SytSysParam sytSysParam) {
        sytSysParam.setId(UUID.randomUUID().toString().replaceAll("-",""));
        mongoTemplate.save(sytSysParam);
    }

    public void update(SytSysParam param) {
        Update update = new Update()
                .set("img", param.getImg())
                .set("name", param.getName())
                .set("value", param.getValue())
                .set("type", param.getType())
                .set("status", param.getStatus())
                .set("sort", param.getSort())
                .set("bz", param.getBz());
        mongoTemplate.upsert(new Query().addCriteria(Criteria.where("id").is(param.getId())),
                update, SytSysParam.class);
    }

    public SytSysParam get(String idOrName) {
        Criteria criteria = new Criteria();
        criteria.and("status").is(0);
        criteria.orOperator(Criteria.where("id").is(idOrName), Criteria.where("name").is(idOrName),
                Criteria.where("type").is(idOrName));
        return mongoTemplate.findOne(new Query().addCriteria(criteria), SytSysParam.class);
    }


    public void delete(String... id) {
        if (id.length > 0) {
            List<String> list = Arrays.asList(id);
            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(list));
            mongoTemplate.remove(query, SytSysParam.class);
        }
    }

    public Page<SytSysParam> queryPage(BaseQuery query) {
        SytSysParam sysParam = JSON.toJavaObject(query.getQueryParam(), SytSysParam.class);
        Query q = getQuery(sysParam);
        long count = mongoTemplate.count(q, SytSysParam.class);
        Pageable pageable = PageRequest.of(query.getPage() - 1,
                query.getPageSize(),
                Sort.by(Sort.Direction.ASC,"sort", "createDate"));
        List<SytSysParam> list = mongoTemplate.find(q.with(pageable), SytSysParam.class);
        Page<SytSysParam> objectPage = new Page<>();
        objectPage.setRecords(list);
        objectPage.setTotal(count);
        objectPage.setCurrent(query.getPage());
        objectPage.setSize(query.getPageSize());
        return objectPage;
    }


    private Query getQuery(SytSysParam param) {
        Query query = new Query();
        if (StringUtils.isNotBlank(param.getId())) {
            Criteria criteria = Criteria.where("id").in(param.getId().split(","));
            query.addCriteria(criteria);
        }
        if (StringUtils.isNotBlank(param.getName())) {
            Pattern pattern = Pattern.compile("^.*" + param.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            Criteria criteria = Criteria.where("name").regex(pattern);
            query.addCriteria(criteria);
        }
        if (StringUtils.isNotBlank(param.getValue())) {
            Pattern pattern = Pattern.compile("^.*" + param.getValue() + ".*$", Pattern.CASE_INSENSITIVE);
            Criteria criteria = Criteria.where("value").regex(pattern);
            query.addCriteria(criteria);
        }
        if (StringUtils.isNotBlank(param.getType())) {
            Pattern pattern = Pattern.compile("^.*" + param.getType() + ".*$", Pattern.CASE_INSENSITIVE);
            Criteria criteria = Criteria.where("type").regex(pattern);
            query.addCriteria(criteria);
        }
        if (param.getStatus()!=null) {
            Criteria criteria = Criteria.where("status").is(param.getStatus());
            query.addCriteria(criteria);
        }
        return query;
    }

    public List<SytSysParam> queryList(BaseQuery query){
        SytSysParam sysParam = JSON.toJavaObject(query.getQueryParam(), SytSysParam.class);
        Query q = getQuery(sysParam);
        return mongoTemplate.find(q, SytSysParam.class);
    }
}
