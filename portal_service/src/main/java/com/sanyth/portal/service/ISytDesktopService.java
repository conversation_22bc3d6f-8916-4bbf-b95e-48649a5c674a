package com.sanyth.portal.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.model.SytDesktop;
import com.sanyth.portal.result.DesktopResult;

import java.util.List;
import java.util.Map;

/**
 * ${table.comment} 服务接口
 * Created by JIANGPING on 2020-05-27.
 */
public interface ISytDesktopService extends IService<SytDesktop> {
        void edit(JSONObject param);
        List<SytDesktop> queryList(CurrentUser currentUser,SytDesktop sytDesktop);
        List<DesktopResult> get(String desktopId, CurrentUser currentUser);
        Page<Map<String, Object>> queryPage(BaseQuery query);

        void removeByUser(CurrentUser currentUser, String... id);
}
