package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.model.SytMobileNavigation;

import java.util.List;

/**
 * 移动端底部导航栏 服务接口
 * Created by WDL on 2023-01-10.
 */
public interface ISytMobileNavigationService extends IService<SytMobileNavigation> {
    Page<SytMobileNavigation> queryPage(BaseQuery query);

    List<SytMobileNavigation> queryList(SytMobileNavigation entity);

    /**
     * 查询当前用户可见的导航栏数据
     * @param currentUser
     * @param entity
     * @return java.util.List<com.sanyth.portal.model.SytMobileNavigation>
     */
    List<SytMobileNavigation> getListByUser(CurrentUser currentUser, SytMobileNavigation entity);

    /**
     * 更新状态
     * @param id
     * @param state
     */
    void updateState(String id, String state);
}
