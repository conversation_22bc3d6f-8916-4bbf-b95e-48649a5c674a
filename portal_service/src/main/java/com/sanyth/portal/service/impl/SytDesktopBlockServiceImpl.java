package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.Constants;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.mapper.SytDesktopBlockMapper;
import com.sanyth.portal.mapper.SytDesktopBlockTabMapper;
import com.sanyth.portal.mapper.SytDesktopBlockUserMapper;
import com.sanyth.portal.model.SytDesktopBlock;
import com.sanyth.portal.model.SytDesktopBlockTab;
import com.sanyth.portal.model.SytDesktopBlockUser;
import com.sanyth.portal.model.SytPermissionAccount;
import com.sanyth.portal.result.DesktopBlockResult;
import com.sanyth.portal.service.ISytDesktopBlockService;
import com.sanyth.portal.service.ISytDesktopBlockUserService;
import com.sanyth.portal.service.ISytPermissionAccountService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-28.
 */
@Service
public class SytDesktopBlockServiceImpl extends ServiceImpl<SytDesktopBlockMapper, SytDesktopBlock> implements ISytDesktopBlockService {

    @Autowired
    SytDesktopBlockMapper blockMapper;
    @Autowired
    SytDesktopBlockTabMapper blockTabMapper;
    @Autowired
    SytDesktopBlockUserMapper blockUserMapper;
    @Autowired
    ISytDesktopBlockUserService blockUserService;
    @Autowired
    ISytPermissionAccountService userService;

    @Transactional
    @Override
    public void edit(JSONObject param) {
        SytDesktopBlock block = JSON.toJavaObject(param, SytDesktopBlock.class);
        JSONArray orgIds = param.getJSONArray("orgIds");
        JSONArray roleIds = param.getJSONArray("roleIds");
        JSONArray categoryIds = param.getJSONArray("categoryIds");

        if (orgIds != null && orgIds.size() > 0) {
            block.setOrgId(StringUtils.join(orgIds.toJavaList(String.class), ","));
        }

        if (roleIds != null && roleIds.size() > 0) {
            block.setRoleId(StringUtils.join(roleIds.toJavaList(String.class), ","));
        }

        if (StringUtils.isNotBlank(block.getId())) {
            QueryWrapper<SytDesktopBlockTab> blockTabWrapper = new QueryWrapper<>();
            blockTabWrapper.eq("BLOCK_ID", block.getId());
            blockTabMapper.delete(blockTabWrapper);
//            updateAllColumnById(block);
            saveOrUpdate(block);
        } else {
            save(block);
        }

        if (categoryIds != null && categoryIds.size() > 0) {
            categoryIds.forEach(cateid -> {
                SytDesktopBlockTab blockTab = new SytDesktopBlockTab();
                blockTab.setBlockId(block.getId());
                blockTab.setCategoryId((String) cateid);
                blockTabMapper.insert(blockTab);
            });
        }
    }

    @Override
    public Page<Map<String, Object>> queryPage(BaseQuery query) {
        Page<Map<String, Object>> page = new Page<>(query.getPage(), query.getPageSize());
        JSONObject queryParam = query.getQueryParam();
        Map map = JSON.toJavaObject(queryParam, Map.class);
        List<Map<String, Object>> list = blockMapper.queryList(map, page);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(m -> {
                String orgId = (String) m.get("orgId");
                String roleId = (String) m.get("roleId");
                m.put("orgIds", StringUtils.isEmpty(orgId) ? new ArrayList<>() : orgId.split(","));
                m.put("roleIds", StringUtils.isEmpty(roleId) ? new ArrayList<>() : roleId.split(","));

                QueryWrapper<SytDesktopBlockTab> blockTabWrapper = new QueryWrapper<>();
                blockTabWrapper.eq("BLOCK_ID", m.get("id"));
                List<SytDesktopBlockTab> blockTabs = blockTabMapper.selectList(blockTabWrapper);
                JSONArray categoryIds = new JSONArray();
                if (CollectionUtils.isNotEmpty(blockTabs)) {
                    blockTabs.forEach(tab -> {
                        categoryIds.add(tab.getCategoryId());
                    });
                }
                m.put("categoryIds", categoryIds);

                m.remove("ROW_ID");
                m.remove("orgId");
                m.remove("roleId");
            });
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public void saveBatchByUser(String desktopId,CurrentUser currentUser, JSONArray blocks) {
        if (blocks.size() > 0) {
            blockUserMapper.removeBatchByUser(currentUser.getHumanCode(), currentUser.getRoleId(), Arrays.asList(desktopId));
            for (int i = 0; i < blocks.size(); i++) {
                JSONObject object = blocks.getJSONObject(i);
                SytDesktopBlock block = this.getById(object.getString("id"));
                if (block != null) {
                    SytDesktopBlockUser blockUser = new SytDesktopBlockUser();
                    blockUser.setBlockId(block.getId());
                    blockUser.setDesktopId(desktopId);
                    blockUser.setAccountId(currentUser.getHumanCode());
                    blockUser.setRoleId(currentUser.getRoleId());
                    blockUser.setCreateTime(new Date());
                    blockUser.setFrontConfig(object.getString("frontConfig"));
                    blockUser.setHeight(object.getInteger("height"));
                    blockUserMapper.insert(blockUser);
                }
            }
        }

    }

    @Override
    public List<DesktopBlockResult> getBlockList(String desktopId, CurrentUser currentUser) {
        List<Map<String, Object>> allBlocktabs = blockTabMapper.getBlocktabs(null);
        Map<String, List<Map<String, Object>>> blocktabMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(allBlocktabs)) {
            for (Map<String, Object> m : allBlocktabs) {
                List<Map<String, Object>> list = null;
                Object blockId = m.get("blockId");
                if (blocktabMap.containsKey(blockId)) {
                    list = blocktabMap.get(blockId);
                } else {
                    list = new ArrayList<>();
                }
                list.add(m);
                blocktabMap.put(blockId.toString(), list);
            }
        }


        QueryWrapper<SytDesktopBlock> blockWrapper = new QueryWrapper<>();
//        blockWrapper.eq("DESKTOP_ID", desktopId);
        List<SytDesktopBlockUser> blockUsers = new ArrayList<>();
        if (currentUser != null && !"syt_visitor".equals(currentUser.getHumanId())) {
            /*QueryWrapper<SytDesktopBlockUser> blockUserWrapper = new QueryWrapper<>();
            blockUserWrapper.eq("DESKTOP_ID", desktopId).eq("ACCOUNT_ID", currentUser.getHumanCode()).eq("ROLE_ID", currentUser.getRoleId());
            long count = blockUserService.count(blockUserWrapper);
            if (count > 0) {    //用户板块关联表里有，按关联表里的id取板块
                SytDesktopBlockUser blockUser = new SytDesktopBlockUser();
                blockUser.setDesktopId(desktopId);
                blockUser.setAccountId(currentUser.getHumanCode());
                blockUser.setRoleId(currentUser.getRoleId());
                blockUsers = blockUserService.queryList(blockUser);
                List<String> blockIds = new ArrayList<>();
                blockUsers.forEach(bu -> blockIds.add(bu.getBlockId()));
                blockWrapper.in("id", blockIds);
            }*/
            blockWrapper.and(wrapper -> wrapper.isNull("ROLE_ID").or().like("ROLE_ID", currentUser.getRoleId()));
            blockWrapper.and(wrapper -> {
                wrapper.isNull("ORG_ID").or(subWrapper -> {
                    currentUser.getOrganizations().forEach(organization -> {
                        subWrapper.or().like("ORG_ID", organization.getId());
                    });
                });
            });

        } else {
            blockWrapper.eq("VISITOR_VISIBILITY", Constants.HAS_YES);
        }
        // 查询启用的数据
        blockWrapper.eq("status", Constants.STATUS_ENABLE);
        List<SytDesktopBlock> blocks = list(blockWrapper);
        List<DesktopBlockResult> blockVos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(blocks)) {
            for (SytDesktopBlock b : blocks) {
                DesktopBlockResult blockVo = new DesktopBlockResult();
                BeanUtils.copyProperties(b, blockVo);

                List<Map<String, Object>> blocktabs = blocktabMap.get(b.getId());

                if (CollectionUtils.isNotEmpty(blocktabs)) {
                    int index = 0;
                    for (Map<String, Object> blocktab : blocktabs) {
                        blocktab.put("data", new ArrayList<>());
                        blocktab.put("index", ++index);
                    }
                }
                blockVo.setTabs(blocktabs == null ? new ArrayList<>() : blocktabs);
                blockUsers.forEach(bu->{
                    if (Objects.equals(blockVo.getId(), bu.getBlockId())) {
                        blockVo.setFrontConfig(bu.getFrontConfig());
                    }
                });
                blockVos.add(blockVo);
            }
        }
        return blockVos;
    }

    @Override
    public void pushDesktop(JSONObject param) {
        String id = param.getString("id");
        String modelUser = param.getString("modelUser");
        String modelRole = param.getString("modelRole");
        //获取布局
        List<SytDesktopBlockUser> blocks = blockUserMapper.selectList(new QueryWrapper<SytDesktopBlockUser>()
                .eq("DESKTOP_ID", id)
                .eq("ACCOUNT_ID", modelUser)
                .eq("ROLE_ID", modelRole));
        String type = param.getString("type");
        if (type.equals("角色")){
            List<SytPermissionAccount> users = userService.getUsersByRoleIds(Arrays.asList(param.getString("roleList").split(",").clone()));
            String role = param.getString("roleList");
            forInsert(id, blocks, role, users);
        } else if (type.equals("部门")||type.equals("人员")){
            JSONObject jsonObject = new JSONObject();
            BaseQuery baseQuery = new BaseQuery();
            baseQuery.setPageSize(Integer.MAX_VALUE);
            String role = param.getString("role");
            param.getString("");
            if (type.equals("部门")) {
                jsonObject.put("roleId",param.getString("role"));
                jsonObject.put("organizationnames",param.getString("orgList"));
            } else {
                jsonObject.put("roleId",role);
                String userList = param.getString("userList");
                jsonObject.put("humancode",userList);
            }
            baseQuery.setQueryParam(jsonObject);
            List<SytPermissionAccount> users = userService.queryPage(baseQuery).getRecords();
            forInsert(id, blocks, role, users);
        }
    }

    private void forInsert(String id, List<SytDesktopBlockUser> blocks, String role, List<SytPermissionAccount> users) {
        for (SytPermissionAccount user : users) {
            blockUserMapper.removeBatchByUser(user.getHumancode(), role, Arrays.asList(id));
            for (SytDesktopBlockUser block:blocks){
                if (block != null) {
                    SytDesktopBlockUser blockUser = new SytDesktopBlockUser();
                    blockUser.setId(null);
                    blockUser.setAccountId(user.getHumancode());
                    blockUser.setDesktopId(id);
                    blockUser.setBlockId(block.getBlockId());
                    blockUser.setFrontConfig(block.getFrontConfig());
                    blockUser.setHeight(block.getHeight());
                    blockUser.setRoleId(role);
                    blockUser.setCreateTime(new Date());
                    blockUserMapper.insert(blockUser);
                }
            }
        }
    }

    private Wrapper<SytDesktopBlock> buildWrapper(SytDesktopBlock sytDesktopBlock) {
        QueryWrapper<SytDesktopBlock> wrapper = new QueryWrapper<>();
        // Query condition...
        /*if (StringUtils.isNotBlank(sytDesktopBlock.getDesktopId())) {
            wrapper.eq("DESKTOP_ID", sytDesktopBlock.getDesktopId());
        }*/
        if (StringUtils.isNotBlank(sytDesktopBlock.getName())) {
            wrapper.like("name", sytDesktopBlock.getName());
        }
        if (StringUtils.isNotBlank(sytDesktopBlock.getVisitorVisibility())) {
            wrapper.eq("VISITOR_VISIBILITY", sytDesktopBlock.getVisitorVisibility());
        }
        return wrapper;
    }
}
