package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytMessage;

/**
 * ${table.comment} 服务接口
 * Created by ZWB on 2022-07-14.
 */
public interface ISytMessageService extends IService<SytMessage> {
    Page<SytMessage> queryPage(BaseQuery query);

    void saveMessageSend(SytMessage sytMessage);
}
