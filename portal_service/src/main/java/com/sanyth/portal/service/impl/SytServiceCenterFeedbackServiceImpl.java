package com.sanyth.portal.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.portal.model.SytServiceCenterFeedback;
import com.sanyth.portal.mapper.SytServiceCenterFeedbackMapper;
import com.sanyth.portal.service.ISytServiceCenterFeedbackService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.core.base.BaseQuery;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-19.
 */
@Service
public class SytServiceCenterFeedbackServiceImpl extends ServiceImpl<SytServiceCenterFeedbackMapper, SytServiceCenterFeedback> implements ISytServiceCenterFeedbackService {

    @Transactional
    @Override
    public void edit(SytServiceCenterFeedback sytServiceCenterFeedback) {
        if(StringUtils.isEmpty(sytServiceCenterFeedback.getId()))
            sytServiceCenterFeedback.setCreatetime(new Date());
        saveOrUpdate(sytServiceCenterFeedback);
    }

    @Override
    public Page<SytServiceCenterFeedback> queryPage(BaseQuery query, SytServiceCenterFeedback sytServiceCenterFeedback) {
        Page<SytServiceCenterFeedback> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<SytServiceCenterFeedback> wrapper = buildWrapper(sytServiceCenterFeedback);
        return page(page, wrapper);
    }

    private Wrapper<SytServiceCenterFeedback> buildWrapper(SytServiceCenterFeedback sytServiceCenterFeedback){
        QueryWrapper<SytServiceCenterFeedback> wrapper = new QueryWrapper<>();
        // Query condition...

        return wrapper;
    }
}
