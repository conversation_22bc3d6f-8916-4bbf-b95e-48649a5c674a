package com.sanyth.portal.service.impl;
import com.alibaba.fastjson.JSON;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytServiceCenterCollectMapper;
import com.sanyth.portal.model.SytServiceCenterCollect;
import com.sanyth.portal.service.ISytServiceCenterCollectService;
import org.springframework.stereotype.Service;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-26.
 */
@Service
public class SytServiceCenterCollectServiceImpl extends ServiceImpl<SytServiceCenterCollectMapper, SytServiceCenterCollect> implements ISytServiceCenterCollectService {

    @Override
    public Page<SytServiceCenterCollect> queryPage(BaseQuery query) {
        Page<SytServiceCenterCollect> page = new Page<>(query.getPage(), query.getPageSize());
        SytServiceCenterCollect obj = JSON.toJavaObject(query.getQueryParam(), SytServiceCenterCollect.class);
        Wrapper<SytServiceCenterCollect> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    private Wrapper<SytServiceCenterCollect> buildWrapper(SytServiceCenterCollect sytServiceCenterCollect){
        QueryWrapper<SytServiceCenterCollect> wrapper = new QueryWrapper<>();
        // Query condition...

        return wrapper;
    }
}
