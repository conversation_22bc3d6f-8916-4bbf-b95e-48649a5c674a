package com.sanyth.portal.service.impl;
import com.alibaba.fastjson.JSON;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytServiceCenterCategoryMapper;
import com.sanyth.portal.model.SytServiceCenterCategory;
import com.sanyth.portal.service.ISytServiceCenterCategoryService;
import org.springframework.stereotype.Service;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-26.
 */
@Service
public class SytServiceCenterCategoryServiceImpl extends ServiceImpl<SytServiceCenterCategoryMapper, SytServiceCenterCategory> implements ISytServiceCenterCategoryService {

    @Override
    public Page<SytServiceCenterCategory> queryPage(BaseQuery query) {
        Page<SytServiceCenterCategory> page = new Page<>(query.getPage(), query.getPageSize());
        SytServiceCenterCategory obj = JSON.toJavaObject(query.getQueryParam(), SytServiceCenterCategory.class);
        Wrapper<SytServiceCenterCategory> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    private Wrapper<SytServiceCenterCategory> buildWrapper(SytServiceCenterCategory sytServiceCenterCategory){
        QueryWrapper<SytServiceCenterCategory> wrapper = new QueryWrapper<>();
        // Query condition...

        return wrapper;
    }
}
