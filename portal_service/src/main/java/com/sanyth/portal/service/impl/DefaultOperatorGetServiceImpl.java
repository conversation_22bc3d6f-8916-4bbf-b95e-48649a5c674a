package com.sanyth.portal.service.impl;

import com.mzt.logapi.beans.Operator;
import com.mzt.logapi.service.IOperatorGetService;
import com.sanyth.portal.util.system.RequestUtils;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class DefaultOperatorGetServiceImpl implements IOperatorGetService {

    @Override
    public Operator getUser() {
         return Optional.ofNullable(RequestUtils.getLoginUserInfo())
                        .map(a -> new Operator(a.get("humanCode").toString()))
                        .orElseThrow(()->new IllegalArgumentException("user is null"));
       
    }
}