package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytLuckyDrawParticipatorMapper;
import com.sanyth.portal.model.SytLuckyDrawParticipator;
import com.sanyth.portal.service.ISytLuckyDrawParticipatorService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 参加抽奖的人 服务实现
 * Created by ZWB on 2023-12-18.
 */
@Service
public class SytLuckyDrawParticipatorServiceImpl extends ServiceImpl<SytLuckyDrawParticipatorMapper, SytLuckyDrawParticipator> implements ISytLuckyDrawParticipatorService {

    @Override
    public Page<SytLuckyDrawParticipator> queryPage(BaseQuery query) {
        Page<SytLuckyDrawParticipator> page = new Page<>(query.getPage(), query.getPageSize());
        SytLuckyDrawParticipator obj = JSON.toJavaObject(query.getQueryParam(), SytLuckyDrawParticipator.class);
        Wrapper<SytLuckyDrawParticipator> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    private Wrapper<SytLuckyDrawParticipator> buildWrapper(SytLuckyDrawParticipator sytLuckyDrawParticipator){
        QueryWrapper<SytLuckyDrawParticipator> wrapper = new QueryWrapper<>();
        // Query condition...
        if(StringUtils.isNotBlank(sytLuckyDrawParticipator.getLuckyId()))
            wrapper.eq("LUCKY_ID", sytLuckyDrawParticipator.getLuckyId());
        if(StringUtils.isNotBlank(sytLuckyDrawParticipator.getHumancode()))
            wrapper.eq("HUMANCODE", sytLuckyDrawParticipator.getHumancode());
        if(StringUtils.isNotBlank(sytLuckyDrawParticipator.getHumanname()))
            wrapper.like("HUMANNAME", sytLuckyDrawParticipator.getHumanname());
        if(StringUtils.isNotBlank(sytLuckyDrawParticipator.getWinOrNot()))
            wrapper.eq("WIN_OR_NOT", sytLuckyDrawParticipator.getWinOrNot());
        if(sytLuckyDrawParticipator.getNum()!=null)
            wrapper.eq("NUM", sytLuckyDrawParticipator.getNum());
        if(StringUtils.isNotBlank(sytLuckyDrawParticipator.getOrganizationnames()))
            wrapper.like("ORGANIZATIONNAMES", sytLuckyDrawParticipator.getOrganizationnames());
        return wrapper;
    }
}
