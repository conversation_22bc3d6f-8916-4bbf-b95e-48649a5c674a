package com.sanyth.portal.service;

import com.sanyth.portal.model.SytZnjqrQuestion;
import org.springframework.data.domain.Page;

import java.util.List;

public interface SytZnjqrQuestionService {

    Long count();

    SytZnjqrQuestion save(SytZnjqrQuestion question);

    void delete(String... id);

    List<SytZnjqrQuestion> queryList(SytZnjqrQuestion question,String fieldSort,String order);

    Page<SytZnjqrQuestion> queryByPage(Integer pageNum, Integer pageSize, SytZnjqrQuestion question,String fieldSort,String order);

    SytZnjqrQuestion getByid(String id);


}
