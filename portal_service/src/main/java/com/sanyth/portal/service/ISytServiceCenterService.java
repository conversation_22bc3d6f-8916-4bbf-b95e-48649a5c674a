package com.sanyth.portal.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytServiceCenter;

import java.util.List;
import java.util.Map;

/**
 * ${table.comment} 服务接口
 * Created by JIANGPING on 2020-05-19.
 */
public interface ISytServiceCenterService extends IService<SytServiceCenter> {
    void edit(JSONObject param) throws Exception;

    Integer getCount(Map<String, Object> param);

    List<SytServiceCenter> queryList(Map<String, Object> param);

    List<SytServiceCenter> getCollectList(Map<String, Object> param);

    List<SytServiceCenter> getRecommendList(Map<String, Object> param);

    Page<SytServiceCenter> queryPage(BaseQuery query);

    void hitCount(String accountId,String serviceCenterId);

    List<SytServiceCenter> getCommonList(Map<String, Object> paramMap);

}
