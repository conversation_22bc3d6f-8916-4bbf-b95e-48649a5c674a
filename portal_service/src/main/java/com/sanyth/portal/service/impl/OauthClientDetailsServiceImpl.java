package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.OauthClientDetailsMapper;
import com.sanyth.portal.model.OauthClientDetails;
import com.sanyth.portal.service.IOauthClientDetailsService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-15.
 */
@Service
public class OauthClientDetailsServiceImpl extends ServiceImpl<OauthClientDetailsMapper, OauthClientDetails> implements IOauthClientDetailsService {

    @Override
    public Page<OauthClientDetails> queryPage(BaseQuery query ) {
        Page<OauthClientDetails> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<OauthClientDetails> wrapper = new QueryWrapper<>();
        OauthClientDetails clientDetails=JSON.toJavaObject(query.getQueryParam(),OauthClientDetails.class);
        wrapper = buildWrapper(clientDetails);
        return page(page, wrapper);
    }

    @Override
    public OauthClientDetails getByidOrAppidAndName(String idOrAppid,String name) {
        QueryWrapper<OauthClientDetails> wrapper = new QueryWrapper<>();
        wrapper.eq("id", idOrAppid).or().eq("thauth_appid", idOrAppid);
        if (StringUtils.isNotBlank(name)) {
            wrapper.eq("client_name", name);
        }
        return getOne(wrapper);
    }

    private Wrapper<OauthClientDetails> buildWrapper(OauthClientDetails clientDetails){
        QueryWrapper<OauthClientDetails> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(clientDetails.getClientName()))
            wrapper.like("client_name", clientDetails.getClientName());
        if(StringUtils.isNotBlank(clientDetails.getClientId()))
            wrapper.like("client_id", clientDetails.getClientId());
        wrapper.orderByAsc("sort");
        return wrapper;
    }

}
