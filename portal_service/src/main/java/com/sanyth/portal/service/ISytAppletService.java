package com.sanyth.portal.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytApplet;

/**
 * ${table.comment} 服务接口
 * Created by JIANGPING on 2020-05-18.
 */
public interface ISytAppletService extends IService<SytApplet> {
    Page<SytApplet> queryPage(BaseQuery query);

    public QueryWrapper<SytApplet> buildWrapper(SytApplet sytApplet);
}
