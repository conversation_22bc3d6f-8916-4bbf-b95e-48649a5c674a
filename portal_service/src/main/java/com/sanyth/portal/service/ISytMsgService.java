package com.sanyth.portal.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.model.SytNews;

import java.util.List;

/**
 * ${table.comment} 服务接口
 * Created by JIANGPING on 2020-05-27.
 */
public interface ISytMsgService extends IService<SytNews> {
        List<SytNews> list(J<PERSON>NObject param, CurrentUser currentUser);
        void edit(SytNews params);
        Page<SytNews> queryPage(BaseQuery query);
}
