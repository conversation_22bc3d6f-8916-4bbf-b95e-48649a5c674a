package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.ErrorInfo;
import com.sanyth.portal.core.common.FileOperationUtil;
import com.sanyth.portal.core.exception.BusinessException;
import com.sanyth.portal.mapper.SytServiceCenterCategoryMapper;
import com.sanyth.portal.mapper.SytServiceCenterMapper;
import com.sanyth.portal.mapper.SytServiceCenterRoleMapper;
import com.sanyth.portal.model.SytServiceCenter;
import com.sanyth.portal.model.SytServiceCenterRole;
import com.sanyth.portal.model.SytServiceCenterVisit;
import com.sanyth.portal.model.SytSysOrganization;
import com.sanyth.portal.service.ISytServiceCenterService;
import com.sanyth.portal.service.ISytServiceCenterVisitService;
import com.sanyth.portal.service.ISytSysOrganizationService;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-19.
 */
@Service
public class SytServiceCenterServiceImpl extends ServiceImpl<SytServiceCenterMapper, SytServiceCenter> implements ISytServiceCenterService {

    @Autowired
    FileOperationUtil fileOperationUtil;
    @Autowired
    SytServiceCenterRoleMapper serviceCenterRoleMapper;
    @Autowired
    SytServiceCenterCategoryMapper categoryMapper;
    @Autowired
    SytServiceCenterMapper serviceCenterMapper;
    @Autowired
    ISytServiceCenterVisitService visitService;
    @Autowired
    private ISytSysOrganizationService iSytSysOrganizationService;

    @Transactional
    @Override
    public void edit(JSONObject param) throws Exception {
        JSONObject serviceCenter = param.getJSONObject("serviceCenter");
        JSONArray roleId = param.getJSONArray("roleId");
        if (serviceCenter == null) {
            throw new BusinessException(ErrorInfo.MSG_00005);
        }
        SytServiceCenter sytServiceCenter = JSON.toJavaObject(serviceCenter, SytServiceCenter.class);
        String title = sytServiceCenter.getName();
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < title.length(); i++) {
            char word = title.charAt(i);
            String[] strings = PinyinHelper.toHanyuPinyinStringArray(word);
            if (null != strings && strings.length > 0)
                builder.append(strings[0].charAt(0));
        }
        sytServiceCenter.setPinyin(builder.toString());
        if (StringUtils.isNotEmpty(sytServiceCenter.getId())) {
            SytServiceCenter sytServiceCenterIndb = getById(sytServiceCenter.getId());
            sytServiceCenter.setCreatetime(sytServiceCenterIndb.getCreatetime());
            sytServiceCenter.setCollectNumber(sytServiceCenterIndb.getCollectNumber());
            sytServiceCenter.setVisits(sytServiceCenterIndb.getVisits());
            if (!sytServiceCenterIndb.getIcon().equals(sytServiceCenter.getIcon())) {
                fileOperationUtil.remove(sytServiceCenterIndb.getIcon());
            }
            QueryWrapper<SytServiceCenterRole> wrapper = new QueryWrapper<>();
            wrapper.eq("service_id", sytServiceCenter.getId());
            serviceCenterRoleMapper.delete(wrapper);
//            updateAllColumnById(sytServiceCenter);
            saveOrUpdate(sytServiceCenter);
        } else {
            sytServiceCenter.setCreatetime(new Date());
            sytServiceCenter.setCollectNumber(0l);
            sytServiceCenter.setVisits(0l);
            save(sytServiceCenter);
        }

        if (roleId.size() > 0) {
            for (Object o : roleId) {
                SytServiceCenterRole serviceCenterRole = new SytServiceCenterRole();
                serviceCenterRole.setRoleId((String) o);
                serviceCenterRole.setServiceId(sytServiceCenter.getId());
                serviceCenterRoleMapper.insert(serviceCenterRole);
            }
        }
    }

    @Override
    public Integer getCount(Map<String, Object> param) {
        return serviceCenterMapper.getCount(param);
    }

    @Override
    public List<SytServiceCenter> queryList(Map<String, Object> param) {
        List<SytServiceCenter> sytServiceCenters = serviceCenterMapper.queryList(param);
        if(sytServiceCenters != null && sytServiceCenters.size() > 0){
            // 查询所有的组织机构
            Map<String, SytSysOrganization> orgMap = getOrgMap();
            for (SytServiceCenter serviceCenter : sytServiceCenters) {
                // 设置组织机构名称
                if(StringUtils.isNotBlank(serviceCenter.getOrgid()) && orgMap.size() > 0){
                    serviceCenter.setOrgname(Objects.isNull(orgMap.get(serviceCenter.getOrgid())) ? "" : orgMap.get(serviceCenter.getOrgid()).getOrgname());
                }
            }
        }
        return sytServiceCenters;
    }

    @Override
    public List<SytServiceCenter> getCollectList(Map<String, Object> param) {
        return serviceCenterMapper.getCollectList(param);
    }

    @Override
    public List<SytServiceCenter> getRecommendList(Map<String, Object> param) {
        return serviceCenterMapper.getRecommendList(param);
    }

    @Override
    public Page<SytServiceCenter> queryPage(BaseQuery query) {
        Page<SytServiceCenter> page = new Page<>(query.getPage(), query.getPageSize());
        JSONObject queryParam = query.getQueryParam();
        Wrapper<SytServiceCenter> wrapper = buildWrapper(JSON.toJavaObject(queryParam, SytServiceCenter.class));
        page = page(page, wrapper);
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            for (SytServiceCenter serviceCenter : page.getRecords()) {
                serviceCenter.setCategory(categoryMapper.selectById(serviceCenter.getCategoryId()));

                QueryWrapper<SytServiceCenterRole> wra = new QueryWrapper();
                wra.eq("service_id", serviceCenter.getId());
                List<SytServiceCenterRole> objects = serviceCenterRoleMapper.selectList(wra);
                JSONArray roleId = new JSONArray();
                if (CollectionUtils.isNotEmpty(objects)) {
                    objects.forEach(role -> {
                        roleId.add(role.getRoleId());
                    });
                }
                serviceCenter.setRoleId(roleId);
            }
        }
        return page;
    }

    @Override
    @Transactional
    public void hitCount(String accountId,String serviceCenterId) {
        QueryWrapper<SytServiceCenterVisit> visitQueryWrapper = new QueryWrapper<>();
        visitQueryWrapper.eq("ACCOUNT_ID", accountId).eq("SERVICE_CENTER_ID", serviceCenterId);
        SytServiceCenterVisit visit = visitService.getOne(visitQueryWrapper);
        Date date = new Date();
        if (visit != null) {
            visit.setCount(visit.getCount() + 1);
        } else {
            visit = new SytServiceCenterVisit();
            visit.setServiceCenterId(serviceCenterId);
            visit.setAccountId(accountId);
            visit.setCount(1);
            visit.setCreateDate(date);
        }
        visit.setModifyDate(date);
        visitService.saveOrUpdate(visit);
        SytServiceCenter serviceCenter = this.getById(serviceCenterId);
        serviceCenter.setVisits(serviceCenter.getVisits() + 1);
        this.saveOrUpdate(serviceCenter);
    }

    @Override
    public List<SytServiceCenter> getCommonList(Map<String, Object> paramMap) {
        return serviceCenterMapper.getCommonList(paramMap);
    }

    private Wrapper<SytServiceCenter> buildWrapper(SytServiceCenter sytServiceCenter) {
        QueryWrapper<SytServiceCenter> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotEmpty(sytServiceCenter.getName()))
            wrapper.like("name", sytServiceCenter.getName());
        if (StringUtils.isNotEmpty(sytServiceCenter.getCategoryId()))
            wrapper.eq("category_id", sytServiceCenter.getCategoryId());
        if (StringUtils.isNotEmpty(sytServiceCenter.getAppId()))
            wrapper.eq("app_id", sytServiceCenter.getAppId());
        if (CollectionUtils.isNotEmpty(sytServiceCenter.getRoleId())) {
            wrapper.apply(" id in(select SERVICE_ID from SYT_SERVICE_CENTER_ROLE " +
                    " where role_id={0})", sytServiceCenter.getRoleId().getString(0));

        }
        if (StringUtils.isNotEmpty(sytServiceCenter.getOrgid()))
            wrapper.eq("orgid", sytServiceCenter.getOrgid());
        if (StringUtils.isNotEmpty(sytServiceCenter.getLinkType()))
            wrapper.eq("linkType", sytServiceCenter.getLinkType());
        wrapper.orderByAsc("sort");
        return wrapper;
    }

    /**
     * 查询所有的组织机构
     */
    private Map<String, SytSysOrganization> getOrgMap() {
        Map<String, SytSysOrganization> orgMap = new HashMap<>();
        List<SytSysOrganization> organizationList = iSytSysOrganizationService.queryList(new SytSysOrganization());
        if(organizationList != null && organizationList.size() > 0){
            organizationList.forEach(organization ->{
                orgMap.put(organization.getId(), organization);
            });
        }
        return orgMap;
    }
}
