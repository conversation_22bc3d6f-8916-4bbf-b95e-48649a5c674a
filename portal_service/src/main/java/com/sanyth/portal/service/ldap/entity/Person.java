package com.sanyth.portal.service.ldap.entity;

import org.springframework.ldap.odm.annotations.Attribute;
import org.springframework.ldap.odm.annotations.DnAttribute;
import org.springframework.ldap.odm.annotations.Entry;
import org.springframework.ldap.odm.annotations.Id;

import javax.naming.Name;
import java.io.Serializable;

@Entry(base = "ou=person", objectClasses = {"inetOrgPerson","extensibleObject"})
public class Person implements Serializable {

    @Id
    private Name dn;
    /**
     * 账号
     */
	@Attribute(name = "uid")
	@DnAttribute(value = "uid", index = 0)
	private String uid;
    /**
     * 姓名
     */
    @Attribute(name = "cn")
    // @DnAttribute(value = "cn" ,index = 1)
    private String cn;
    /**
     * 姓名
     */
    @Attribute(name = "sn")
    private String sn;

    /**
     * 邮箱
     */
	@Attribute(name = "mail")
	private String mail;
    /**
     * 手机号
     */
	@Attribute(name = "telephoneNumber")
	private String telephoneNumber;
    /**
     * 用户密码
     */
    @Attribute(name = "userPassword")
    private String userPassword;
    /**
     * 用户类别
     */
    @Attribute(name = "employeeType")
    private String employeeType;
    /**
     * 用户状态
     */
    @Attribute(name = "olcAllows")
    private boolean olcAllows;

    /**
     * 时间限制
     */
    @Attribute(name = "olcTimeLimit")
    private String olcTimeLimit;

    /**
     * 组织机构
     */
    @Attribute(name = "ou")
    private String ou;

    /**
     * 组织机构简称
     */
    @Attribute(name = "o")
    private String organizationName;

    /**
     * 身份证号
     */
    @Attribute(name = "uniqueIdentifier")
    private String idcode;

    public Person() {
    	super();
    }

    public Person(String uid) {
        this.uid = uid;
    }

    public Person(String uid, String cn, String sn, String mail, String telephoneNumber, String userPassword, String employeeType
            , String olcTimeLimit, String ou, String organizationName, String idcode) {
        this.uid = uid;
        this.cn = cn;
        this.sn = sn;
        this.mail = mail;
        this.telephoneNumber = telephoneNumber;
        this.userPassword = userPassword;
        this.employeeType = employeeType;
        this.olcTimeLimit = olcTimeLimit;
        this.ou = ou;
        this.organizationName = organizationName;
        this.idcode = idcode;
    }

    /* getter */
    public Name getDn() {
        return dn;
    }

    public String getCn() {
        return cn;
    }

    public String getSn() {
        return sn;
    }

    public String getUserPassword() {
        return userPassword;
    }

    /* setter */
    public void setDn(Name dn) {
        this.dn = dn;
    }

    public void setCn(String cn) {
        this.cn = cn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public void setUserPassword(String userPassword) {
        this.userPassword = userPassword;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

	public String getMail() {
		return mail;
	}

	public void setMail(String mail) {
		this.mail = mail;
	}

	public String getTelephoneNumber() {
		return telephoneNumber;
	}

	public void setTelephoneNumber(String telephoneNumber) {
		this.telephoneNumber = telephoneNumber;
	}

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public boolean isOlcAllows() {
        return olcAllows;
    }

    public void setOlcAllows(boolean olcAllows) {
        this.olcAllows = olcAllows;
    }

    public String getOlcTimeLimit() {
        return olcTimeLimit;
    }

    public void setOlcTimeLimit(String olcTimeLimit) {
        this.olcTimeLimit = olcTimeLimit;
    }

    public String getOu() {
        return ou;
    }

    public void setOu(String ou) {
        this.ou = ou;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getIdcode() {
        return idcode;
    }

    public void setIdcode(String idcode) {
        this.idcode = idcode;
    }
}
