package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytFileResource;

import java.util.List;

/**
 * 常用文件管理 服务接口
 * Created by WDL on 2022-11-16.
 */
public interface ISytFileResourceService extends IService<SytFileResource> {

    Page<SytFileResource> queryPage(BaseQuery query);

    void editFile(SytFileResource sytFileResource) throws Exception;

    void removeFile(List<String> asList);
}
