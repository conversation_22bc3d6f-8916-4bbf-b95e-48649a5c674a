package com.sanyth.portal.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.portal.model.SytDesktopBlockTab;
import com.sanyth.portal.mapper.SytDesktopBlockTabMapper;
import com.sanyth.portal.service.ISytDesktopBlockTabService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.core.base.BaseQuery;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-28.
 */
@Service
public class SytDesktopBlockTabServiceImpl extends ServiceImpl<SytDesktopBlockTabMapper, SytDesktopBlockTab> implements ISytDesktopBlockTabService {

    @Override
    public Page<SytDesktopBlockTab> queryPage(BaseQuery query) {
        Page<SytDesktopBlockTab> page = new Page<>(query.getPage(), query.getPageSize());
        SytDesktopBlockTab obj = JSON.toJavaObject(query.getQueryParam(), SytDesktopBlockTab.class);
        Wrapper<SytDesktopBlockTab> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    private Wrapper<SytDesktopBlockTab> buildWrapper(SytDesktopBlockTab sytDesktopBlockTab){
        Wrapper<SytDesktopBlockTab> wrapper = new QueryWrapper<>();
        // Query condition...

        return wrapper;
    }
}
