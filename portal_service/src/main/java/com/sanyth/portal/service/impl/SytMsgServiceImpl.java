package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.mapper.SytMsgCategoryMapper;
import com.sanyth.portal.mapper.SytNewsMapper;
import com.sanyth.portal.model.SytNews;
import com.sanyth.portal.service.ISytMsgService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-27.
 */
@Service
public class SytMsgServiceImpl extends ServiceImpl<SytNewsMapper, SytNews> implements ISytMsgService {

    @Autowired
    SytMsgCategoryMapper newsCategoryMapper;
    @Autowired
    SytNewsMapper newsMapper;

    @Override
    public List<SytNews> list(JSONObject param, CurrentUser currentUser) {
        SytNews sytNews = JSON.toJavaObject(param, SytNews.class);
        QueryWrapper<SytNews> newsWrapper = buildWrapper(sytNews);
        if (currentUser != null) {
            newsWrapper.and(queryWrapper -> queryWrapper
                    .or(wrapper -> wrapper.and(s -> s.isNull("ROLEIDS").or().like("ROLEIDS", "[]"))
                            .and(s -> s.isNull("ORGIDS").or().like("ORGIDS", "[]"))
                            .and(s -> s.isNull("USERIDS").or().like("USERIDS", "[]"))
                    )
                    .or().like("ROLEIDS", currentUser.getRoleId())
                    .or().like("USERIDS", currentUser.getHumanCode())
                    .or(wrapper -> {
                        currentUser.getOrganizations().forEach(organization -> {
                            wrapper.or().like("ORGIDS", organization.getId());
                        });
                    }));

        }
        return list(newsWrapper);
    }

    @Override
    public void edit(SytNews params) {
        SytNews news = new SytNews();
        BeanUtils.copyProperties(params, news);
        if (StringUtils.isEmpty(params.getId())) {
            news.setCreateTime(new Date());
            save(news);
        } else {
            SytNews sytNewsINdb = getById(params.getId());
            news.setCreateTime(sytNewsINdb.getCreateTime());
//            updateAllColumnById(news);
            saveOrUpdate(news);
        }
    }

    @Override
    public Page<SytNews> queryPage(BaseQuery query) {
        Page<SytNews> page = new Page<>(query.getPage(), query.getPageSize());
        SytNews obj = JSON.toJavaObject(query.getQueryParam(), SytNews.class);
        QueryWrapper<SytNews> newsWrapper = buildWrapper(obj);
        CurrentUser currentUser = query.getCurrentUser();
        if (currentUser != null) {
            newsWrapper.and(queryWrapper -> queryWrapper
                    .or(wrapper -> wrapper.and(s -> s.isNull("ROLEIDS").or().like("ROLEIDS", "[]"))
                            .and(s -> s.isNull("ORGIDS").or().like("ORGIDS", "[]"))
                    )
                    .or(wrapper -> {
                        wrapper.and(subWrapper -> subWrapper.isNull("ROLEIDS").or().like("ROLEIDS", "[]").or().like("ROLEIDS", currentUser.getRoleId()))
                                .and(subWrapper -> {
                                    currentUser.getOrganizations().forEach(organization -> {
                                        subWrapper.or().like("ORGIDS", organization.getId());
                                    });
                                });
                    })
                    .or(wrapper -> {
                        wrapper.like("ROLEIDS", currentUser.getRoleId())
                                .and(subWrapper -> {
                                    currentUser.getOrganizations().forEach(organization -> {
                                        subWrapper.or().like("ORGIDS", organization.getId());
                                    });
                                    subWrapper.or().isNull("ORGIDS").or().like("ORGIDS", "[]");
                                });
                    }));

        }
        return page(page, newsWrapper);
    }

    private QueryWrapper<SytNews> buildWrapper(SytNews sytNews) {
        QueryWrapper<SytNews> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotEmpty(sytNews.getTitle())) {
            wrapper.like("title", sytNews.getTitle());
        }
        if (StringUtils.isNotEmpty(sytNews.getCategoryId())) {
            wrapper.eq("CATEGORY_ID", sytNews.getCategoryId());
        }
        if (StringUtils.isNotEmpty(sytNews.getIsAdminPush())) {
            wrapper.eq("ISADMIN_PUSH", sytNews.getIsAdminPush());
        }
        wrapper.orderByDesc("CREATE_TIME");
        return wrapper;
    }
}
