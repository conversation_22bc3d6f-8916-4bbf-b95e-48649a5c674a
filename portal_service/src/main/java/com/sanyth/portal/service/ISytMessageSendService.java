package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytMessageSend;

import java.util.List;

/**
 * ${table.comment} 服务接口
 * Created by ZWB on 2022-07-14.
 */
public interface ISytMessageSendService extends IService<SytMessageSend> {
    Page<SytMessageSend> queryPage(BaseQuery query);

    void deleteByMessageId(List<String> messagesIds);

    void send(SytMessageSend sytMessageSend);

}
