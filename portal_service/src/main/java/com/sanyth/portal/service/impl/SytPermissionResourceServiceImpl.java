package com.sanyth.portal.service.impl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytPermissionResourceMapper;
import com.sanyth.portal.mapper.SytPermissionResourceRoleMapper;
import com.sanyth.portal.model.SytPermissionResource;
import com.sanyth.portal.model.SytPermissionResourceRole;
import com.sanyth.portal.service.ISytPermissionResourceService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-14.
 */
@Service
public class SytPermissionResourceServiceImpl extends ServiceImpl<SytPermissionResourceMapper, SytPermissionResource> implements ISytPermissionResourceService {

    @Autowired
    SytPermissionResourceMapper resourceMapper;
    @Autowired
    SytPermissionResourceRoleMapper resourceRoleMapper;

    @Transactional
    @Override
    public void delete(String... id) {
        QueryWrapper<SytPermissionResourceRole> resourceRoleWrapper = new QueryWrapper<>();
        resourceRoleWrapper.in("RESOURCE_ID", id);
        resourceRoleMapper.delete(resourceRoleWrapper);
        for (String s : id) {
            removeById(s);
        }
    }

    @Override
    public List<SytPermissionResource> getListByRole(String roleId) {
        return resourceMapper.getListByRole(roleId);
    }

    @Override
    public List<SytPermissionResource> queryList(SytPermissionResource resource) {
        return list(buildWrapper(resource));
    }

    @Override
    public Page<SytPermissionResource> getAllChildren(String id, BaseQuery query) {
        Page<SytPermissionResource> page = new Page<>(query.getPage(), query.getPageSize());
        Map<String, Object> params = new HashMap();
        params.put("id", id);
        page.setRecords(resourceMapper.getAllChildren(params, page));
        return page;
    }

    private Wrapper<SytPermissionResource> buildWrapper(SytPermissionResource resource) {
        QueryWrapper<SytPermissionResource> wrapper = new QueryWrapper();
        if (resource.getType() != null)
            wrapper.eq("type", resource.getType());
        if (resource.getType() != null)
            wrapper.eq("is_btn", resource.getIsBtn());
        if (StringUtils.isNotBlank(resource.getParentId()))
            wrapper.eq("parent_id", resource.getParentId());
        if (StringUtils.isNotBlank(resource.getStatus()))
            wrapper.eq("status", resource.getStatus());
        if (StringUtils.isNotBlank(resource.getVisitorVisibility()))
            wrapper.eq("VISITOR_VISIBILITY", resource.getVisitorVisibility());
        wrapper.orderByAsc("sort");
        return wrapper;
    }


}
