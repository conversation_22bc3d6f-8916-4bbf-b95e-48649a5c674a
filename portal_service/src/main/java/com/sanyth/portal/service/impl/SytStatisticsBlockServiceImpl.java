package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.Constants;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.mapper.SytStatisticsBlockMapper;
import com.sanyth.portal.model.SytStatisticsBlock;
import com.sanyth.portal.service.ISytStatisticsBlockService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 流程统计板块管理 服务实现
 * Created by WDL on 2023-03-06.
 */
@Service
public class SytStatisticsBlockServiceImpl extends ServiceImpl<SytStatisticsBlockMapper, SytStatisticsBlock> implements ISytStatisticsBlockService {

    @Override
    public Page<SytStatisticsBlock> queryPage(BaseQuery query) {
        Page<SytStatisticsBlock> page = new Page<>(query.getPage(), query.getPageSize());
        SytStatisticsBlock obj = JSON.toJavaObject(query.getQueryParam(), SytStatisticsBlock.class);
        QueryWrapper<SytStatisticsBlock> wrapper = buildWrapper(obj);
        buildQuery(query.getCurrentUser(), wrapper);
        return page(page, wrapper);
    }

    @Override
    public List<SytStatisticsBlock> queryList(BaseQuery query) {
        SytStatisticsBlock sytStatisticsBlock = JSON.toJavaObject(query.getQueryParam(), SytStatisticsBlock.class);
        QueryWrapper<SytStatisticsBlock> statisticsTaWrapper = buildWrapper(sytStatisticsBlock);
        CurrentUser currentUser = query.getCurrentUser();
        buildQuery(currentUser, statisticsTaWrapper);
        List<SytStatisticsBlock> list = list(statisticsTaWrapper);

        if (StringUtils.isNotBlank(sytStatisticsBlock.getParentId()) && CollectionUtils.isNotEmpty(list)){
            List<String> ids = list.stream().map(SytStatisticsBlock::getId).collect(Collectors.toList());
            QueryWrapper<SytStatisticsBlock> wrapper = buildWrapper(new SytStatisticsBlock().setParentId(ids.stream().collect(Collectors.joining(","))));
            buildQuery(currentUser, wrapper);
            List<SytStatisticsBlock> l = list(wrapper);
            list.addAll(l);
        }

        return list;
    }

    private void buildQuery(CurrentUser currentUser, QueryWrapper<SytStatisticsBlock> statisticsTaWrapper) {
        if (currentUser != null) {
            statisticsTaWrapper.and(queryWrapper -> queryWrapper
                    .or(wrapper -> wrapper.and(s -> s.isNull("ROLEIDS").or().like("ROLEIDS", "[]"))
                            .and(s -> s.isNull("ORGIDS").or().like("ORGIDS", "[]"))
                    )
                    .or(wrapper -> {
                        wrapper.and(subWrapper -> subWrapper.isNull("ROLEIDS").or().like("ROLEIDS", "[]").or().like("ROLEIDS", currentUser.getRoleId()))
                                .and(subWrapper -> {
                                    currentUser.getOrganizations().forEach(organization -> {
                                        subWrapper.or().like("ORGIDS", organization.getId());
                                    });
                                });
                    })
                    .or(wrapper -> {
                        wrapper.like("ROLEIDS", currentUser.getRoleId())
                                .and(subWrapper -> {
                                    currentUser.getOrganizations().forEach(organization -> {
                                        subWrapper.or().like("ORGIDS", organization.getId());
                                    });
                                    subWrapper.or().isNull("ORGIDS").or().like("ORGIDS", "[]");
                                });
                    })
            );
        }
    }

    @Override
    public List<SytStatisticsBlock> queryList(SytStatisticsBlock query) {
        QueryWrapper<SytStatisticsBlock> wrapper = buildWrapper(query);
        return list(wrapper);
    }

    @Override
    public List<SytStatisticsBlock> queryParentData(CurrentUser currentUser, String parentId) {
        SytStatisticsBlock sytStatisticsBlock = new SytStatisticsBlock();
        sytStatisticsBlock.setStatus("1");  // 查询启用的数据
        if(StringUtils.isBlank(parentId)){
            // parentId为空时，查询根目录数据
            sytStatisticsBlock.setParentId("ISNULL");
        } else {
            sytStatisticsBlock.setParentId(parentId);
            sytStatisticsBlock.setHomeFlag(Constants.HAS_NO);
        }
        QueryWrapper<SytStatisticsBlock> statisticsTaWrapper = buildWrapper(sytStatisticsBlock);
        buildQuery(currentUser, statisticsTaWrapper);
        return list(statisticsTaWrapper);
    }

    private QueryWrapper<SytStatisticsBlock> buildWrapper(SytStatisticsBlock sytStatisticsBlock){
        QueryWrapper<SytStatisticsBlock> wrapper = new QueryWrapper<>();
        // Query condition...
        if(StringUtils.isNotBlank(sytStatisticsBlock.getId()))
            wrapper.in("ID", sytStatisticsBlock.getId().split(","));
        if(StringUtils.isNotBlank(sytStatisticsBlock.getName()))
            wrapper.like("NAME", sytStatisticsBlock.getName());
        if(StringUtils.isNotBlank(sytStatisticsBlock.getStatus()))
            wrapper.eq("STATUS", sytStatisticsBlock.getStatus());
        if(StringUtils.isNotBlank(sytStatisticsBlock.getParentId())) {
            if(Objects.equals("ISNULL", sytStatisticsBlock.getParentId())){
                // parentId值为"ISNULL"时，查询根目录数据
                wrapper.isNull("PARENT_ID");
            } else if(Objects.equals("NOTNULL", sytStatisticsBlock.getParentId())){
                // parentId值为"NOTNULL"时，查询数据
                wrapper.isNotNull("PARENT_ID");
            } else {
                wrapper.in("PARENT_ID", sytStatisticsBlock.getParentId().split(","));
            }
        }
        if(StringUtils.isNotBlank(sytStatisticsBlock.getTerminalType()))
            wrapper.eq("TERMINAL_TYPE", sytStatisticsBlock.getTerminalType());
        if(StringUtils.isNotBlank(sytStatisticsBlock.getTabId()))
            wrapper.in("TAB_ID", sytStatisticsBlock.getTabId().split(","));
        if(StringUtils.isNotBlank(sytStatisticsBlock.getNameShow()))
            wrapper.eq("NAME_SHOW", sytStatisticsBlock.getNameShow());
        if(StringUtils.isNotBlank(sytStatisticsBlock.getHomeFlag()))
            wrapper.eq("HOME_FLAG", sytStatisticsBlock.getHomeFlag());

        wrapper.orderByAsc("sort");
        return wrapper;
    }
}
