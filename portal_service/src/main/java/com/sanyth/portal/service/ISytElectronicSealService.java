package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytElectronicSeal;

import java.util.List;

/**
 * 电子印章 服务接口
 * Created by ZWB on 2022-12-20.
 */
public interface ISytElectronicSealService extends IService<SytElectronicSeal> {
    Page<SytElectronicSeal> queryPage(BaseQuery query);

    List<SytElectronicSeal> queryList(SytElectronicSeal seal);
}
