package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.Constants;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.mapper.SytHallStatisticsMapper;
import com.sanyth.portal.model.SytHallStatistics;
import com.sanyth.portal.service.ISytHallStatisticsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 服务大厅首页统计数据 服务实现
 * Created by WDL on 2022-09-02.
 */
@Service
public class SytHallStatisticsServiceImpl extends ServiceImpl<SytHallStatisticsMapper, SytHallStatistics> implements ISytHallStatisticsService {

    @Override
    public Page<SytHallStatistics> queryPage(BaseQuery query) {
        Page<SytHallStatistics> page = new Page<>(query.getPage(), query.getPageSize());
        SytHallStatistics obj = JSON.toJavaObject(query.getQueryParam(), SytHallStatistics.class);
        Wrapper<SytHallStatistics> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public List<SytHallStatistics> queryList(SytHallStatistics resource) {
        return list(buildWrapper(resource));
    }

    private QueryWrapper<SytHallStatistics> buildWrapper(SytHallStatistics sytHallStatistics){
        QueryWrapper<SytHallStatistics> wrapper = new QueryWrapper<>();
        // Query condition...
        if(StringUtils.isNotBlank(sytHallStatistics.getName()))
            wrapper.like("name", sytHallStatistics.getName());
        if(StringUtils.isNotBlank(sytHallStatistics.getParentId()))
            wrapper.eq("parent_Id", sytHallStatistics.getParentId());
        if(StringUtils.isNotBlank(sytHallStatistics.getStatus()))
            wrapper.eq("status", sytHallStatistics.getStatus());
        if(StringUtils.isNotBlank(sytHallStatistics.getDetailShow()))
            wrapper.eq("detail_Show", sytHallStatistics.getDetailShow());

        wrapper.orderByAsc("sort");
        return wrapper;
    }

    @Override
    public List<SytHallStatistics> getListByUser(CurrentUser currentUser, SytHallStatistics resource) {
        QueryWrapper<SytHallStatistics> hallStatisticsWrapper = buildWrapper(resource);
        if (currentUser != null && !"syt_visitor".equals(currentUser.getHumanId())) {
            hallStatisticsWrapper.and(queryWrapper -> queryWrapper
                    .or(wrapper -> wrapper.and(s -> s.isNull("ROLEIDS").or().like("ROLEIDS", "[]"))
                            .and(s -> s.isNull("ORGIDS").or().like("ORGIDS", "[]"))
                    )
                    .or(wrapper -> {
                        wrapper.and(subWrapper -> subWrapper.isNull("ROLEIDS").or().like("ROLEIDS", "[]").or().like("ROLEIDS", currentUser.getRoleId()))
                                .and(subWrapper -> {
                                    currentUser.getOrganizations().forEach(organization -> {
                                        subWrapper.or().like("ORGIDS", organization.getId());
                                    });
                                });
                    })
                    .or(wrapper -> {
                        wrapper.like("ROLEIDS", currentUser.getRoleId())
                                .and(subWrapper -> {
                                    currentUser.getOrganizations().forEach(organization -> {
                                        subWrapper.or().like("ORGIDS", organization.getId());
                                    });
                                    subWrapper.or().isNull("ORGIDS").or().like("ORGIDS", "[]");
                                });
                    })
            );

        } else {
            hallStatisticsWrapper.and(wrapper -> wrapper.isNull("ROLEIDS").isNull("ORGIDS"));
        }
        hallStatisticsWrapper.eq("status", Constants.STATUS_ENABLE);
        return list(hallStatisticsWrapper);
    }
}
