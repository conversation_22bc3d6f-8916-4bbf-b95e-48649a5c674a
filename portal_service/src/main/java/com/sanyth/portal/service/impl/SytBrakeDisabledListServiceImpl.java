package com.sanyth.portal.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.mapper.SytBrakeDisabledListMapper;
import com.sanyth.portal.model.SytBrakeDisabledList;
import com.sanyth.portal.model.SytPermissionAccount;
import com.sanyth.portal.service.ISytBrakeDisabledListService;
import com.sanyth.portal.service.ISytPermissionAccountService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.core.base.BaseQuery;
import com.alibaba.fastjson.JSON;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 闸机禁用名单 服务实现
 * Created by MHR on 2023-03-20.
 */
@Service
public class SytBrakeDisabledListServiceImpl extends ServiceImpl<SytBrakeDisabledListMapper, SytBrakeDisabledList> implements ISytBrakeDisabledListService {

    @Autowired
    ISytPermissionAccountService service;

    @Override
    public Page<SytBrakeDisabledList> queryPage(BaseQuery query) {
        Page<SytBrakeDisabledList> page = new Page<>(query.getPage(), query.getPageSize());
        SytBrakeDisabledList obj = JSON.toJavaObject(query.getQueryParam(), SytBrakeDisabledList.class);
        Wrapper<SytBrakeDisabledList> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    @Transactional(rollbackFor=Exception.class)
    public void edit(SytBrakeDisabledList params) {
        if (StringUtils.isNotBlank(params.getId())) {
            removeById(params.getId());
        }
        for (String brakeId : params.getBrakeId().split(",")){
            Set<SytBrakeDisabledList> set = new HashSet<>();
            Date date = new Date();
            if (StringUtils.isNotBlank(params.getUserCode())){
                String[] split = params.getUserCode().split(",");
                for (String item : split) {
                    SytPermissionAccount user = service.getByHumancode(item);
                    set.add(new SytBrakeDisabledList(user.getHumanname(),user.getHumancode(), user.getIdcode(),
                            params.getStartTime(),params.getEndTime(),date,brakeId));
                }
            }
            List<String> list = Arrays.asList(params.getOrgidList().split(","));
            if (CollectionUtils.isNotEmpty(list)){
                List<SytPermissionAccount> users = service.getUsersByDeptIds(list);
                for (SytPermissionAccount user : users){
                    set.add(new SytBrakeDisabledList(user.getHumanname(),user.getHumancode(), user.getIdcode(),
                            params.getStartTime(),params.getEndTime(),date,brakeId));
                }
            }
            saveBatch(set);
        }
    }

    @Override
    public boolean permission(SytBrakeDisabledList param) {
        SytBrakeDisabledList entity = new SytBrakeDisabledList();
        entity.setUsername(param.getUsername());
        entity.setUserCode(param.getUserCode());
        entity.setIdCode(param.getIdCode());
        entity.setBrakeName(param.getBrakeName());
        Wrapper<SytBrakeDisabledList> wrapper = buildWrapper(entity);
        List<SytBrakeDisabledList> list = list(wrapper);
        Date date = new Date();
        for (SytBrakeDisabledList item:list) {
            if (date.after(item.getStartTime())&&date.before(item.getEndTime()))return false;
        }
        return true;
    }

    private Wrapper<SytBrakeDisabledList> buildWrapper(SytBrakeDisabledList params){
        QueryWrapper<SytBrakeDisabledList> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(params.getId())) {
            wrapper.eq("ID",params.getId());
        }
        if (StringUtils.isNotBlank(params.getBrakeName())) {
            wrapper.like("BRAKENAME",params.getBrakeName());
        }
        if (StringUtils.isNotBlank(params.getBrakeId())) {
            wrapper.like("BRAKEID",params.getBrakeId());
        }
        if (StringUtils.isNotBlank(params.getUsername())) {
            wrapper.like("USERNAME",params.getUsername());
        }
        if (StringUtils.isNotBlank(params.getUserCode())) {
            wrapper.eq("USERCODE",params.getUserCode());
        }
        wrapper.orderByDesc("CREATEDATE");
        return wrapper;
    }
}
