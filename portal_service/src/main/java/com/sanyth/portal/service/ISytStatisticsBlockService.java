package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.model.SytStatisticsBlock;

import java.util.List;

/**
 * 流程统计板块管理 服务接口
 * Created by WDL on 2023-03-06.
 */
public interface ISytStatisticsBlockService extends IService<SytStatisticsBlock> {
    Page<SytStatisticsBlock> queryPage(BaseQuery query);

    List<SytStatisticsBlock> queryList(BaseQuery query);

    List<SytStatisticsBlock> queryList(SytStatisticsBlock query);

    List<SytStatisticsBlock> queryParentData(CurrentUser currentUser, String parentId);
}
