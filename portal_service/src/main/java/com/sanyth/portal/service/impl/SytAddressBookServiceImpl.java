package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytAddressBookMapper;
import com.sanyth.portal.model.SytAddressBook;
import com.sanyth.portal.service.ISytAddressBookService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 通讯录 服务实现
 * Created by ZWB on 2022-09-29.
 */
@Service
public class SytAddressBookServiceImpl extends ServiceImpl<SytAddressBookMapper, SytAddressBook> implements ISytAddressBookService {

    @Override
    public Page<SytAddressBook> queryPage(BaseQuery query) {
        Page<SytAddressBook> page = new Page<>(query.getPage(), query.getPageSize());
        SytAddressBook obj = JSON.toJavaObject(query.getQueryParam(), SytAddressBook.class);
        Wrapper<SytAddressBook> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    private Wrapper<SytAddressBook> buildWrapper(SytAddressBook sytAddressBook){
        QueryWrapper<SytAddressBook> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(sytAddressBook.getHumanCode())) {
            wrapper.like("HUMANCODE", sytAddressBook.getHumanCode());
        }
        if (StringUtils.isNotBlank(sytAddressBook.getHumanName())) {
//            wrapper.like("HUMANNAME", sytAddressBook.getHumanName());
            wrapper.apply(" replace(HUMANNAME,' ','') like '%" + sytAddressBook.getHumanName() + "%'");
        }
        if (StringUtils.isNotBlank(sytAddressBook.getDepartment())) {
            wrapper.like("DEPARTMENT", sytAddressBook.getDepartment());
        }
        if (StringUtils.isNotBlank(sytAddressBook.getTelePhone())) {
            wrapper.like("TELEPHONE", sytAddressBook.getTelePhone());
        }
        if (StringUtils.isNotBlank(sytAddressBook.getMobilePhone())) {
            wrapper.like("MOBILEPHONE", sytAddressBook.getMobilePhone());
        }
        wrapper.orderByAsc("sort");
        return wrapper;
    }
}
