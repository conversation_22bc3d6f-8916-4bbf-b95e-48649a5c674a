package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytElectronicSealMapper;
import com.sanyth.portal.model.SytElectronicSeal;
import com.sanyth.portal.service.ISytElectronicSealService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 电子印章 服务实现
 * Created by ZWB on 2022-12-20.
 */
@Service
public class SytElectronicSealServiceImpl extends ServiceImpl<SytElectronicSealMapper, SytElectronicSeal> implements ISytElectronicSealService {

    @Override
    public Page<SytElectronicSeal> queryPage(BaseQuery query) {
        Page<SytElectronicSeal> page = new Page<>(query.getPage(), query.getPageSize());
        SytElectronicSeal obj = JSON.toJavaObject(query.getQueryParam(), SytElectronicSeal.class);
        Wrapper<SytElectronicSeal> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public List<SytElectronicSeal> queryList(SytElectronicSeal seal) {
        Wrapper<SytElectronicSeal> wrapper = buildWrapper(seal);
        return list(wrapper);
    }

    private Wrapper<SytElectronicSeal> buildWrapper(SytElectronicSeal sytElectronicSeal){
        QueryWrapper<SytElectronicSeal> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(sytElectronicSeal.getName())) {
            wrapper.like("name", sytElectronicSeal.getName());
        }
        if (StringUtils.isNotBlank(sytElectronicSeal.getHumanname())) {
            wrapper.like("HUMANNAME", sytElectronicSeal.getHumanname());
        }
        if (StringUtils.isNotBlank(sytElectronicSeal.getSfmr())) {
            wrapper.eq("sfmr", sytElectronicSeal.getSfmr());
        }
        if (StringUtils.isNotBlank(sytElectronicSeal.getHumancode())) {
            wrapper.eq("HUMANCODE", sytElectronicSeal.getHumancode());
        }
        wrapper.orderByDesc("CREATETIME");
        return wrapper;
    }
}
