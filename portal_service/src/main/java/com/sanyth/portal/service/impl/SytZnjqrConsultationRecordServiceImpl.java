package com.sanyth.portal.service.impl;

import com.sanyth.portal.model.SytZnjqrConsultationRecord;
import com.sanyth.portal.service.SytZnjqrConsultationRecordService;
import com.sanyth.portal.service.SytZnjqrConsultationRepository;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class SytZnjqrConsultationRecordServiceImpl implements SytZnjqrConsultationRecordService {

    @Autowired
    private SytZnjqrConsultationRepository sytZnjqrConsultationRepository;
    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Override
    public Long count() {
        return sytZnjqrConsultationRepository.count();
    }

    @Override
    public SytZnjqrConsultationRecord save(SytZnjqrConsultationRecord record) {
        return sytZnjqrConsultationRepository.save(record);
    }

    @Override
    public void delete(String... id) {
        sytZnjqrConsultationRepository.deleteAllById(Arrays.asList(id));
    }

    @Override
    public List<SytZnjqrConsultationRecord> queryList(SytZnjqrConsultationRecord record) {
        NativeSearchQuery searchQuery = buildSearchQuery(record);
        List<SytZnjqrConsultationRecord> list = new ArrayList<>();
        SearchHits<SytZnjqrConsultationRecord> searchHits = elasticsearchRestTemplate.search(searchQuery, SytZnjqrConsultationRecord.class);
        for (SearchHit<SytZnjqrConsultationRecord> searchHit : searchHits) {
            list.add(searchHit.getContent());
        }
        return list;
    }

    @Override
    public Page<SytZnjqrConsultationRecord> queryByPage(Integer pageNum, Integer pageSize, SytZnjqrConsultationRecord record) {
        PageRequest pageRequest = PageRequest.of(pageNum, pageSize);
        NativeSearchQuery searchQuery = buildSearchQuery(record);
        searchQuery.setPageable(pageRequest);
        List<SytZnjqrConsultationRecord> list = new ArrayList<>();
        SearchHits<SytZnjqrConsultationRecord> searchHits = elasticsearchRestTemplate.search(searchQuery, SytZnjqrConsultationRecord.class);
        for (SearchHit<SytZnjqrConsultationRecord> searchHit : searchHits) {
            list.add(searchHit.getContent());
        }
        Page<SytZnjqrConsultationRecord> page = new PageImpl<>(list, pageRequest, searchHits.getTotalHits());
        return page;
    }

    private NativeSearchQuery buildSearchQuery(SytZnjqrConsultationRecord record) {
        BoolQueryBuilder defaultQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(record.getPid())) {
            defaultQueryBuilder.should(QueryBuilders.matchPhraseQuery("pid", record.getPid()));
        }
        if (StringUtils.isNotBlank(record.getZxr())) {
            defaultQueryBuilder.should(QueryBuilders.matchPhraseQuery("zxr", record.getZxr()));
        }
        if (StringUtils.isNotBlank(record.getZxrxm())) {
            defaultQueryBuilder.should(QueryBuilders.matchPhraseQuery("zxrxm", record.getZxrxm()));
        }
        FieldSortBuilder sortBuilder = SortBuilders.fieldSort("createtime").order(SortOrder.DESC);
        //组装条件
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(defaultQueryBuilder)
                .withSort(sortBuilder).build();
        return searchQuery;
    }
}
