package com.sanyth.portal.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SysResource;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * ${table.comment} 服务接口
 * Created by JIANGPING on 2020-05-20.
 */
public interface ISysResourceService extends IService<SysResource> {
    void delete(String... id);

    Page<SysResource> queryPage(BaseQuery query, SysResource sysResource);

    List<SysResource> queryList(SysResource resource);
}
