package com.sanyth.portal.service.impl;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.*;
import com.sanyth.portal.core.exception.BusinessException;
import com.sanyth.portal.core.model.Organization;
import com.sanyth.portal.core.model.Role;
import com.sanyth.portal.mapper.*;
import com.sanyth.portal.model.*;
import com.sanyth.portal.pojo.AccountExcel;
import com.sanyth.portal.pojo.ImportErrorInfo;
import com.sanyth.portal.service.*;
import com.sanyth.portal.service.ldap.entity.Person;
import com.sanyth.portal.service.ldap.service.PersonService;
import com.sanyth.portal.util.validation.ValidationUtils;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by JIANGPING on 2020/5/15.
 */
@Service
public class SytPermissionAccountServiceImpl extends ServiceImpl<SytPermissionAccountMapper, SytPermissionAccount> implements ISytPermissionAccountService {

    @Autowired
    SytPermissionAccountMapper accountMapper;
    @Autowired
    SytPermissionAccountRoleMapper accountRoleMapper;
    @Autowired
    SytSysOrganizationMapper organizationMapper;
    @Autowired
    SytSysOrganizationUserMapper organizationUserMapper;
    @Autowired
    SytPermissionRoleMapper roleMapper;
    @Autowired
    ISytPermissionRoleService sytPermissionRoleService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISytSysOrganizationService organizationService;
    @Autowired
    ISytPermissionAccountRoleService accountRoleService;
    @Autowired
    ISytPermissionRoleService roleService;
    @Resource
    SytSysParamService paramService;
    @Autowired
    SytSysSafetyService sytSysSafetyService;
    @Resource
    PersonService personService;

    @Override
    public SytPermissionAccount getByHumancode(String humancode) {
        SytPermissionAccount account = new SytPermissionAccount();
        account.setHumancode(humancode);
        Wrapper<SytPermissionAccount> accountWrapper = buildWrapper(account);
        return getOne(accountWrapper);
    }

    @Override
    public List<SytPermissionAccount> queryList(SytPermissionAccount account) {
        QueryWrapper<SytPermissionAccount> accountWrapper = buildWrapper(account);
        accountWrapper.ne("id", "syt_visitor");
        return list(accountWrapper);
    }

    @Override
    public Page<SytPermissionAccount> queryPage(BaseQuery query) {
        Page<SytPermissionAccount> page = new Page<>(query.getPage(), query.getPageSize());
        Map map = JSON.toJavaObject(query.getQueryParam(), Map.class);
        List<SytPermissionAccount> list = accountMapper.queryList(map, page);
        if (CollectionUtils.isNotEmpty(list)) {
            for (SytPermissionAccount account : list) {
                JSONArray roles = new JSONArray();
                List<SytPermissionRole> roleList = roleMapper.getByAccount(account.getId());
                if (CollectionUtils.isNotEmpty(roleList)) {
                    for (SytPermissionRole sytPermissionRole : roleList) {
                        JSONObject r = new JSONObject();
                        r.put("label", sytPermissionRole.getRolename());
                        r.put("value", sytPermissionRole.getId());
                        roles.add(r);
                    }
                }
                account.setRole(roles);

                // 组织关系
                QueryWrapper<SytSysOrganizationUser> param = new QueryWrapper<>();
                param.eq("USER_ID", account.getId());
                List<SytSysOrganizationUser> organizationUsers = organizationUserMapper.selectList(param);
                JSONArray org = new JSONArray();
                if (CollectionUtils.isNotEmpty(organizationUsers)) {
                    for (SytSysOrganizationUser organizationUser : organizationUsers) {
                        org.add(organizationUser.getOrganizationId());
                    }
                }
                account.setOrganization(org);
            }
        }
        page.setRecords(list);
        return page;
    }

    @Transactional
    @Override
    public Map<String,Object> edit(JSONObject params) throws Exception {
        Map<String, Object> map = new HashMap<>();
        JSONObject accountJson = params.getJSONObject("account");
        JSONArray roleJson = params.getJSONArray("role");
        JSONArray organizationJson = params.getJSONArray("organization");
        if (accountJson == null || roleJson == null || organizationJson == null) {
            throw new BusinessException(ErrorInfo.MSG_00005);
        }

        SytPermissionAccount account = JSON.toJavaObject(accountJson, SytPermissionAccount.class);
        ValidationUtils.validate(account);
        StringBuilder organizationBuider = new StringBuilder();
        StringBuilder orgshortnameBuider = new StringBuilder();
        /*QueryWrapper<SytSysOrganization> organizationWrapper = new QueryWrapper<>();
        organizationWrapper.isNull("parent");
        List<SytSysOrganization> organizations = organizationMapper.selectList(organizationWrapper);
        if (CollectionUtils.isNotEmpty(organizations)) {
            organizationBuider.append(organizations.get(0).getOrgname()).append(";");
        }*/

        List<SytSysOrganizationUser> organizationUsers = new ArrayList<>();
        for (int i = 0; i < organizationJson.size(); i++) {
            Object obj = organizationJson.get(i);
            String o = (String) obj;
            SytSysOrganization organization = organizationMapper.selectById(o);
            if (i == 0 && organization.getParent() != null) {
                SytSysOrganization parent = organizationMapper.selectById(organization.getParent());
                organizationBuider.append(parent.getOrgname()).append(";");
                orgshortnameBuider.append(parent.getOrgshortname()).append(";");
            }
            organizationBuider.append(organization.getOrgname()).append(",");
            orgshortnameBuider.append(organization.getOrgshortname()).append(",");
            SytSysOrganizationUser ou = new SytSysOrganizationUser();
            ou.setOrganizationId(o);
            organizationUsers.add(ou);
        }
        account.setOrganizationnames(organizationBuider.deleteCharAt(organizationBuider.length() - 1).toString());
        account.setOrgshortname(orgshortnameBuider.deleteCharAt(orgshortnameBuider.length() - 1).toString());

        SytPermissionAccount accountParam = new SytPermissionAccount();
        accountParam.setHumancode(account.getHumancode());
        List<SytPermissionAccount> list = list(buildWrapper(accountParam));// 当前登录账号是否存在

        if (StringUtils.isBlank(account.getId())) {
            if (CollectionUtils.isNotEmpty(list))
                throw new BusinessException(ErrorInfo.MSG_00006);
            /*String pass = account.getHumancode();
            if (StringUtils.isNotEmpty(account.getIdcode())) {
                if (account.getIdcode().length() >= 6) {
                    pass = account.getIdcode().substring(account.getIdcode().length()-6, account.getIdcode().length());
                }
            }*/
            String pass = passwordHandler(account);
            account.setHumanpassword(DigestUtils.md5DigestAsHex(pass.getBytes()));
            account.setCreatedate(new Date());
            save(account);
            map.put("account", account);
        } else {
            if (CollectionUtils.isNotEmpty(list) && !list.get(0).getHumancode().equals(account.getHumancode()))
                throw new BusinessException(ErrorInfo.MSG_00006);

            SytPermissionAccount accountIndb = getById(account.getId());
            accountIndb.setHumancode(accountJson.getString("humancode"));
            accountIndb.setHumanname(accountJson.getString("humanname"));
            accountIndb.setTelmobile1(accountJson.getString("telmobile1"));
            accountIndb.setDutyid(accountJson.getString("dutyid"));
            accountIndb.setIdtype(accountJson.getString("idtype"));
            accountIndb.setIdcode(accountJson.getString("idcode"));
            accountIndb.setTeloffice(accountJson.getString("teloffice"));
            accountIndb.setTelhome(accountJson.getString("telhome"));
            accountIndb.setTelmobile2(accountJson.getString("telmobile2"));
            if (StringUtils.isNotEmpty(accountJson.getString("birthday")))
                accountIndb.setBirthday(new SimpleDateFormat("yyyy-MM-dd").parse(accountJson.getString("birthday")));
            accountIndb.setEmail(accountJson.getString("email"));
            accountIndb.setPostalcode(accountJson.getString("postalcode"));
            accountIndb.setValidflag(accountJson.getInteger("validflag"));
            accountIndb.setDisplayorder(accountJson.getDouble("displayorder"));
            accountIndb.setSex(accountJson.getString("sex"));
            accountIndb.setAddress(accountJson.getString("address"));
            accountIndb.setValidfromdate(accountJson.getDate("validfromdate"));
            accountIndb.setValidtodate(accountJson.getDate("validtodate"));
            accountIndb.setEmployeeType(accountJson.getString("employeeType"));

//            accountIndb.setOrganizationnames(organizationBuider.deleteCharAt(organizationBuider.length() - 1).toString());
            accountIndb.setOrganizationnames(organizationBuider.toString().trim());
            accountIndb.setOrgshortname(orgshortnameBuider.toString().trim());
            updateById(accountIndb);
            accountRoleMapper.delete(new QueryWrapper<SytPermissionAccountRole>().eq("ACCOUNT_ID", accountIndb.getId()));
            organizationUserMapper.delete(new QueryWrapper<SytSysOrganizationUser>().eq("USER_ID", accountIndb.getId()));
            map.put("account", accountIndb);

        }
        List<SytPermissionAccountRole> accountRoleList = new ArrayList<>();
        for (int i = 0; i < roleJson.size(); i++) {
            String roleid = (String) roleJson.get(i);
            SytPermissionAccountRole accountRole = new SytPermissionAccountRole();
            accountRole.setAccountId(account.getId());
            accountRole.setRoleId(roleid);
            accountRoleMapper.insert(accountRole);
            accountRoleList.add(accountRole);
        }
        map.put("accountRoleList", accountRoleList);
        for (SytSysOrganizationUser ou : organizationUsers) {
            ou.setUserId(account.getId());
            organizationUserMapper.insert(ou);
        }
        //更新reids缓存
        updatePermissionCacheByToken(account);
        return map;
    }

    @Transactional
    @Override
    public void delete(String... id) throws Exception {
        if (id != null && id.length > 0) {
            accountRoleMapper.delete(new QueryWrapper<SytPermissionAccountRole>().in("ACCOUNT_ID", id));
            organizationUserMapper.delete(new QueryWrapper<SytSysOrganizationUser>().in("USER_ID", id));
            removeByIds(Arrays.asList(id));
        } else {
            throw new BusinessException(ErrorInfo.MSG_00005);
        }
    }

    @Override
    public Resp updateInfo(SytPermissionAccount account) {
        if (org.apache.commons.lang3.StringUtils.isBlank(account.getId())) {
            return Resp.error("id不能为空");
        }
        SytPermissionAccount accountIndb = getById(account.getId());
        if(accountIndb == null){
            return Resp.error("用户不存在");
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(account.getHumanname())) {
            accountIndb.setHumanname(account.getHumanname());
        }
        accountIndb.setSignature(account.getSignature());
        accountIndb.setSex(account.getSex());
        accountIndb.setTelmobile1(account.getTelmobile1());
        accountIndb.setEmail(account.getEmail());
        accountIndb.setIdtype(account.getIdtype());
        accountIndb.setIdcode(account.getIdcode());
        accountIndb.setAddress(account.getAddress());
        accountIndb.setLogintime(account.getLogintime());
        accountIndb.setHumanpassword(null);
        ValidationUtils.validate(accountIndb);
        updateById(accountIndb);
        return Resp.success();
    }

    @Transactional
    @Override
    public Resp changePasswd(String oldpassword, String newpassword, String newpasswords, CurrentUser currentUser) {
        if(org.apache.commons.lang3.StringUtils.isNotBlank(oldpassword) && org.apache.commons.lang3.StringUtils.isNotBlank(newpassword) && org.apache.commons.lang3.StringUtils.isNotBlank(newpasswords)){
            if(!newpassword.equals(newpasswords)){
                return Resp.error("两次新密码输入不一致");
            }
            String humancode = currentUser.getHumanCode();
            // 查询密码修改策略
            Map<String, Object> strategyMdifyMap = sytSysSafetyService.getByCode(Constants.PASSWORD_STRATEGY_MODIFY);
            String pattern = "";
            if(strategyMdifyMap != null){
                if(strategyMdifyMap.get("passRegExp") != null && StringUtils.isNotBlank(strategyMdifyMap.get("passRegExp").toString())){
                    pattern = strategyMdifyMap.get("passRegExp").toString();
                }
                // 是否允许包含用户名
                if(strategyMdifyMap.get("sfyhm") != null){
                    String sfyhm = strategyMdifyMap.get("sfyhm").toString();
                    if(Objects.equals(Constants.HAS_NO, sfyhm) && newpasswords.indexOf(humancode) > -1){
                        return Resp.error("密码不允许包含用户名");
                    }
                }
            }
            if (!ToolsUtil.checkPasswordStrength(pattern, newpassword)&&!ToolsUtil.checkPasswordStrength(pattern, newpasswords)) {
                return Resp.error("密码强度不符");
            }
            SytPermissionAccount account = new SytPermissionAccount();
            account.setHumancode(humancode);
            account.setHumanpassword(DigestUtils.md5DigestAsHex(oldpassword.getBytes()));
            List<SytPermissionAccount> list = queryList(account);
            if(list != null && list.size() > 0){
                SytPermissionAccount currentAccount = list.get(0);
                currentAccount.setHumanpassword(DigestUtils.md5DigestAsHex(newpassword.getBytes()));
                updateById(currentAccount);
                return Resp.success();
            }else{
                return Resp.error(currentUser.getHumanCode()+"用户原始密码不正确");
            }
        }
        return Resp.error("请完整填写密码");
    }

    @Override
    public List<SytPermissionAccount> getUsersByDeptIds(Collection<String> deptIds) {
        return accountMapper.getUsersByDeptIds(deptIds);
    }

    @Override
    public List<SytPermissionAccount> getUsersByPostIds(Collection<String> postIds) {
        return accountMapper.getUsersByPostIds(postIds);
    }

    @Override
    public List<SytPermissionAccount> getUsersByRoleIds(Collection<String> roleIds) {
        return accountMapper.getUsersByRoleIds(roleIds);
    }

    @Override
    public String updatePermissionCacheByToken(SytPermissionAccount account) {
//        String token = (String) redisUtil.get(account.getId());
        String token = UUID.randomUUID().toString().replaceAll("-", "");
        /*if (StringUtils.isBlank(token)) {
            token = UUID.randomUUID().toString().replaceAll("-", "");
        }*/
        QueryWrapper<SytPermissionAccountRole> accountRoleQueryWrapper = new QueryWrapper<>();
        accountRoleQueryWrapper.eq("account_id", account.getId());
        List<SytPermissionAccountRole> sytPermissionAccountRoles = accountRoleService.list(accountRoleQueryWrapper);
        CurrentUser user = new CurrentUser()
                .setHumanId(account.getId())
                .setHumanCode(account.getHumancode())
                .setHumanName(account.getHumanname())
                .setSex(account.getSex())
                .setTelmobile1(account.getTelmobile1())
                .setEmail(account.getEmail())
                .setCreateTime(new Date());
        List<SytSysOrganization> organizations = organizationService.getOrganizationByUser(user.getHumanId());
        List<Organization> organizationList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(organizations)) {
            organizations.forEach(org -> {
                Organization organization = new Organization();
                BeanUtils.copyProperties(org, organization);
                organizationList.add(organization);
            });
            user.setOrganizations(organizationList);
            user.setOrganizationnames(account.getOrganizationnames());
        }

        List<String> ids = new ArrayList<>();
        sytPermissionAccountRoles.forEach(accountRole -> {
            ids.add(accountRole.getRoleId());
        });
        List<SytPermissionRole> sytPermissionRoles = roleService.listByIds(ids);
        List<Role> newRole = new ArrayList<>();
        sytPermissionRoles.forEach(role -> {
            Role r = new Role();
            BeanUtils.copyProperties(role, r);
            newRole.add(r);
        });
        user.setRoles(newRole);
        user.setRoleId(newRole.get(0).getId());
        user.setRoleName(newRole.get(0).getRolename());
        user.setRoleKey(newRole.get(0).getRolekey());
        long expireTime = Constants.TOKEN_EXPIRE;
        // 查询密码安全策略：登录超时限制(分钟) overtime
        Map<String, Object> strategySafetyeMap = sytSysSafetyService.getByCode(Constants.PASSWORD_STRATEGY_SAFETY);
        if(strategySafetyeMap != null && strategySafetyeMap.get("overtime") != null) {
            expireTime = Long.parseLong(strategySafetyeMap.get("overtime").toString()) * 60;
        }
        redisUtil.set(token, user, expireTime);
        return token;
    }

    @Override
    public Resp resetPasswordByAdmin(JSONObject param) {
        String humancode = param.getString("humancode");
        SytPermissionAccount account = this.getByHumancode(humancode);
        if (account == null) {
            return Resp.error("未找到用户,操作失败");
        }
        String password = passwordHandler(account);
        account.setHumanpassword(DigestUtils.md5DigestAsHex(password.getBytes()));
        account.setModifypasstime(new Date());
        updateById(account);
        saveLdapPerson(account, password);
        return Resp.success(account);
    }

    @Override
    public Resp updateDisplayorder(Map<String, Object> param) {
        Integer currentPage = (Integer) param.get("currentPage");
        Integer pageSize = (Integer) param.get("pageSize");
        List<String> idList = (List<String>) param.get("idList");
        if (currentPage == null || pageSize == null || CollectionUtils.isEmpty(idList)) {
            return Resp.error("参数错误");
        }
        Integer order = (currentPage - 1) * pageSize;
        for (String id : idList) {
            SytPermissionAccount account = this.getById(id);
            if (account == null) {
                continue;
            }
            account.setDisplayorder(Double.parseDouble(String.valueOf(order)));
            updateById(account);
            order++;
        }
        return Resp.success();
    }

    @Override
    public List<ImportErrorInfo> importUpdateDisplayorder(List<AccountExcel> accountExcels) {
        List<ImportErrorInfo> errorInfo = new ArrayList<>();
        List<SytPermissionAccount> list = new LinkedList<>();
        for (AccountExcel accountExcel : accountExcels) {
            if (StringUtils.isBlank(accountExcel.getHumancode())) {
                errorInfo.add(new ImportErrorInfo(accountExcel.getRowIndex() + 1, accountExcel.getHumancode(), "学工/工号不能为空"));
                continue;
            }
            if (StringUtils.isBlank(accountExcel.getDisplayorder())) {
                errorInfo.add(new ImportErrorInfo(accountExcel.getRowIndex() + 1, accountExcel.getDisplayorder(), "显示顺序不能为空"));
                continue;
            }
            SytPermissionAccount account = this.getByHumancode(accountExcel.getHumancode().trim());
            if (account == null) {
                errorInfo.add(new ImportErrorInfo(accountExcel.getRowIndex() + 1, accountExcel.getHumancode(), "该学工/工号在系统中不存在"));
                continue;
            }
            try {
                account.setDisplayorder(Double.parseDouble(accountExcel.getDisplayorder()));
            } catch (NumberFormatException e) {
                errorInfo.add(new ImportErrorInfo(accountExcel.getRowIndex() + 1, accountExcel.getDisplayorder(), "显示顺序必须为数字"));
                continue;
            }
            list.add(account);
        }
        if (list.size() > 0) {
            this.saveOrUpdateBatch(list);
        }
        return errorInfo;
    }

    /**
     * 编辑ldap里的用户信息
     * @param account
     */
    private void saveLdapPerson(SytPermissionAccount account,String password) {
        Person person = personService.findById(account.getHumancode());
        if (person == null) {
            person = new Person(account.getHumancode(), account.getHumanname(), account.getHumanname(), account.getEmail(), account.getTelmobile1()
                    , LdapEncoderByMd5(password), account.getEmployeeType()
                    , (account.getValidtodate() == null ? "2099-12-31 00:00:00" : DateFormatUtils.format(account.getValidtodate(), "yyyy-MM-dd HH:mm:ss"))
                    , account.getOrganizationnames(), account.getOrgshortname()
                    , account.getIdcode());
            person.setOlcAllows(account.getValidflag() == 0 ? true : false);
            personService.saveBean(person);
        } else {
            person.setUid(account.getHumancode());
            person.setCn(account.getHumanname());
            person.setSn(account.getHumanname());
            person.setMail(account.getEmail());
            person.setTelephoneNumber(account.getTelmobile1());
            person.setOlcTimeLimit((account.getValidtodate() == null ? "2099-12-31 00:00:00" : DateFormatUtils.format(account.getValidtodate(), "yyyy-MM-dd HH:mm:ss")));
            person.setEmployeeType(account.getEmployeeType());
            person.setOu(account.getOrganizationnames());
            person.setOrganizationName(account.getOrgshortname());
            person.setIdcode(account.getIdcode());
            person.setOlcAllows(account.getValidflag() == 0 ? true : false);
            if (StringUtils.isEmpty(password)) {
                personService.updateBean(person);
            } else {
                person.setUserPassword(LdapEncoderByMd5(password));
                personService.changePassword(person);
            }
        }
    }
    /**
     * ldap md5加密
     * @param psw
     * @return
     * @throws NoSuchAlgorithmException
     * @throws UnsupportedEncodingException
     */
    @SneakyThrows
    public static String LdapEncoderByMd5(String psw) {
        MessageDigest md5= MessageDigest.getInstance("MD5");
        String md5psw= Base64.encode(md5.digest(psw.getBytes("utf-8")));
        return "{MD5}"+ md5psw;
    }

    private QueryWrapper<SytPermissionAccount> buildWrapper(SytPermissionAccount account) {
        QueryWrapper<SytPermissionAccount> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(account.getHumancode()))
            wrapper.eq("humancode", account.getHumancode());
        if (StringUtils.isNotBlank(account.getTelmobile1()))
            wrapper.eq("telmobile1", account.getTelmobile1());
        if (StringUtils.isNotBlank(account.getHumanpassword()))
            wrapper.eq("humanpassword", account.getHumanpassword());
        return wrapper;
    }

    /**
     * 用户密码统一生成规则，适用于添加用户、导入用户、重置用户密码，生成按优先级排序
     * 1.参数表默认密码+身份证后六位
     * 2.参数表默认密码+用户账户名
     * 3.身份证后六位
     * 4.用户账户名
     * @param account
     * @return
     */
    public String passwordHandler(SytPermissionAccount account){
        String pass = "";
        SytSysParam sysParam = paramService.get(Constants.DEFAULT_PASSWORD);    // 默认密码前缀
        if (sysParam != null) {
            pass = sysParam.getValue();
        }
        // 查询密码生成策略
        Map<String, Object> strategyGenerateMap = sytSysSafetyService.getByCode(Constants.PASSWORD_STRATEGY_GENERATE);
        if(strategyGenerateMap != null && strategyGenerateMap.get("sccl") != null){
            // 设置的密码
            String passwordTmp = "";
            String sccl = strategyGenerateMap.get("sccl").toString();
            switch (sccl) {
                // 取身份证件号后6位
                case "idcard":
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(account.getIdcode())&&account.getIdcode().length() >= 6) {
                        passwordTmp = account.getIdcode().substring(account.getIdcode().length() - 6);
                    }
                    break;
                // 取账号名
                case "loginnumber":
                    passwordTmp = account.getHumancode();
                    break;
                // 指定密码
                case "manageset":
                    if(strategyGenerateMap.get("setnumber") != null){
                        passwordTmp = strategyGenerateMap.get("setnumber").toString();
                    }
                    break;
                default:
                    break;
            }
            if(StringUtils.isNotBlank(passwordTmp)){
                return pass + passwordTmp;
            }
        }
        if (sysParam != null) {
            pass = sysParam.getValue();
            // 有身份证号，则参数表值加上身份证后六位；没有身份证号的，则加上用户账号
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(account.getIdcode())&&account.getIdcode().length() >= 6) {
                pass += account.getIdcode().substring(account.getIdcode().length() - 6);
            } else {
                pass += account.getHumancode();
            }
        } else if (org.apache.commons.lang3.StringUtils.isNotEmpty(account.getIdcode())&&account.getIdcode().length() >= 6) {
            pass = account.getIdcode().substring(account.getIdcode().length() - 6);
        } else {
            pass = account.getHumancode();
        }
        return pass;
    }
}
