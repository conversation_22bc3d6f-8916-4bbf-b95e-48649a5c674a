package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytMessageMapper;
import com.sanyth.portal.mapper.SytPermissionAccountMapper;
import com.sanyth.portal.model.SytMessage;
import com.sanyth.portal.model.SytMessageSend;
import com.sanyth.portal.model.SytPermissionAccount;
import com.sanyth.portal.service.ISytMessageSendService;
import com.sanyth.portal.service.ISytMessageService;
import com.sanyth.portal.service.ISytPermissionAccountService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * ${table.comment} 服务实现
 * Created by ZWB on 2022-07-14.
 */
@Service
public class SytMessageServiceImpl extends ServiceImpl<SytMessageMapper, SytMessage> implements ISytMessageService {

    @Autowired
    private ISytPermissionAccountService permissionAccountService;
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    @Autowired
    private ISytMessageSendService iSytMessageSendService;
    @Resource
    private SytPermissionAccountMapper sytPermissionAccountMapper;

    @Override
    public Page<SytMessage> queryPage(BaseQuery query) {
        Page<SytMessage> page = new Page<>(query.getPage(), query.getPageSize());
        SytMessage obj = JSON.toJavaObject(query.getQueryParam(), SytMessage.class);
        Wrapper<SytMessage> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Transactional
    @Override
    public void saveMessageSend(SytMessage sytMessage) {
        save(sytMessage);
        List<SytMessageSend> messageSends = new ArrayList<>();
        Set<SytPermissionAccount> accountSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(sytMessage.getRoleIds())) {
            accountSet.addAll(sytPermissionAccountMapper.getUsersByRoleIds(sytMessage.getRoleIds()));
        }
        if (CollectionUtils.isNotEmpty(sytMessage.getOrgIds())) {
            accountSet.addAll(sytPermissionAccountMapper.getUsersByDeptIds(sytMessage.getOrgIds()));
        }
        if (CollectionUtils.isNotEmpty(sytMessage.getUserIds())) {
            QueryWrapper<SytPermissionAccount> wrapper = new QueryWrapper<>();
            wrapper.eq("VALIDFLAG", 0);
            List<String> userIds = sytMessage.getUserIds();
            if(userIds.size() < 1000){
                wrapper.in("HUMANCODE", sytMessage.getUserIds());
            } else {
                wrapper.and(sub->{
                    double v = userIds.size() / 1000.0;
                    for (int i = 0; i < v; i++) {
                        int i1 = i + 1;
                        List<String> subList = userIds.subList(i * 1000, (Math.min((i1 * 1000), userIds.size())));
                        sub.or().in("HUMANCODE", subList);
                    }
                });
            }
            accountSet.addAll(permissionAccountService.list(wrapper));
        }
        accountSet.forEach(u->{
            SytMessageSend ms = new SytMessageSend();
            BeanUtils.copyProperties(sytMessage,ms);
            ms.setId(null);
            ms.setMessageid(sytMessage.getId());
            ms.setUserid(u.getHumancode());
            ms.setUsername(u.getHumanname());
            ms.setSendtime(new Date());
            ms.setStatus(0);
            messageSends.add(ms);
            messagingTemplate.convertAndSendToUser(u.getHumancode(),"/queue/subscribe", "您收到了新的消息");
        });
//        messagingTemplate.convertAndSend("/topic/subscribe", "您收到了新的系统消息");
        /*for (String userId : sytMessage.getUserIds()) {
            SytMessageSend ms = new SytMessageSend();
            ms.setMessageid(sytMessage.getId());
            ms.setUserid(userId);
            SytPermissionAccount permissionAccount = permissionAccountService.getByHumancode(userId);
            ms.setUsername(permissionAccount.getHumanname());
            ms.setTitle(sytMessage.getTitle());
            ms.setContent(sytMessage.getContent());
            ms.setType(sytMessage.getType());
            ms.setSendtime(new Date());
            ms.setStatus(0);
            messageSends.add(ms);
            messagingTemplate.convertAndSendToUser(userId,"/queue/subscribe", "您收到了新的消息");
        }*/
        iSytMessageSendService.saveOrUpdateBatch(messageSends);
    }

    private Wrapper<SytMessage> buildWrapper(SytMessage sytMessage){
        QueryWrapper<SytMessage> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(sytMessage.getTitle())) {
            wrapper.like("TITLE", sytMessage.getTitle());
        }
        if (StringUtils.isNotBlank(sytMessage.getType())) {
            wrapper.in("TYPE", sytMessage.getType().split(","));
        }
        wrapper.orderByDesc("CREATEDATE");
        return wrapper;
    }
}
