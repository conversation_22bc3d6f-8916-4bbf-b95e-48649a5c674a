package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytDesktopAppMapper;
import com.sanyth.portal.model.SytDesktopApp;
import com.sanyth.portal.service.ISytDesktopAppService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 桌面关联APP 服务实现
 * Created by ZWB on 2022-04-02.
 */
@Service
public class SytDesktopAppServiceImpl extends ServiceImpl<SytDesktopAppMapper, SytDesktopApp> implements ISytDesktopAppService {

    @Override
    public Page<SytDesktopApp> queryPage(BaseQuery query) {
        Page<SytDesktopApp> page = new Page<>(query.getPage(), query.getPageSize());
        SytDesktopApp obj = JSON.toJavaObject(query.getQueryParam(), SytDesktopApp.class);
        Wrapper<SytDesktopApp> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    private QueryWrapper<SytDesktopApp> buildWrapper(SytDesktopApp sytDesktopApp){
        QueryWrapper<SytDesktopApp> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(sytDesktopApp.getVisitorVisibility())) {
            wrapper.eq("VISITOR_VISIBILITY", sytDesktopApp.getVisitorVisibility());
        }
        return wrapper;
    }
}
