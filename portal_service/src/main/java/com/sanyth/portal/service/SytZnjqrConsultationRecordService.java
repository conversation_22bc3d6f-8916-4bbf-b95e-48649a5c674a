package com.sanyth.portal.service;

import com.sanyth.portal.model.SytZnjqrConsultationRecord;
import org.springframework.data.domain.Page;

import java.util.List;

public interface SytZnjqrConsultationRecordService {

    Long count();

    SytZnjqrConsultationRecord save(SytZnjqrConsultationRecord record);

    void delete(String... id);

    List<SytZnjqrConsultationRecord> queryList(SytZnjqrConsultationRecord record);

    Page<SytZnjqrConsultationRecord> queryByPage(Integer pageNum, Integer pageSize, SytZnjqrConsultationRecord record);
}
