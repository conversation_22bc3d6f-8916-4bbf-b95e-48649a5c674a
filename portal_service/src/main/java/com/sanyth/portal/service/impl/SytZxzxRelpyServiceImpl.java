package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytZxzxRelpyMapper;
import com.sanyth.portal.model.SytZxzxRelpy;
import com.sanyth.portal.service.ISytZxzxRelpyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 回复记录表 服务实现
 * Created by ZWB on 2024-10-30.
 */
@Service
public class SytZxzxRelpyServiceImpl extends ServiceImpl<SytZxzxRelpyMapper, SytZxzxRelpy> implements ISytZxzxRelpyService {

    @Override
    public Page<SytZxzxRelpy> queryPage(BaseQuery query) {
        Page<SytZxzxRelpy> page = new Page<>(query.getPage(), query.getPageSize());
        SytZxzxRelpy obj = JSON.toJavaObject(query.getQueryParam(), SytZxzxRelpy.class);
        QueryWrapper<SytZxzxRelpy> wrapper = buildWrapper(obj);
        wrapper.orderByDesc("CREATE_DATE");
        return page(page, wrapper);
    }

    @Override
    public List<SytZxzxRelpy> queryList(BaseQuery query) {
        SytZxzxRelpy obj = JSON.toJavaObject(query.getQueryParam(), SytZxzxRelpy.class);
        QueryWrapper<SytZxzxRelpy> wrapper = buildWrapper(obj);
        wrapper.orderByDesc("CREATE_DATE");
        return list(wrapper);
    }

    private QueryWrapper<SytZxzxRelpy> buildWrapper(SytZxzxRelpy sytZxzxRelpy){
        QueryWrapper<SytZxzxRelpy> wrapper = new QueryWrapper<>();
        // Query condition...
        if(StringUtils.isNotBlank(sytZxzxRelpy.getQid())){
            wrapper.eq("qid", sytZxzxRelpy.getQid());
        }
        if(StringUtils.isNotBlank(sytZxzxRelpy.getHumanCode())){
            wrapper.eq("HUMAN_CODE", sytZxzxRelpy.getHumanCode());
        }
        if(StringUtils.isNotBlank(sytZxzxRelpy.getHumanName())){
            wrapper.like("HUMAN_NAME", sytZxzxRelpy.getHumanName());
        }

        return wrapper;
    }
}
