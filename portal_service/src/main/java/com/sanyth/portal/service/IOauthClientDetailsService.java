package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.OauthClientDetails;

/**
 * ${table.comment} 服务接口
 * Created by JIANGPING on 2020-05-15.
 */
public interface IOauthClientDetailsService extends IService<OauthClientDetails> {
    Page<OauthClientDetails> queryPage(BaseQuery query);

    OauthClientDetails getByidOrAppidAndName(String idOrAppid,String name);
}
