package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytZxzxZxflb;

import java.util.List;

/**
 * 在线咨询分类 服务接口
 * Created by ZWB on 2024-10-30.
 */
public interface ISytZxzxZxflbService extends IService<SytZxzxZxflb> {
        Page<SytZxzxZxflb> queryPage(BaseQuery query);

        List<SytZxzxZxflb> queryList(BaseQuery query);
}
