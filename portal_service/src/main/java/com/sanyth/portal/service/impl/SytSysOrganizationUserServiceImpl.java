package com.sanyth.portal.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.portal.model.SytSysOrganizationUser;
import com.sanyth.portal.mapper.SytSysOrganizationUserMapper;
import com.sanyth.portal.service.ISytSysOrganizationUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.core.base.BaseQuery;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-15.
 */
@Service
public class SytSysOrganizationUserServiceImpl extends ServiceImpl<SytSysOrganizationUserMapper, SytSysOrganizationUser> implements ISytSysOrganizationUserService {

    @Override
    public Page<SytSysOrganizationUser> queryPage(BaseQuery query, SytSysOrganizationUser sytSysOrganizationUser) {
        Page<SytSysOrganizationUser> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<SytSysOrganizationUser> wrapper = buildWrapper(sytSysOrganizationUser);
        return page(page, wrapper);
    }

    private Wrapper<SytSysOrganizationUser> buildWrapper(SytSysOrganizationUser sytSysOrganizationUser){
        QueryWrapper<SytSysOrganizationUser> wrapper = new QueryWrapper<>();
        // Query condition...

        return wrapper;
    }
}
