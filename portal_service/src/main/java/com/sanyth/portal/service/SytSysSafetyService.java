package com.sanyth.portal.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.Constants;
import com.sanyth.portal.model.SytSysSafety;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class SytSysSafetyService {

    @Autowired
    MongoTemplate mongoTemplate;

    public void save(SytSysSafety entity) {
        entity.setId(UUID.randomUUID().toString().replaceAll("-",""));
        mongoTemplate.save(entity);
    }

    public void update(SytSysSafety entity) {
        Update update = new Update()
                .set("code", entity.getCode())
                .set("value", entity.getValue());
        mongoTemplate.upsert(new Query().addCriteria(Criteria.where("id").is(entity.getId())),
                update, SytSysSafety.class);
    }

    public void edit(JSONObject param) {
        // 删除原有数据
        deleteByCode(Constants.PASSWORD_STRATEGY_GENERATE + "," + Constants.PASSWORD_STRATEGY_SAFETY + "," + Constants.PASSWORD_STRATEGY_MODIFY);
        // 保存新的数据
        JSONObject generateJson = param.getJSONObject(Constants.PASSWORD_STRATEGY_GENERATE);
        SytSysSafety safety = new SytSysSafety();
        if(generateJson != null){
            safety.setId(UUID.randomUUID().toString().replaceAll("-",""));
            safety.setCode(Constants.PASSWORD_STRATEGY_GENERATE);
            safety.setValue(generateJson.toString());
            this.save(safety);
        }

        JSONObject safetyJson = param.getJSONObject(Constants.PASSWORD_STRATEGY_SAFETY);
        if(safetyJson != null) {
            safety = new SytSysSafety();
            safety.setId(UUID.randomUUID().toString().replaceAll("-",""));
            safety.setCode(Constants.PASSWORD_STRATEGY_SAFETY);
            safety.setValue(safetyJson.toString());
            this.save(safety);
        }

        JSONObject modifyJson = param.getJSONObject(Constants.PASSWORD_STRATEGY_MODIFY);
        if(modifyJson != null){
            safety = new SytSysSafety();
            safety.setId(UUID.randomUUID().toString().replaceAll("-",""));
            safety.setCode(Constants.PASSWORD_STRATEGY_MODIFY);
            safety.setValue(modifyJson.toString());
            this.save(safety);
        }
    }

    public void delete(String... id) {
        if (id.length > 0) {
            List<String> list = Arrays.asList(id);
            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(list));
            mongoTemplate.remove(query, SytSysSafety.class);
        }
    }
    public void deleteByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            Query query = getQuery(new SytSysSafety().setCode(code));
            mongoTemplate.remove(query, SytSysSafety.class);
        }
    }
    public List<SytSysSafety> queryByVo(SytSysSafety entity){
        Query q = getQuery(entity);
        return mongoTemplate.find(q, SytSysSafety.class);
    }

    public List<SytSysSafety> queryList(BaseQuery query){
        Query q = getQuery(JSON.toJavaObject(query.getQueryParam(), SytSysSafety.class));
        return mongoTemplate.find(q, SytSysSafety.class);
    }

    public Page<SytSysSafety> queryPage(BaseQuery query) {
        Query q = getQuery(JSON.toJavaObject(query.getQueryParam(), SytSysSafety.class));
        long count = mongoTemplate.count(q, SytSysSafety.class);
        Pageable pageable = PageRequest.of(query.getPage() - 1,
                query.getPageSize(),
                Sort.by(Sort.Direction.DESC, "id"));
        List<SytSysSafety> list = mongoTemplate.find(q.with(pageable), SytSysSafety.class);
        Page<SytSysSafety> objectPage = new Page<>();
        objectPage.setRecords(list);
        objectPage.setTotal(count);
        objectPage.setCurrent(query.getPage());
        objectPage.setSize(query.getPageSize());
        return objectPage;
    }


    private Query getQuery(SytSysSafety entity) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(entity.getId())) {
            criteria.and("id").in(entity.getId().split(","));
        }
        if (StringUtils.isNotBlank(entity.getCode())) {
            criteria.and("code").in(entity.getCode().split(","));
        }

        query.addCriteria(criteria);
        return query;
    }

    public Map<String, Object> getByCode(String code) {
        Query q = getQuery(new SytSysSafety().setCode(code));
        SytSysSafety sytSysSafety = mongoTemplate.findOne(q, SytSysSafety.class);
        return this.getSafetyData(sytSysSafety);
    }
    public Map<String, Object> getSafetyData(SytSysSafety sytSysSafety) {
        Map<String, Object> map = new HashMap<>();
        if(sytSysSafety == null){
            return map;
        }
        if(Objects.equals(Constants.PASSWORD_STRATEGY_GENERATE, sytSysSafety.getCode())
                || Objects.equals(Constants.PASSWORD_STRATEGY_SAFETY, sytSysSafety.getCode())){
            String value = sytSysSafety.getValue();
            if(StringUtils.isNotBlank(value)){
                map = JSONObject.parseObject(value, Map.class);
            }
        }
        // 密码修改策略：返回对应的正则表达式
        if(Objects.equals(Constants.PASSWORD_STRATEGY_MODIFY, sytSysSafety.getCode())){
            String value = sytSysSafety.getValue();
            if(StringUtils.isNotBlank(value)){
                //数字：(?=.*\d)  (?=.*[0-9])
                //小写字母：(?=.*[a-z])
                //大写字母：(?=.*[A-Z])
                //特殊字符：(?=.*[~'!@#$%^&*()\-+_=:?.\[\]<>/,;{}])
                //长度限制：.{6,12}
                Map mapTmp = JSONObject.parseObject(value, Map.class);
                StringBuilder strB = new StringBuilder();
                StringBuilder strTip = new StringBuilder("必须包含");
                // 是否包含数字
                if(mapTmp.get("sfsz") != null && Objects.equals(Constants.HAS_YES, mapTmp.get("sfsz").toString())){
                    strB.append("(?=.*[0-9])");
                    strTip.append("数字").append("、");
                }
                // 是否包含小写字母
                if(mapTmp.get("sfxxzm") != null && Objects.equals(Constants.HAS_YES, mapTmp.get("sfxxzm").toString())){
                    strB.append("(?=.*[a-z])");
                    strTip.append("小写字母").append("、");
                }
                // 是否包含大写字母
                if(mapTmp.get("sfdxzm") != null && Objects.equals(Constants.HAS_YES, mapTmp.get("sfdxzm").toString())){
                    strB.append("(?=.*[A-Z])");
                    strTip.append("大写字母").append("、");
                }
                // 是否包含特殊符号
                if(mapTmp.get("sffh") != null && Objects.equals(Constants.HAS_YES, mapTmp.get("sffh").toString())){
                    // strB.append("(?=.*[~!@#$%^&*])");
                    strB.append("(?=.*[~'!@#$%^&*()\\-+_=:?.\\[\\]<>/,;{}])");
                    strTip.append("特殊符号(~'!@#$%^&*()-+_=:?.[]<>/,;{})").append("、");
                }
                strTip = strTip.length() > 0 ? strTip.deleteCharAt(strTip.length() - 1) : strTip;
                // 密码最小长度
                if(mapTmp.get("passwordlength") != null){
                    strB.append(".{" + mapTmp.get("passwordlength") + ",}");
                    map.put("passwordlength", mapTmp.get("passwordlength"));
                    if(strTip.length() > 0){
                        strTip.append("，");
                    }
                    strTip.append("长度不少于").append(mapTmp.get("passwordlength")).append("位");
                }
                map.put("includeTip", strTip.toString());  // 密码中要包含的内容
                map.put("passRegExp", strB.toString()); // 密码校验正则
                // 是否允许包含用户名
                if(mapTmp.get("sfyhm") != null){
                    map.put("sfyhm", mapTmp.get("sfyhm"));
                }
            }
        }
        return map;
    }

    public Map<String, Object> getAll(String codes) {
        Query query = getQuery(new SytSysSafety().setCode(codes));
        List<SytSysSafety> list = mongoTemplate.find(query, SytSysSafety.class);
        Map<String, Object> map = new HashMap<>();
        list.forEach(objTmp -> {
            if(StringUtils.isNotBlank(objTmp.getValue())){
                map.putAll(JSONObject.parseObject(objTmp.getValue(), Map.class));
            }
        });
        return map;
    }
}
