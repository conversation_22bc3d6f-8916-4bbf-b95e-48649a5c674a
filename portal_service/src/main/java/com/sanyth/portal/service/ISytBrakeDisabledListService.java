package com.sanyth.portal.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.model.SytBrakeDisabledList;

/**
 * 闸机禁用名单 服务接口
 * Created by MHR on 2023-03-20.
 */
public interface ISytBrakeDisabledListService extends IService<SytBrakeDisabledList> {
        Page<SytBrakeDisabledList> queryPage(BaseQuery query);

        void edit(SytBrakeDisabledList sytBrakeDisabledList);

        boolean permission(SytBrakeDisabledList param);
}
