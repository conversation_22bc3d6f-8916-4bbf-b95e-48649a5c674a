package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytServiceCenterVisitMapper;
import com.sanyth.portal.model.SytServiceCenterVisit;
import com.sanyth.portal.service.ISytServiceCenterVisitService;
import org.springframework.stereotype.Service;

/**
 * 服务中心访问记录 服务实现
 * Created by ZWB on 2022-04-08.
 */
@Service
public class SytServiceCenterVisitServiceImpl extends ServiceImpl<SytServiceCenterVisitMapper, SytServiceCenterVisit> implements ISytServiceCenterVisitService {

    @Override
    public Page<SytServiceCenterVisit> queryPage(BaseQuery query) {
        Page<SytServiceCenterVisit> page = new Page<>(query.getPage(), query.getPageSize());
        SytServiceCenterVisit obj = JSON.toJavaObject(query.getQueryParam(), SytServiceCenterVisit.class);
        Wrapper<SytServiceCenterVisit> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    private Wrapper<SytServiceCenterVisit> buildWrapper(SytServiceCenterVisit sytServiceCenterVisit){
        QueryWrapper<SytServiceCenterVisit> wrapper = new QueryWrapper<>();
        // Query condition...

        return wrapper;
    }
}
