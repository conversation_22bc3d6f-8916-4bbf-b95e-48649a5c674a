package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.Constants;
import com.sanyth.portal.mapper.SytLuckyDrawMapper;
import com.sanyth.portal.mapper.SytLuckyDrawParticipatorMapper;
import com.sanyth.portal.model.SytLuckyDraw;
import com.sanyth.portal.model.SytLuckyDrawAwards;
import com.sanyth.portal.model.SytLuckyDrawParticipator;
import com.sanyth.portal.service.ISytLuckyDrawAwardsService;
import com.sanyth.portal.service.ISytLuckyDrawParticipatorService;
import com.sanyth.portal.service.ISytLuckyDrawService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 抽奖活动 服务实现
 * Created by ZWB on 2023-12-18.
 */
@Service
public class SytLuckyDrawServiceImpl extends ServiceImpl<SytLuckyDrawMapper, SytLuckyDraw> implements ISytLuckyDrawService {

    @Resource
    private ISytLuckyDrawAwardsService sytLuckyDrawAwardsService;
    @Resource
    private ISytLuckyDrawParticipatorService sytLuckyDrawParticipatorService;
    @Resource
    private SytLuckyDrawParticipatorMapper sytLuckyDrawParticipatorMapper;

    @Override
    public Page<SytLuckyDraw> queryPage(BaseQuery query) {
        Page<SytLuckyDraw> page = new Page<>(query.getPage(), query.getPageSize());
        SytLuckyDraw obj = JSON.toJavaObject(query.getQueryParam(), SytLuckyDraw.class);
        Wrapper<SytLuckyDraw> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public  List<SytLuckyDrawParticipator> drawLottery(SytLuckyDraw luckyDraw, Integer headcount) {
        List<SytLuckyDrawParticipator> winnersList = new ArrayList<>();

        String hdcjfs = luckyDraw.getHdcjfs();
        QueryWrapper<SytLuckyDrawAwards> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("LUCKY_ID", luckyDraw.getId());
        List<SytLuckyDrawAwards> awardsList = sytLuckyDrawAwardsService.list(queryWrapper);
        // 部门中奖名额比例
        Map<String, Integer> departmentQuotas = new HashMap<>();
        for (SytLuckyDrawAwards awards : awardsList) {
            departmentQuotas.put(StringUtils.join(awards.getItem(), ","), awards.getValue());
        }
        // 使用Stream API对Map进行排序
        Map<String, Integer> sortedDepartmentQuotas = departmentQuotas.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByValue())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
        //查询签到人员数据
        QueryWrapper<SytLuckyDrawParticipator> participatorQueryWrapper = new QueryWrapper<>();
        participatorQueryWrapper.eq("LUCKY_ID", luckyDraw.getId());
        participatorQueryWrapper.nested(participatorQueryWrapperOr -> participatorQueryWrapperOr.eq("WIN_OR_NOT", Constants.HAS_NO).or().isNull("WIN_OR_NOT"));
        List<SytLuckyDrawParticipator> participatorList = sytLuckyDrawParticipatorService.list(participatorQueryWrapper);
        //中奖名单
        List<SytLuckyDrawParticipator> prizeWinners = new ArrayList<>();

        //倍率
        if (Constants.LUCKYDRAW_HDCJFS_BL.equals(hdcjfs)) {
            for (Map.Entry<String, Integer> entry : sortedDepartmentQuotas.entrySet()) {
                String department = entry.getKey();
                int quota = entry.getValue();

                List<SytLuckyDrawParticipator> candidates = getCandidates(participatorList, department);
                List<SytLuckyDrawParticipator> selected = selectWinners(candidates, Math.round(headcount * ((float) quota / getTotalQuota(departmentQuotas))));
                int i = prizeWinners.size() + selected.size() - headcount;
                if (i <= 0) {
                    prizeWinners.addAll(selected);
                    participatorList.removeAll(selected);
                } else if (i > 0) {
                    List<SytLuckyDrawParticipator> subbedList = selected.subList(0, selected.size() - i);
                    prizeWinners.addAll(subbedList);
                    participatorList.removeAll(subbedList);
                }
            }

        } else if (Constants.LUCKYDRAW_HDCJFS_RS.equals(hdcjfs)) {  //人数 时间紧先不做

        }
        Integer maxNum = sytLuckyDrawParticipatorMapper.queryMaxNum(luckyDraw.getId());
        for (SytLuckyDrawParticipator participator : prizeWinners) {
            participator.setNum((maxNum == null ? 0 : maxNum) + 1);
            participator.setWinOrNot(Constants.HAS_YES);
            sytLuckyDrawParticipatorService.saveOrUpdate(participator);
            winnersList.add(participator);
        }
        return winnersList;
    }

    // 获取某个部门的候选人员列表
    private static List<SytLuckyDrawParticipator> getCandidates(List<SytLuckyDrawParticipator> luckyDrawParticipators, String department) {
        List<SytLuckyDrawParticipator> candidates = new ArrayList<>();

        for (SytLuckyDrawParticipator participator : luckyDrawParticipators) {
            if (department.contains(participator.getOrgids())) {
                candidates.add(participator);
            }
        }
        return candidates;
    }


    // 从候选人员中随机选取中奖者
    private static List<SytLuckyDrawParticipator> selectWinners(List<SytLuckyDrawParticipator> candidates, int quota) {
        List<SytLuckyDrawParticipator> winners = new ArrayList<>();
        Random random = new Random();

        while (winners.size() < quota && !candidates.isEmpty()) {
            int selectedIndex = random.nextInt(candidates.size());
            winners.add(candidates.remove(selectedIndex));
        }

        return winners;
    }

    // 获取总中奖名额
    private static int getTotalQuota(Map<String, Integer> departmentQuotas) {
        int totalQuota = 0;
        for (int quota : departmentQuotas.values()) {
            totalQuota += quota;
        }
        return totalQuota;
    }

    private Wrapper<SytLuckyDraw> buildWrapper(SytLuckyDraw sytLuckyDraw){
        QueryWrapper<SytLuckyDraw> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(sytLuckyDraw.getName())) {
            wrapper.like("NAME", sytLuckyDraw.getName());
        }
        if (StringUtils.isNotBlank(sytLuckyDraw.getHdcjr())) {
            wrapper.eq("HDCJR", sytLuckyDraw.getHdcjr());
        }
        if (StringUtils.isNotBlank(sytLuckyDraw.getHdcjrxm())) {
            wrapper.eq("HDCJRXM", sytLuckyDraw.getHdcjrxm());
        }
        return wrapper;
    }
}
