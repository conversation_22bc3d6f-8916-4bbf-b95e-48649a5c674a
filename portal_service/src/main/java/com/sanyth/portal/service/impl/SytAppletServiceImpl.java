package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytAppletMapper;
import com.sanyth.portal.model.SytApplet;
import com.sanyth.portal.service.ISytAppletService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-18.
 */
@Service
public class SytAppletServiceImpl extends ServiceImpl<SytAppletMapper, SytApplet> implements ISytAppletService {

    @Override
    public Page<SytApplet> queryPage(BaseQuery query) {
        Page<SytApplet> page = new Page<>(query.getPage(), query.getPageSize());
         SytApplet sytApplet = JSON.toJavaObject(query.getQueryParam(), SytApplet.class);
        QueryWrapper<SytApplet> wrapper = buildWrapper(sytApplet);
        return page(page, wrapper);
    }

    public QueryWrapper<SytApplet> buildWrapper(SytApplet sytApplet){
        QueryWrapper<SytApplet> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(sytApplet.getType())) {
            wrapper.eq("type", sytApplet.getType());
        }
        if (StringUtils.isNotBlank(sytApplet.getName())) {
            wrapper.like("name", sytApplet.getName());
        }
        if (StringUtils.isNotBlank(sytApplet.getAppid())) {
            wrapper.eq("APPID", sytApplet.getAppid());
        }
        if (StringUtils.isNotBlank(sytApplet.getStatus())) {
            wrapper.eq("STATUS", sytApplet.getStatus());
        }
        if (StringUtils.isNotBlank(sytApplet.getPlacement())) {
            wrapper.like("PLACEMENT", sytApplet.getPlacement());
        }
        wrapper.orderByAsc("sort");
        return wrapper;
    }
}
