package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.core.common.FileOperationUtil;
import com.sanyth.portal.mapper.SytFileResourceMapper;
import com.sanyth.portal.model.SytFileResource;
import com.sanyth.portal.service.ISytFileResourceService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 常用文件管理 服务实现
 * Created by WDL on 2022-11-16.
 */
@Service
public class SytFileResourceServiceImpl extends ServiceImpl<SytFileResourceMapper, SytFileResource> implements ISytFileResourceService {

    @Autowired
    FileOperationUtil fileOperationUtil;

    @Override
    public Page<SytFileResource> queryPage(BaseQuery query) {
        Page<SytFileResource> page = new Page<>(query.getPage(), query.getPageSize());
        SytFileResource obj = JSON.toJavaObject(query.getQueryParam(), SytFileResource.class);
        QueryWrapper<SytFileResource> fileResourceQueryWrapper = buildWrapper(obj);
        CurrentUser currentUser = query.getCurrentUser();
        if (currentUser != null) {
            fileResourceQueryWrapper.and(queryWrapper -> queryWrapper
                    .or(wrapper -> wrapper.and(s -> s.isNull("ROLEIDS").or().like("ROLEIDS", "[]"))
                            .and(s -> s.isNull("ORGIDS").or().like("ORGIDS", "[]"))
                    )
                    .or(wrapper -> {
                        wrapper.and(subWrapper -> subWrapper.isNull("ROLEIDS").or().like("ROLEIDS", "[]").or().like("ROLEIDS", currentUser.getRoleId()))
                                .and(subWrapper -> {
                                    currentUser.getOrganizations().forEach(organization -> {
                                        subWrapper.or().like("ORGIDS", organization.getId());
                                    });
                                });
                    })
                    .or(wrapper -> {
                        wrapper.like("ROLEIDS", currentUser.getRoleId())
                                .and(subWrapper -> {
                                    currentUser.getOrganizations().forEach(organization -> {
                                        subWrapper.or().like("ORGIDS", organization.getId());
                                    });
                                    subWrapper.or().isNull("ORGIDS").or().like("ORGIDS", "[]");
                                });
                    }));

        }
        return page(page, fileResourceQueryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editFile(SytFileResource sytFileResource) throws Exception {
        if(StringUtils.isBlank(sytFileResource.getId())){
            Date date = Calendar.getInstance().getTime();
            sytFileResource.setCreatetime(date);
            sytFileResource.setUpdatetime(date);
            this.save(sytFileResource);
        } else {
            SytFileResource fileResource = this.getById(sytFileResource.getId());
            if(fileResource != null){
                String fileIdOld = fileResource.getFileId();
                String fileIdNew = sytFileResource.getFileId();
                if(StringUtils.isNotBlank(fileIdNew) && !Objects.equals(fileIdOld, fileIdNew)){
                    fileOperationUtil.remove(fileIdOld);
                }
                fileResource.setName(sytFileResource.getName());
                fileResource.setStatus(sytFileResource.getStatus());
                fileResource.setFileId(fileIdNew);
                fileResource.setRemark(sytFileResource.getRemark());
                fileResource.setSort(sytFileResource.getSort());
                fileResource.setUpdatetime(Calendar.getInstance().getTime());
                fileResource.setRoleIds(sytFileResource.getRoleIds());
                fileResource.setOrgIds(sytFileResource.getOrgIds());
                this.updateById(fileResource);
            }
        }
    }

    @Override
    public void removeFile(List<String> asList) {
        asList.forEach(id -> {
            SytFileResource fileResource = this.getById(id);
            String fileId = fileResource.getFileId();
            if(StringUtils.isNotBlank(fileId)){
                try {
                    fileOperationUtil.remove(fileId);
                } catch (Exception e) {
                }
            }
            this.removeById(id);
        });
    }

    private QueryWrapper<SytFileResource> buildWrapper(SytFileResource sytFileResource){
        QueryWrapper<SytFileResource> wrapper = new QueryWrapper<>();
        // Query condition...
        if(StringUtils.isNotBlank(sytFileResource.getId())){
            wrapper.eq("ID", sytFileResource.getId());
        }
        if(StringUtils.isNotBlank(sytFileResource.getName())){
            wrapper.like("NAME", sytFileResource.getName());
        }
        if(StringUtils.isNotBlank(sytFileResource.getStatus())){
            wrapper.like("STATUS", sytFileResource.getStatus());
        }

        wrapper.orderByAsc("SORT");
        return wrapper;
    }
}
