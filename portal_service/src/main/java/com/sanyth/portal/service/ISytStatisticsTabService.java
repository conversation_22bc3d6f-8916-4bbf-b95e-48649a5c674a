package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytStatisticsTab;

import java.util.List;

/**
 * 流程统计管理 服务接口
 * Created by WDL on 2023-03-06.
 */
public interface ISytStatisticsTabService extends IService<SytStatisticsTab> {
        Page<SytStatisticsTab> queryPage(BaseQuery query);

        List<SytStatisticsTab> queryList(BaseQuery query);
}
