package com.sanyth.portal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SysResource;
import com.sanyth.portal.mapper.SysResourceMapper;
import com.sanyth.portal.service.ISysResourceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-20.
 */
@Service
public class SysResourceServiceImpl extends ServiceImpl<SysResourceMapper, SysResource> implements ISysResourceService {

    @Override
    public void delete(String... id) {

    }

    @Override
    public Page<SysResource> queryPage(BaseQuery query, SysResource sysResource) {
        Page<SysResource> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<SysResource> wrapper = buildWrapper(sysResource);
        return page(page, wrapper);
    }

    @Override
    public List<SysResource> queryList(SysResource resource) {
        return list(buildWrapper(resource));
    }

    private Wrapper<SysResource> buildWrapper(SysResource resource){
        QueryWrapper<SysResource> wrapper = new QueryWrapper<>();
        // Query condition...
        if (resource.getType() != null)
            wrapper.eq("type", resource.getType());
//        if (resource.getType() != null)
//            wrapper.eq("is_btn", resource.getIsBtn());
        if (resource.getParentId()!=null)
            wrapper.eq("parent_id", resource.getParentId());
//        if (StringUtils.isNotBlank(resource.getStatus()))
//            wrapper.eq("status", resource.getStatus());
        wrapper.orderByAsc("sort");
        return wrapper;
    }
}
