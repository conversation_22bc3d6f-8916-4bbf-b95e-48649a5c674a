package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytZxzxQuestion;

import java.util.List;

/**
 * 咨询问题记录 服务接口
 * Created by ZWB on 2024-10-30.
 */
public interface ISytZxzxQuestionService extends IService<SytZxzxQuestion> {
    Page<SytZxzxQuestion> queryPage(BaseQuery query);

    Page<SytZxzxQuestion> queryPageForReplyer(BaseQuery query);

    void removeAndReplyByIds(List<String> list);

}
