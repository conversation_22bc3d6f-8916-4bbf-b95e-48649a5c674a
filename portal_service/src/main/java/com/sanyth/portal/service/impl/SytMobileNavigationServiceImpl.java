package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.Constants;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.mapper.SytMobileNavigationMapper;
import com.sanyth.portal.model.SytMobileNavigation;
import com.sanyth.portal.service.ISytMobileNavigationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 移动端底部导航栏 服务实现
 * Created by WDL on 2023-01-10.
 */
@Service
public class SytMobileNavigationServiceImpl extends ServiceImpl<SytMobileNavigationMapper, SytMobileNavigation> implements ISytMobileNavigationService {

    @Resource
    private SytMobileNavigationMapper sytMobileNavigationMapper;

    @Override
    public Page<SytMobileNavigation> queryPage(BaseQuery query) {
        Page<SytMobileNavigation> page = new Page<>(query.getPage(), query.getPageSize());
        SytMobileNavigation obj = JSON.toJavaObject(query.getQueryParam(), SytMobileNavigation.class);
        Wrapper<SytMobileNavigation> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public List<SytMobileNavigation> queryList(SytMobileNavigation entity) {
        return list(buildWrapper(entity));
    }

    @Override
    public List<SytMobileNavigation> getListByUser(CurrentUser currentUser, SytMobileNavigation entity) {
        entity.setStatus(Constants.STATUS_ENABLE);
        QueryWrapper<SytMobileNavigation> hallStatisticsWrapper = buildWrapper(entity);
        if (currentUser != null && !"syt_visitor".equals(currentUser.getHumanId())) {
            hallStatisticsWrapper.and(queryWrapper -> queryWrapper
                    .or(wrapper -> wrapper.and(s -> s.isNull("ROLEIDS").or().like("ROLEIDS", "[]"))
                            .and(s -> s.isNull("ORGIDS").or().like("ORGIDS", "[]"))
                    )
                    .or(wrapper -> {
                        wrapper.and(subWrapper -> subWrapper.isNull("ROLEIDS").or().like("ROLEIDS", "[]").or().like("ROLEIDS", currentUser.getRoleId()))
                                .and(subWrapper -> {
                                    currentUser.getOrganizations().forEach(organization -> {
                                        subWrapper.or().like("ORGIDS", organization.getId());
                                    });
                                });
                    })
                    .or(wrapper -> {
                        wrapper.like("ROLEIDS", currentUser.getRoleId())
                                .and(subWrapper -> {
                                    currentUser.getOrganizations().forEach(organization -> {
                                        subWrapper.or().like("ORGIDS", organization.getId());
                                    });
                                    subWrapper.or().isNull("ORGIDS").or().like("ORGIDS", "[]");
                                });
                    })
            );
        } else {
            hallStatisticsWrapper.eq("VISITOR_VISIBILITY", Constants.HAS_YES);
            hallStatisticsWrapper.and(wrapper -> wrapper.isNull("ROLEIDS").or().like("ROLEIDS", "[]").isNull("ORGIDS").or().like("ORGIDS", "[]"));
        }
        return list(hallStatisticsWrapper);
    }
    /**
     * 更新状态
     * @param id
     * @param state
     */
    @Override
    public void updateState(String id, String state) {
        SytMobileNavigation mobileNavigation = new SytMobileNavigation().setId(id);
        if(Objects.equals(Constants.STATUS_ENABLE, state)){
            mobileNavigation.setStatus(Constants.STATUS_ENABLE);
        } else {
            mobileNavigation.setStatus(Constants.STATUS_DISABLE);
        }
        sytMobileNavigationMapper.updateById(mobileNavigation);
    }

    private QueryWrapper<SytMobileNavigation> buildWrapper(SytMobileNavigation entity){
        QueryWrapper<SytMobileNavigation> wrapper = new QueryWrapper<>();
        // Query condition...
        // Query condition...
        if(StringUtils.isNotBlank(entity.getId())) {
            wrapper.in("ID", entity.getId());
        }
        if(StringUtils.isNotBlank(entity.getName())) {
            wrapper.like("name", entity.getName());
        }
        if(StringUtils.isNotBlank(entity.getStatus())) {
            wrapper.eq("status", entity.getStatus());
        }
        if (StringUtils.isNotBlank(entity.getVisitorVisibility())) {
            wrapper.eq("VISITOR_VISIBILITY", entity.getVisitorVisibility());
        }
        if(StringUtils.isNotBlank(entity.getShowLocale())) {
            wrapper.like("SHOW_LOCALE", entity.getShowLocale());
        }
        wrapper.orderByAsc("sort");

        return wrapper;
    }
}
