package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytZxzxRelpy;

import java.util.List;

/**
 * 回复记录表 服务接口
 * Created by ZWB on 2024-10-30.
 */
public interface ISytZxzxRelpyService extends IService<SytZxzxRelpy> {
    Page<SytZxzxRelpy> queryPage(BaseQuery query);
    List<SytZxzxRelpy> queryList(BaseQuery query);
}
