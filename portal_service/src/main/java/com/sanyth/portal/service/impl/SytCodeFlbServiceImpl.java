package com.sanyth.portal.service.impl;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.mapper.SytCodeFlbMapper;
import com.sanyth.portal.model.SytCodeFlb;
import com.sanyth.portal.service.ISytCodeFlbService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ${table.comment} 服务实现
 * Created by ZWB on 2022-10-18.
 */
@Service
public class SytCodeFlbServiceImpl extends ServiceImpl<SytCodeFlbMapper, SytCodeFlb> implements ISytCodeFlbService {

    @Override
    public Page<SytCodeFlb> queryPage(BaseQuery query) {
        Page<SytCodeFlb> page = new Page<>(query.getPage(), query.getPageSize());
        SytCodeFlb obj = JSON.toJavaObject(query.getQueryParam(), SytCodeFlb.class);
        Wrapper<SytCodeFlb> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public List<SytCodeFlb> queryList(SytCodeFlb sytCodeFlb) {
        return list(buildWrapper(sytCodeFlb));
    }

    private Wrapper<SytCodeFlb> buildWrapper(SytCodeFlb sytCodeFlb){
        QueryWrapper<SytCodeFlb> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(sytCodeFlb.getBz()))
           wrapper.like("bz", sytCodeFlb.getBz());
        if (StringUtils.isNotBlank(sytCodeFlb.getName()))
           wrapper.like("name", sytCodeFlb.getName());
       if (StringUtils.isNotBlank(sytCodeFlb.getCode()))
           wrapper.eq("code", sytCodeFlb.getCode());
        if (StringUtils.isNotBlank(sytCodeFlb.getId()))
            wrapper.in("id", sytCodeFlb.getId().split(","));
       wrapper.orderByAsc("code");
        return wrapper;
    }
}
