package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytDesktopBlockUser;

import java.util.List;

/**
 * 用户板块关联表 服务接口
 * Created by ZWB on 2022-04-18.
 */
public interface ISytDesktopBlockUserService extends IService<SytDesktopBlockUser> {
        Page<SytDesktopBlockUser> queryPage(BaseQuery query);

        List<SytDesktopBlockUser> queryList(SytDesktopBlockUser blockUser);
}
