package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.Constants;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.mapper.SytZxzxQuestionMapper;
import com.sanyth.portal.mapper.SytZxzxRelpyMapper;
import com.sanyth.portal.model.SytZxzxQuestion;
import com.sanyth.portal.service.ISytZxzxQuestionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 咨询问题记录 服务实现
 * Created by ZWB on 2024-10-30.
 */
@Service
public class SytZxzxQuestionServiceImpl extends ServiceImpl<SytZxzxQuestionMapper, SytZxzxQuestion> implements ISytZxzxQuestionService {

    @Resource
    private SytZxzxRelpyMapper zxzxRelpyMapper;

    @Override
    public Page<SytZxzxQuestion> queryPage(BaseQuery query) {
        Page<SytZxzxQuestion> page = new Page<>(query.getPage(), query.getPageSize());
        SytZxzxQuestion obj = JSON.toJavaObject(query.getQueryParam(), SytZxzxQuestion.class);
        QueryWrapper<SytZxzxQuestion> wrapper = buildWrapper(obj);
        CurrentUser currentUser = query.getCurrentUser();
        if (!currentUser.getRoleKey().contains(Constants.ROLE_KEY_SUPERADMIN)) {
            if ("myreply".equals(obj.getType())) {
                wrapper.exists("SELECT 1 FROM SYT_ZXZX_RELPY r WHERE r.QID = SYT_ZXZX_QUESTION.ID " +
                        "AND r.HUMAN_CODE = '" + currentUser.getHumanCode() + "'");
            } else if ("myquestion".equals(obj.getType())) {
                wrapper.eq("HUMAN_CODE", currentUser.getHumanCode());
            } else {
                wrapper.and(w -> w.exists("SELECT 1 FROM SYT_ZXZX_ZXFLB f WHERE f.ID = SYT_ZXZX_QUESTION.FLID " +
                                "AND INSTR(f.HUMAN_CODE, '" + currentUser.getHumanCode() + "') > 0")
                        .or()
                        .eq("HUMAN_CODE", currentUser.getHumanCode()));
            }
        }
        wrapper.orderByDesc("hfzt").orderByDesc("CREATE_DATE");
        return page(page, wrapper);
    }

    @Deprecated
    @Override
    public Page<SytZxzxQuestion> queryPageForReplyer(BaseQuery query) {
        Page<SytZxzxQuestion> page = new Page<>(query.getPage(), query.getPageSize());
        SytZxzxQuestion obj = JSON.toJavaObject(query.getQueryParam(), SytZxzxQuestion.class);
        QueryWrapper<SytZxzxQuestion> wrapper = buildWrapper(obj);
        CurrentUser currentUser = query.getCurrentUser();
        wrapper.exists("SELECT 1 FROM SYT_ZXZX_ZXFLB f WHERE f.ID = SYT_ZXZX_QUESTION.FLID " +
                "AND INSTR(f.HUMAN_CODE, '" + currentUser.getHumanCode() + "') > 0");
        if ("myreply".equals(obj.getType())) {
            wrapper.exists("SELECT 1 FROM SYT_ZXZX_RELPY r WHERE r.QID = SYT_ZXZX_QUESTION.ID " +
                    "AND r.HUMAN_CODE = '" + currentUser.getHumanCode() + "'");
        }
        return page(page, wrapper);
    }

    @Transactional
    @Override
    public void removeAndReplyByIds(List<String> list) {
        removeByIds(list);
        zxzxRelpyMapper.removeByQids(list);
    }

    private QueryWrapper<SytZxzxQuestion> buildWrapper(SytZxzxQuestion sytZxzxQuestion){
        QueryWrapper<SytZxzxQuestion> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(sytZxzxQuestion.getHumanCode())||StringUtils.isNotBlank(sytZxzxQuestion.getHumanCode())) {
            wrapper.eq("sfnm", Constants.HAS_NO);
        }
        if (StringUtils.isNotBlank(sytZxzxQuestion.getHfzt())) {
            wrapper.eq("hfzt", sytZxzxQuestion.getHfzt());
        }
        if (StringUtils.isNotBlank(sytZxzxQuestion.getFlid())) {
            wrapper.eq("flid", sytZxzxQuestion.getFlid());
        }
        if (sytZxzxQuestion.getCreateDate() != null) {
            LocalDateTime startTime = sytZxzxQuestion.getCreateDate().withHour(0).withMinute(0).withSecond(0);
            LocalDateTime endTime = sytZxzxQuestion.getCreateDate().withHour(23).withMinute(59).withSecond(59);
            wrapper.between("CREATE_DATE", startTime, endTime);
        }
        if (StringUtils.isNotBlank(sytZxzxQuestion.getHumanCode())) {
            wrapper.eq("HUMAN_CODE", sytZxzxQuestion.getHumanCode());
        }
        if (StringUtils.isNotBlank(sytZxzxQuestion.getHumanName())) {
            wrapper.like("HUMAN_NAME", sytZxzxQuestion.getHumanName());
        }
        if (StringUtils.isNotBlank(sytZxzxQuestion.getTheme())) {
            wrapper.like("theme", sytZxzxQuestion.getTheme());
        }
        return wrapper;
    }
}
