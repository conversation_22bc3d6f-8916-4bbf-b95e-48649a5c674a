package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytPermissionResource;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * ${table.comment} 服务接口
 * Created by JIANGPING on 2020-05-14.
 */
public interface ISytPermissionResourceService extends IService<SytPermissionResource> {
    void delete(String...id);

    List<SytPermissionResource> getListByRole(String roleId);

    List<SytPermissionResource> queryList(SytPermissionResource resource);

    Page<SytPermissionResource> getAllChildren(String id, BaseQuery query);
}
