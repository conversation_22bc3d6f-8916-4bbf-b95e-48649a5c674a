package com.sanyth.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.model.SytSysOrganization;

import java.util.List;
import java.util.Map;

/**
 * 组织机构表 服务接口
 * Created by JIANGPING on 2020-05-14.
 */
public interface ISytSysOrganizationService extends IService<SytSysOrganization> {
    List<SytSysOrganization> getOrganizationByUser(String userId);

    Page<SytSysOrganization> queryPage(BaseQuery query);

    List<SytSysOrganization> queryList(SytSysOrganization organization);

    List<SytSysOrganization> queryParentList();

    /**
     * 查询服务大厅数据中选择的部门
     */
    List<SytSysOrganization> queryOrgDataInSC();

    List<String> getBpmOrgAttributeConfigList(Map<String,Object> map);
}
