package com.sanyth.portal.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.portal.core.base.BaseQuery;
import com.sanyth.portal.core.common.CurrentUser;
import com.sanyth.portal.mapper.SytStatisticsTabMapper;
import com.sanyth.portal.model.SytStatisticsBlock;
import com.sanyth.portal.model.SytStatisticsTab;
import com.sanyth.portal.service.ISytStatisticsBlockService;
import com.sanyth.portal.service.ISytStatisticsTabService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 流程统计管理 服务实现
 * Created by WDL on 2023-03-06.
 */
@Service
public class SytStatisticsTabServiceImpl extends ServiceImpl<SytStatisticsTabMapper, SytStatisticsTab> implements ISytStatisticsTabService {

    @Autowired
    private ISytStatisticsBlockService sytStatisticsBlockService;

    @Override
    public Page<SytStatisticsTab> queryPage(BaseQuery query) {
        Page<SytStatisticsTab> page = new Page<>(query.getPage(), query.getPageSize());
        SytStatisticsTab obj = JSON.toJavaObject(query.getQueryParam(), SytStatisticsTab.class);
        Wrapper<SytStatisticsTab> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public List<SytStatisticsTab> queryList(BaseQuery query) {
        SytStatisticsTab sytStatisticsTab = JSON.toJavaObject(query.getQueryParam(), SytStatisticsTab.class);
        QueryWrapper<SytStatisticsTab> statisticsTaWrapper = buildWrapper(sytStatisticsTab);
        CurrentUser currentUser = query.getCurrentUser();
        if (currentUser != null) {
            statisticsTaWrapper.and(queryWrapper -> queryWrapper
                    .or(wrapper -> wrapper.and(s -> s.isNull("ROLEIDS").or().like("ROLEIDS", "[]"))
                            .and(s -> s.isNull("ORGIDS").or().like("ORGIDS", "[]"))
                    )
                    .or(wrapper -> {
                        wrapper.and(subWrapper -> subWrapper.isNull("ROLEIDS").or().like("ROLEIDS", "[]").or().like("ROLEIDS", currentUser.getRoleId()))
                                .and(subWrapper -> {
                                    currentUser.getOrganizations().forEach(organization -> {
                                        subWrapper.or().like("ORGIDS", organization.getId());
                                    });
                                });
                    })
                    .or(wrapper -> {
                        wrapper.like("ROLEIDS", currentUser.getRoleId())
                                .and(subWrapper -> {
                                    currentUser.getOrganizations().forEach(organization -> {
                                        subWrapper.or().like("ORGIDS", organization.getId());
                                    });
                                    subWrapper.or().isNull("ORGIDS").or().like("ORGIDS", "[]");
                                });
                    })
            );

        } else {
            statisticsTaWrapper.and(wrapper -> wrapper.isNull("ROLEIDS").isNull("ORGIDS"));
        }
        // 查询数据
        List<SytStatisticsTab> tabList = list(statisticsTaWrapper);
        if(CollectionUtils.isNotEmpty(tabList)){
            String tabIds = tabList.stream().map(SytStatisticsTab::getId).collect(Collectors.joining(","));
            // 查询出对应的统计板块
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("tabId", tabIds);
            List<SytStatisticsBlock> blockList = sytStatisticsBlockService.queryList(new BaseQuery().setCurrentUser(currentUser).setQueryParam(jsonObject));
            Map<String, List<SytStatisticsBlock>> blockMap = blockList.stream().collect(Collectors.groupingBy(SytStatisticsBlock::getTabId,
                    Collectors.mapping(obj -> obj, Collectors.toList())));
            tabList.forEach(obj -> {
                List<SytStatisticsBlock> statisticsBlock = blockMap.get(obj.getId());
                if(statisticsBlock != null){
                    obj.setBlockList(statisticsBlock);
                }
            });
        }
        return tabList;
    }

    private QueryWrapper<SytStatisticsTab> buildWrapper(SytStatisticsTab sytStatisticsTab){
        QueryWrapper<SytStatisticsTab> wrapper = new QueryWrapper<>();
        // Query condition...
        if(StringUtils.isNotBlank(sytStatisticsTab.getName()))
            wrapper.like("name", sytStatisticsTab.getName());
        if(StringUtils.isNotBlank(sytStatisticsTab.getStatus()))
            wrapper.eq("status", sytStatisticsTab.getStatus());

        wrapper.orderByAsc("sort");
        return wrapper;
    }
}
