<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sanyth_portal</artifactId>
        <groupId>com.sanythPortal</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>portal_service</artifactId>
    <packaging>jar</packaging>

    <name>portal_service Maven Webapp</name>
    <!-- FIXME change it to the project's website -->
    <url>http://www.example.com</url>

    <dependencies>
        <dependency>
            <groupId>com.sanythPortal</groupId>
            <artifactId>portal_core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sanythPortal</groupId>
            <artifactId>portal_mapper</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sanythPortal</groupId>
            <artifactId>portal_utils</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.11</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-ldap</artifactId>
        </dependency>

    </dependencies>


</project>
